import json
import os

# 省份ID到行政代码的映射
province_map = {
    "beijing": "110000",
    "tianjin": "120000", 
    "hebei": "130000",
    "shanxi": "140000",
    "neimenggu": "150000",
    "liaoning": "210000",
    "jilin": "220000",
    "heilongjiang": "230000",
    "shanghai": "310000",
    "jiangsu": "320000",
    "zhejiang": "330000",
    "anhui": "340000",
    "fujian": "350000",
    "jiangxi": "360000",
    "shandong": "370000",
    "henan": "410000",
    "hubei": "420000",
    "hunan": "430000",
    "guangdong": "440000",
    "guangxi": "450000",
    "hainan": "460000",
    "chongqing": "500000",
    "sichuan": "510000",
    "guizhou": "520000",
    "yunnan": "530000",
    "xizang": "540000",
    "shaanxi": "610000",
    "gansu": "620000",
    "qinghai": "630000",
    "ningxia": "640000",
    "xinjiang": "650000",
    "taiwan": "710000",
    "hongkong": "810000",
    "macau": "820000"
}

def extract_province_data(code):
    file_path = f"geoJson数据包/100000/{code}.geoJson"
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data
        except Exception as e:
            print(f"Error reading {code}: {e}")
    return None

# 输出前10个省份的数据来做测试
test_provinces = list(province_map.items())[:10]

print("完整的详细省份地理数据（前10个省份）:")
for province_key, code in test_provinces:
    data = extract_province_data(code)
    if data:
        json_str = json.dumps(data, separators=(',', ':'))
        print(f'"{code}": """')
        print(json_str)
        print('""",')
        print()
    else:
        print(f'// No data found for {province_key} ({code})') 