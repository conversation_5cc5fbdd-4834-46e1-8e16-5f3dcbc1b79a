# 环球足迹 - 环球人生功能

## 功能概述

环球人生是环球足迹应用的核心功能之一，让用户可以可视化地展示和管理自己的旅行足迹，通过地图的方式点亮去过的地方。

## 主要功能

### 1. 地图展示
- 显示中国地图，用蓝色区域标记用户访问过的省份和城市
- 支持地图缩放和平移操作
- 实时显示统计信息

### 2. 统计信息
- **战胜用户百分比**：基于足迹覆盖率计算用户排名
- **地区覆盖率**：显示点亮中国和世界的百分比
- **详细统计**：
  - 已访问省份和城市数量
  - 总行程距离
  - 相当于绕地球圈数
  - 相当于横跨中国次数

### 3. 点亮方式
#### 自动点亮（照片GPS）
- 分析手机相册中带有GPS信息的照片
- 自动识别照片拍摄地点并点亮对应城市
- 用浅绿色标记表示照片GPS来源

#### 手动点亮
- 用户可以手动选择城市进行点亮
- 支持按大洲、国家、省份分类选择
- 用浅蓝色标记表示手动标记

### 4. 城市选择
- **中国**：按省份分组，显示所有地级市
- **其他大洲**：亚洲、欧洲、北美洲、南美洲、非洲
- 支持省份级别和城市级别的点亮
- 显示每个地区的访问状态和来源

### 5. 年度报告
- **主要成就**：探索国家数、点亮城市数、旅行天数等
- **距离统计**：总行程、地球圈数对比、中国跨度对比
- **地区覆盖**：世界和中国覆盖率进度条
- **照片统计**：总照片数、GPS照片数、手动标记数
- **排名统计**：用户排名和计算说明

## 技术实现

### 数据模型
- `GlobalLifeModels.swift`：定义地区、城市、统计信息等数据结构
- 支持省份、城市、国家的层级关系
- 区分访问来源（照片GPS vs 手动标记）

### 数据管理
- `GlobalLifeDataManager.swift`：单例模式管理所有地区数据
- 包含中国34个省级行政区和主要城市
- 支持世界主要国家和城市数据
- 数据持久化到UserDefaults

### 界面组件
- `GlobalLifeViewController.swift`：主页面，显示地图和统计信息
- `CitySelectionViewController.swift`：城市选择页面
- `AnnualReportViewController.swift`：年度报告页面

### 地图功能
- 使用MapKit显示地图
- 自定义标注显示访问过的城市
- 根据访问来源使用不同颜色标记
- 支持地图区域覆盖层显示

### 照片分析
- 使用Photos框架访问相册
- 分析照片GPS信息
- 自动匹配最近的城市（50公里范围内）
- 批量处理和进度显示

## 使用说明

1. **查看足迹**：打开应用，默认显示环球人生页面，可以看到当前的足迹地图和统计信息

2. **自动点亮**：
   - 点击"点亮足迹地图"按钮
   - 选择"从照片GPS自动点亮"
   - 授权访问相册后自动分析并点亮城市

3. **手动点亮**：
   - 点击右上角"选择城市"或"点亮足迹地图" → "手动选择城市"
   - 选择大洲/国家/省份
   - 点击具体城市或省份进行点亮

4. **查看报告**：
   - 点击"点亮足迹地图" → "查看年度报告"
   - 浏览详细的统计信息和成就

5. **管理标记**：
   - 点击地图上的城市标记可以查看详情
   - 可以取消已点亮的城市

## 数据说明

- 所有数据保存在本地，保护用户隐私
- 支持中国34个省级行政区和300+地级市
- 包含世界主要国家和城市数据
- 统计算法基于覆盖率和距离计算

## 未来扩展

- 支持更多国家和城市数据
- 添加真实的地理边界数据
- 支持轨迹数据导入
- 社交分享功能
- 更丰富的统计维度 