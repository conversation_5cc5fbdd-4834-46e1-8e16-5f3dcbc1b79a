import json
import os

# 省份ID到行政代码的映射
province_map = {
    "beijing": "110000",
    "tianjin": "120000", 
    "hebei": "130000",
    "shanxi": "140000",
    "neimenggu": "150000",
    "liaoning": "210000",
    "jilin": "220000",
    "heilongjiang": "230000",
    "shanghai": "310000",
    "jiangsu": "320000",
    "zhejiang": "330000",
    "anhui": "340000",
    "fujian": "350000",
    "jiangxi": "360000",
    "shandong": "370000",
    "henan": "410000",
    "hubei": "420000",
    "hunan": "430000",
    "guangdong": "440000",
    "guangxi": "450000",
    "hainan": "460000",
    "chongqing": "500000",
    "sichuan": "510000",
    "guizhou": "520000",
    "yunnan": "530000",
    "xizang": "540000",
    "shaanxi": "610000",
    "gansu": "620000",
    "qinghai": "630000",
    "ningxia": "640000",
    "xinjiang": "650000",
    "taiwan": "710000",
    "hongkong": "810000",
    "macau": "820000"
}

def extract_province_data(code):
    file_path = f"globalstep/geoJsonData/100000/{code}.geoJson"
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # 简化数据，只取第一个feature的geometry
                if data.get('features') and len(data['features']) > 0:
                    first_feature = data['features'][0]
                    geometry = first_feature.get('geometry')
                    if geometry:
                        return {
                            "type": "FeatureCollection",
                            "features": [
                                {
                                    "type": "Feature", 
                                    "geometry": geometry
                                }
                            ]
                        }
        except Exception as e:
            print(f"Error reading {code}: {e}")
    return None

# 提取前5个省份的真实数据
target_provinces = ["beijing", "tianjin", "hebei", "shanxi", "guangdong"]

for province in target_provinces:
    code = province_map[province]
    data = extract_province_data(code)
    if data:
        print(f'"{code}": """')
        print(json.dumps(data, separators=(',', ':')))
        print('""",')
    else:
        print(f"No data found for {province} ({code})") 