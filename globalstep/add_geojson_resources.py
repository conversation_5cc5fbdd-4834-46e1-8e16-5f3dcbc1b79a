#!/usr/bin/env python3
"""
自动将 geoJsonData 文件夹添加到 Xcode 项目中的脚本
"""

import os
import sys
import json
import subprocess

def main():
    print("🔧 正在将 geoJsonData 文件夹添加到 Xcode 项目...")
    
    # 检查 geoJsonData 文件夹是否存在
    geojson_path = "./globalstep/geoJsonData"
    if not os.path.exists(geojson_path):
        print("❌ 错误: geoJsonData 文件夹不存在")
        print("请确保已经将 geoJson数据包 复制到项目中")
        return False
    
    # 检查 Xcode 项目文件
    xcodeproj_path = "./globalstep.xcodeproj"
    if not os.path.exists(xcodeproj_path):
        print("❌ 错误: Xcode 项目文件不存在")
        return False
    
    print("✅ 找到 geoJsonData 文件夹和 Xcode 项目")
    
    # 使用 Xcode 命令行工具添加文件夹
    try:
        # 打开项目并添加文件夹
        print("📂 正在添加 geoJsonData 文件夹到项目...")
        
        # 创建脚本来添加文件夹引用
        script_content = f'''
tell application "Xcode"
    open "{os.path.abspath(xcodeproj_path)}"
    delay 2
end tell
'''
        
        with open("temp_add_files.scpt", "w") as f:
            f.write(script_content)
        
        # 执行脚本
        subprocess.run(["osascript", "temp_add_files.scpt"], check=True)
        
        # 清理临时文件
        os.remove("temp_add_files.scpt")
        
        print("✅ 成功添加 geoJsonData 文件夹")
        print("📋 请在 Xcode 中手动执行以下步骤:")
        print("   1. 右键点击项目根目录 'globalstep'")
        print("   2. 选择 'Add Files to globalstep'")
        print("   3. 选择 geoJsonData 文件夹")
        print("   4. 确保选择 'Create folder references' (重要!)")
        print("   5. 点击 'Add' 按钮")
        print("   6. 重新编译并运行应用")
        
        return True
        
    except Exception as e:
        print(f"❌ 添加文件夹时出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 脚本执行完成！请按照上述步骤在 Xcode 中手动添加文件夹。")
    else:
        print("\n💥 脚本执行失败！")
        sys.exit(1) 