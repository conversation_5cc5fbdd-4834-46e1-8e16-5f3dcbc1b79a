import Foundation
import UIKit

// MARK: - 通知名称
extension Notification.Name {
    static let tripDataUpdated = Notification.Name("tripDataUpdated")
}

// MARK: - 行程状态枚举
enum TripStatus: String, Codable {
    case planning = "规划中"
    case ongoing = "进行中"  
    case completed = "已结束"
}

// MARK: - 行程记录模型
struct TripRecord: Codable {
    let id: String
    let title: String
    let destination: String
    let departure: String
    let startDate: Date
    let endDate: Date
    let dayCount: Int
    let attractions: [String]
    let currentDay: Int
    let status: TripStatus
    let createdDate: Date
    let lastUpdated: Date
    
    // 计算属性
    var progress: String {
        switch status {
        case .planning:
            return "规划中"
        case .ongoing:
            return "第\(currentDay)天/共\(dayCount)天"
        case .completed:
            return "已完成"
        }
    }
    
    var formattedDuration: String {
        return "\(dayCount)天"
    }
    
    var formattedLocations: String {
        return "\(attractions.count)个地点"
    }
    
    var statusColor: UIColor {
        switch status {
        case .planning:
            return .systemOrange
        case .ongoing:
            return .systemGreen
        case .completed:
            return .systemBlue
        }
    }
}

// MARK: - 行程计划模型
struct TripPlan {
    let title: String
    let description: String
    let dayCount: Int
    let attractionCount: Int
    let attractions: [String]
    let dailyPlans: [TripDailyPlan]?
}

struct TripDailyPlan {
    let day: Int
    let title: String
    let activities: [TripActivity]
}

struct TripActivity {
    let time: String
    let activity: String
    let location: String
    let duration: String
    let cost: String
    let tips: String
}

// MARK: - 行程数据管理器
class TripDataManager {
    static let shared = TripDataManager()
    
    private var trips: [TripRecord] = []
    private let userDefaults = UserDefaults.standard
    private let tripsKey = "SavedTrips"
    
    private init() {
        loadTrips()
    }
    
    func saveTrip(_ trip: TripRecord) {
        // 检查是否已存在
        if let index = trips.firstIndex(where: { $0.id == trip.id }) {
            trips[index] = trip
        } else {
            trips.append(trip)
        }
        saveTrips()
        
        // 发送通知告知数据更新
        NotificationCenter.default.post(name: .tripDataUpdated, object: nil)
    }
    
    func getAllTrips() -> [TripRecord] {
        return trips.sorted { $0.lastUpdated > $1.lastUpdated }
    }
    
    func getTrip(withId id: String) -> TripRecord? {
        return trips.first { $0.id == id }
    }
    
    func deleteTrip(withId id: String) {
        trips.removeAll { $0.id == id }
        saveTrips()
        
        // 发送通知告知数据更新
        NotificationCenter.default.post(name: .tripDataUpdated, object: nil)
    }
    
    func updateTripStatus(_ tripId: String, newStatus: TripStatus, currentDay: Int? = nil) {
        if let index = trips.firstIndex(where: { $0.id == tripId }) {
            var updatedTrip = trips[index]
            trips[index] = TripRecord(
                id: updatedTrip.id,
                title: updatedTrip.title,
                destination: updatedTrip.destination,
                departure: updatedTrip.departure,
                startDate: updatedTrip.startDate,
                endDate: updatedTrip.endDate,
                dayCount: updatedTrip.dayCount,
                attractions: updatedTrip.attractions,
                currentDay: currentDay ?? updatedTrip.currentDay,
                status: newStatus,
                createdDate: updatedTrip.createdDate,
                lastUpdated: Date()
            )
            saveTrips()
            
            // 发送通知告知数据更新
            NotificationCenter.default.post(name: .tripDataUpdated, object: nil)
        }
    }
    
    private func loadTrips() {
        if let data = userDefaults.data(forKey: tripsKey),
           let savedTrips = try? JSONDecoder().decode([TripRecord].self, from: data) {
            trips = savedTrips
        }
    }
    
    private func saveTrips() {
        if let data = try? JSONEncoder().encode(trips) {
            userDefaults.set(data, forKey: tripsKey)
        }
    }
} 