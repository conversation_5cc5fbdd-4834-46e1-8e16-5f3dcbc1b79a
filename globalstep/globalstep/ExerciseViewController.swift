import UIKit
import CoreLocation

class ExerciseViewController: UIViewController {
    
    // MARK: - Properties
    private let dataManager = ExerciseDataManager.shared
    private var exerciseStats: ExerciseStats?
    private var recentRecords: [ExerciseRecord] = []
    private var activeGoals: [ExerciseGoal] = []
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    // 头部统计卡片
    private let statsCardView = UIView()
    private let todayStatsLabel = UILabel()
    private let weekStatsLabel = UILabel()
    private let monthStatsLabel = UILabel()
    
    // 快速开始运动
    private let quickStartView = UIView()
    private let quickStartTitleLabel = UILabel()
    private let exerciseTypesStackView = UIStackView()
    
    // 运动目标
    private let goalsView = UIView()
    private let goalsTitleLabel = UILabel()
    private let goalsStackView = UIStackView()
    
    // 最近记录
    private let recentRecordsView = UIView()
    private let recentRecordsTitleLabel = UILabel()
    private let recentRecordsTableView = UITableView()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
        
        // 添加测试数据
        dataManager.addTestData()
        loadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        refreshData()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "运动"
        view.backgroundColor = .systemBackground
        
        // 导航栏按钮
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "chart.bar"),
            style: .plain,
            target: self,
            action: #selector(showStatsTapped)
        )
        
        setupScrollView()
        setupStatsCard()
        setupQuickStart()
        setupGoals()
        setupRecentRecords()
        setupConstraints()
    }
    
    private func setupScrollView() {
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
    }
    
    private func setupStatsCard() {
        statsCardView.backgroundColor = .systemBlue
        statsCardView.layer.cornerRadius = 12
        statsCardView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(statsCardView)
        
        // 今日统计
        todayStatsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        todayStatsLabel.textColor = .white
        todayStatsLabel.textAlignment = .center
        todayStatsLabel.numberOfLines = 0
        todayStatsLabel.translatesAutoresizingMaskIntoConstraints = false
        statsCardView.addSubview(todayStatsLabel)
        
        // 本周统计
        weekStatsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        weekStatsLabel.textColor = .white
        weekStatsLabel.textAlignment = .center
        weekStatsLabel.numberOfLines = 0
        weekStatsLabel.translatesAutoresizingMaskIntoConstraints = false
        statsCardView.addSubview(weekStatsLabel)
        
        // 本月统计
        monthStatsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        monthStatsLabel.textColor = .white
        monthStatsLabel.textAlignment = .center
        monthStatsLabel.numberOfLines = 0
        monthStatsLabel.translatesAutoresizingMaskIntoConstraints = false
        statsCardView.addSubview(monthStatsLabel)
    }
    
    private func setupQuickStart() {
        quickStartView.backgroundColor = .secondarySystemBackground
        quickStartView.layer.cornerRadius = 12
        quickStartView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(quickStartView)
        
        quickStartTitleLabel.text = "快速开始运动"
        quickStartTitleLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        quickStartTitleLabel.translatesAutoresizingMaskIntoConstraints = false
        quickStartView.addSubview(quickStartTitleLabel)
        
        exerciseTypesStackView.axis = .vertical
        exerciseTypesStackView.spacing = 12
        exerciseTypesStackView.translatesAutoresizingMaskIntoConstraints = false
        quickStartView.addSubview(exerciseTypesStackView)
        
        // 创建运动类型按钮
        let exerciseTypes: [ExerciseType] = [.running, .walking, .cycling, .hiking, .swimming, .basketball, .football, .tennis, .badminton, .yoga, .fitness, .other]
        
        for exerciseType in exerciseTypes {
            let button = createExerciseTypeButton(for: exerciseType)
            exerciseTypesStackView.addArrangedSubview(button)
        }
        
        NSLayoutConstraint.activate([
            quickStartTitleLabel.topAnchor.constraint(equalTo: quickStartView.topAnchor, constant: 15),
            quickStartTitleLabel.leadingAnchor.constraint(equalTo: quickStartView.leadingAnchor, constant: 20),
            quickStartTitleLabel.trailingAnchor.constraint(equalTo: quickStartView.trailingAnchor, constant: -20),
            
            exerciseTypesStackView.topAnchor.constraint(equalTo: quickStartTitleLabel.bottomAnchor, constant: 15),
            exerciseTypesStackView.leadingAnchor.constraint(equalTo: quickStartView.leadingAnchor, constant: 20),
            exerciseTypesStackView.trailingAnchor.constraint(equalTo: quickStartView.trailingAnchor, constant: -20),
            exerciseTypesStackView.bottomAnchor.constraint(equalTo: quickStartView.bottomAnchor, constant: -15)
        ])
    }
    
    private func createExerciseTypeButton(for exerciseType: ExerciseType) -> UIButton {
        let button = UIButton(type: .system)
        button.backgroundColor = .tertiarySystemBackground
        button.layer.cornerRadius = 12
        button.translatesAutoresizingMaskIntoConstraints = false
        
        // 创建按钮内容
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: exerciseType.icon)
        iconImageView.tintColor = exerciseType.color
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        
        let nameLabel = UILabel()
        nameLabel.text = exerciseType.displayName
        nameLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let descriptionLabel = UILabel()
        descriptionLabel.text = exerciseType.description
        descriptionLabel.font = UIFont.systemFont(ofSize: 12)
        descriptionLabel.textColor = .secondaryLabel
        descriptionLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let arrowImageView = UIImageView()
        arrowImageView.image = UIImage(systemName: "chevron.right")
        arrowImageView.tintColor = .secondaryLabel
        arrowImageView.contentMode = .scaleAspectFit
        arrowImageView.translatesAutoresizingMaskIntoConstraints = false
        
        button.addSubview(iconImageView)
        button.addSubview(nameLabel)
        button.addSubview(descriptionLabel)
        button.addSubview(arrowImageView)
        
        NSLayoutConstraint.activate([
            iconImageView.leadingAnchor.constraint(equalTo: button.leadingAnchor, constant: 15),
            iconImageView.centerYAnchor.constraint(equalTo: button.centerYAnchor),
            iconImageView.widthAnchor.constraint(equalToConstant: 32),
            iconImageView.heightAnchor.constraint(equalToConstant: 32),
            
            nameLabel.leadingAnchor.constraint(equalTo: iconImageView.trailingAnchor, constant: 15),
            nameLabel.topAnchor.constraint(equalTo: button.topAnchor, constant: 12),
            
            descriptionLabel.leadingAnchor.constraint(equalTo: nameLabel.leadingAnchor),
            descriptionLabel.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 2),
            descriptionLabel.bottomAnchor.constraint(equalTo: button.bottomAnchor, constant: -12),
            
            arrowImageView.trailingAnchor.constraint(equalTo: button.trailingAnchor, constant: -15),
            arrowImageView.centerYAnchor.constraint(equalTo: button.centerYAnchor),
            arrowImageView.widthAnchor.constraint(equalToConstant: 12),
            arrowImageView.heightAnchor.constraint(equalToConstant: 12),
            arrowImageView.leadingAnchor.constraint(greaterThanOrEqualTo: nameLabel.trailingAnchor, constant: 10),
            
            button.heightAnchor.constraint(equalToConstant: 60)
        ])
        
        // 添加点击事件
        button.addTarget(self, action: #selector(exerciseTypeButtonTapped(_:)), for: .touchUpInside)
        button.tag = exerciseType.hashValue
        
        return button
    }
    
    private func setupGoals() {
        goalsView.backgroundColor = .secondarySystemBackground
        goalsView.layer.cornerRadius = 12
        goalsView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(goalsView)
        
        goalsTitleLabel.text = "运动目标"
        goalsTitleLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        goalsTitleLabel.translatesAutoresizingMaskIntoConstraints = false
        goalsView.addSubview(goalsTitleLabel)
        
        goalsStackView.axis = .vertical
        goalsStackView.spacing = 10
        goalsStackView.translatesAutoresizingMaskIntoConstraints = false
        goalsView.addSubview(goalsStackView)
    }
    
    private func setupRecentRecords() {
        recentRecordsView.backgroundColor = .secondarySystemBackground
        recentRecordsView.layer.cornerRadius = 12
        recentRecordsView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(recentRecordsView)
        
        recentRecordsTitleLabel.text = "最近记录"
        recentRecordsTitleLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        recentRecordsTitleLabel.translatesAutoresizingMaskIntoConstraints = false
        recentRecordsView.addSubview(recentRecordsTitleLabel)
        
        recentRecordsTableView.delegate = self
        recentRecordsTableView.dataSource = self
        recentRecordsTableView.register(ExerciseRecordTableViewCell.self, forCellReuseIdentifier: "ExerciseRecordCell")
        recentRecordsTableView.backgroundColor = .clear
        recentRecordsTableView.separatorStyle = .none
        recentRecordsTableView.isScrollEnabled = false
        recentRecordsTableView.translatesAutoresizingMaskIntoConstraints = false
        recentRecordsView.addSubview(recentRecordsTableView)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 滚动视图
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            // 统计卡片
            statsCardView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            statsCardView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            statsCardView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            statsCardView.heightAnchor.constraint(equalToConstant: 100),
            
            todayStatsLabel.leadingAnchor.constraint(equalTo: statsCardView.leadingAnchor, constant: 15),
            todayStatsLabel.centerYAnchor.constraint(equalTo: statsCardView.centerYAnchor),
            todayStatsLabel.widthAnchor.constraint(equalTo: statsCardView.widthAnchor, multiplier: 0.3),
            
            weekStatsLabel.centerXAnchor.constraint(equalTo: statsCardView.centerXAnchor),
            weekStatsLabel.centerYAnchor.constraint(equalTo: statsCardView.centerYAnchor),
            weekStatsLabel.widthAnchor.constraint(equalTo: statsCardView.widthAnchor, multiplier: 0.3),
            
            monthStatsLabel.trailingAnchor.constraint(equalTo: statsCardView.trailingAnchor, constant: -15),
            monthStatsLabel.centerYAnchor.constraint(equalTo: statsCardView.centerYAnchor),
            monthStatsLabel.widthAnchor.constraint(equalTo: statsCardView.widthAnchor, multiplier: 0.3),
            
            // 快速开始
            quickStartView.topAnchor.constraint(equalTo: statsCardView.bottomAnchor, constant: 20),
            quickStartView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            quickStartView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            
            quickStartTitleLabel.topAnchor.constraint(equalTo: quickStartView.topAnchor, constant: 15),
            quickStartTitleLabel.leadingAnchor.constraint(equalTo: quickStartView.leadingAnchor, constant: 20),
            quickStartTitleLabel.trailingAnchor.constraint(equalTo: quickStartView.trailingAnchor, constant: -20),
            
            exerciseTypesStackView.topAnchor.constraint(equalTo: quickStartTitleLabel.bottomAnchor, constant: 15),
            exerciseTypesStackView.leadingAnchor.constraint(equalTo: quickStartView.leadingAnchor, constant: 20),
            exerciseTypesStackView.trailingAnchor.constraint(equalTo: quickStartView.trailingAnchor, constant: -20),
            exerciseTypesStackView.bottomAnchor.constraint(equalTo: quickStartView.bottomAnchor, constant: -15),
            
            // 运动目标
            goalsView.topAnchor.constraint(equalTo: quickStartView.bottomAnchor, constant: 20),
            goalsView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            goalsView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            
            goalsTitleLabel.topAnchor.constraint(equalTo: goalsView.topAnchor, constant: 15),
            goalsTitleLabel.leadingAnchor.constraint(equalTo: goalsView.leadingAnchor, constant: 15),
            goalsTitleLabel.trailingAnchor.constraint(equalTo: goalsView.trailingAnchor, constant: -15),
            
            goalsStackView.topAnchor.constraint(equalTo: goalsTitleLabel.bottomAnchor, constant: 15),
            goalsStackView.leadingAnchor.constraint(equalTo: goalsView.leadingAnchor, constant: 15),
            goalsStackView.trailingAnchor.constraint(equalTo: goalsView.trailingAnchor, constant: -15),
            goalsStackView.bottomAnchor.constraint(equalTo: goalsView.bottomAnchor, constant: -15),
            
            // 最近记录
            recentRecordsView.topAnchor.constraint(equalTo: goalsView.bottomAnchor, constant: 20),
            recentRecordsView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            recentRecordsView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            recentRecordsView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20),
            
            recentRecordsTitleLabel.topAnchor.constraint(equalTo: recentRecordsView.topAnchor, constant: 15),
            recentRecordsTitleLabel.leadingAnchor.constraint(equalTo: recentRecordsView.leadingAnchor, constant: 15),
            recentRecordsTitleLabel.trailingAnchor.constraint(equalTo: recentRecordsView.trailingAnchor, constant: -15),
            
            recentRecordsTableView.topAnchor.constraint(equalTo: recentRecordsTitleLabel.bottomAnchor, constant: 15),
            recentRecordsTableView.leadingAnchor.constraint(equalTo: recentRecordsView.leadingAnchor),
            recentRecordsTableView.trailingAnchor.constraint(equalTo: recentRecordsView.trailingAnchor),
            recentRecordsTableView.bottomAnchor.constraint(equalTo: recentRecordsView.bottomAnchor, constant: -15),
            recentRecordsTableView.heightAnchor.constraint(equalToConstant: 300) // 固定高度
        ])
    }
    
    // MARK: - Helper Methods
    private func createGoalProgressView(goal: ExerciseGoal) -> UIView {
        let container = UIView()
        container.backgroundColor = .tertiarySystemBackground
        container.layer.cornerRadius = 8
        
        let titleLabel = UILabel()
        titleLabel.text = "\(goal.period.displayName)\(goal.type.displayName)"
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(titleLabel)
        
        let progressLabel = UILabel()
        progressLabel.text = "\(String(format: "%.1f", goal.currentValue))/\(String(format: "%.0f", goal.targetValue)) \(goal.type.unit)"
        progressLabel.font = UIFont.systemFont(ofSize: 12)
        progressLabel.textColor = .secondaryLabel
        progressLabel.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(progressLabel)
        
        let progressView = UIProgressView(progressViewStyle: .default)
        progressView.progress = Float(goal.progress)
        progressView.progressTintColor = goal.isCompleted ? .systemGreen : .systemBlue
        progressView.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(progressView)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor, constant: 10),
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 15),
            titleLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -15),
            
            progressLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 5),
            progressLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 15),
            progressLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -15),
            
            progressView.topAnchor.constraint(equalTo: progressLabel.bottomAnchor, constant: 8),
            progressView.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 15),
            progressView.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -15),
            progressView.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -10),
            
            container.heightAnchor.constraint(equalToConstant: 70)
        ])
        
        return container
    }
    
    // MARK: - Data Loading
    private func loadData() {
        exerciseStats = dataManager.getExerciseStats()
        recentRecords = dataManager.getRecentExerciseRecords(limit: 5)
        activeGoals = dataManager.getActiveGoals()
        
        updateUI()
    }
    
    private func refreshData() {
        loadData()
    }
    
    private func updateUI() {
        updateStatsCard()
        updateGoals()
        recentRecordsTableView.reloadData()
    }
    
    private func updateStatsCard() {
        guard let stats = exerciseStats else { return }
        
        // 今日统计（简化显示）
        let todayDistance = stats.thisWeekStats.totalDistance / 7 / 1000 // 平均每日距离
        todayStatsLabel.text = "今日\n\(String(format: "%.1f", todayDistance)) 公里"
        
        // 本周统计
        let weekDistance = stats.thisWeekStats.totalDistance / 1000
        weekStatsLabel.text = "本周\n\(String(format: "%.1f", weekDistance)) 公里"
        
        // 本月统计
        let monthDistance = stats.thisMonthStats.totalDistance / 1000
        monthStatsLabel.text = "本月\n\(String(format: "%.1f", monthDistance)) 公里"
    }
    
    private func updateGoals() {
        // 清除现有目标视图
        goalsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        if activeGoals.isEmpty {
            let noGoalsLabel = UILabel()
            noGoalsLabel.text = "暂无运动目标，点击设置目标"
            noGoalsLabel.font = UIFont.systemFont(ofSize: 14)
            noGoalsLabel.textColor = .secondaryLabel
            noGoalsLabel.textAlignment = .center
            goalsStackView.addArrangedSubview(noGoalsLabel)
        } else {
            for goal in activeGoals {
                let goalView = createGoalProgressView(goal: goal)
                goalsStackView.addArrangedSubview(goalView)
            }
        }
    }
    
    // MARK: - Actions
    @objc private func exerciseTypeButtonTapped(_ sender: UIButton) {
        // 根据tag获取运动类型
        let exerciseTypes: [ExerciseType] = [.running, .walking, .cycling, .hiking, .swimming, .basketball, .football, .tennis, .badminton, .yoga, .fitness, .other]
        
        guard let exerciseType = exerciseTypes.first(where: { $0.hashValue == sender.tag }) else { return }
        
        // 根据运动类型决定使用哪种记录方式
        if exerciseType.requiresGPS {
            // 需要GPS的运动，使用简洁的轨迹追踪界面
            let trackingVC = SimpleExerciseTrackingViewController()
            trackingVC.modalPresentationStyle = .fullScreen
            present(trackingVC, animated: true)
        } else {
            // 不需要GPS的运动，使用手动记录
            let manualVC = ManualExerciseViewController()
            manualVC.exerciseType = exerciseType
            let navController = UINavigationController(rootViewController: manualVC)
            navController.modalPresentationStyle = .fullScreen
            present(navController, animated: true)
        }
    }
    
    @objc private func showStatsTapped() {
        let statsVC = ExerciseStatsViewController()
        statsVC.exerciseStats = exerciseStats
        let navController = UINavigationController(rootViewController: statsVC)
        present(navController, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension ExerciseViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return recentRecords.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "ExerciseRecordCell", for: indexPath) as! ExerciseRecordTableViewCell
        cell.configure(with: recentRecords[indexPath.row])
        return cell
    }
}

// MARK: - UITableViewDelegate
extension ExerciseViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let record = recentRecords[indexPath.row]
        let detailVC = ExerciseDetailViewController()
        detailVC.exerciseRecord = record
        navigationController?.pushViewController(detailVC, animated: true)
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 60
    }
}

// MARK: - Custom Cell
class ExerciseRecordTableViewCell: UITableViewCell {
    
    private let typeIconImageView = UIImageView()
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    private let valueLabel = UILabel()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        
        typeIconImageView.contentMode = .scaleAspectFit
        typeIconImageView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(typeIconImageView)
        
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(titleLabel)
        
        subtitleLabel.font = UIFont.systemFont(ofSize: 12)
        subtitleLabel.textColor = .secondaryLabel
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(subtitleLabel)
        
        valueLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        valueLabel.textAlignment = .right
        valueLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(valueLabel)
        
        NSLayoutConstraint.activate([
            typeIconImageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 15),
            typeIconImageView.centerYAnchor.constraint(equalTo: contentView.centerYAnchor),
            typeIconImageView.widthAnchor.constraint(equalToConstant: 30),
            typeIconImageView.heightAnchor.constraint(equalToConstant: 30),
            
            titleLabel.leadingAnchor.constraint(equalTo: typeIconImageView.trailingAnchor, constant: 15),
            titleLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            titleLabel.trailingAnchor.constraint(equalTo: valueLabel.leadingAnchor, constant: -10),
            
            subtitleLabel.leadingAnchor.constraint(equalTo: titleLabel.leadingAnchor),
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 2),
            subtitleLabel.trailingAnchor.constraint(equalTo: titleLabel.trailingAnchor),
            subtitleLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),
            
            valueLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -15),
            valueLabel.centerYAnchor.constraint(equalTo: contentView.centerYAnchor),
            valueLabel.widthAnchor.constraint(equalToConstant: 80)
        ])
    }
    
    func configure(with record: ExerciseRecord) {
        typeIconImageView.image = UIImage(systemName: record.type.icon)
        typeIconImageView.tintColor = record.type.color
        
        titleLabel.text = record.type.displayName
        
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        subtitleLabel.text = formatter.string(from: record.startTime)
        
        valueLabel.text = record.formattedDistance
        valueLabel.textColor = record.type.color
    }
} 