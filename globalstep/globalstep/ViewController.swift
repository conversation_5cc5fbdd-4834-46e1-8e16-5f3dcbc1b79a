//
//  ViewController.swift
//  globalstep
//
//  Created by yy on 2025/5/18.
//

import UIKit
import MapKit

class ViewController: UITabBarController {

    override func viewDidLoad() {
        super.viewDidLoad()
        setupTabBar()
    }
    
    private func setupTabBar() {
        // Tab 1: 首页 (Home)
        let homeVC = HomeViewController()
        let homeNav = UINavigationController(rootViewController: homeVC)
        homeNav.tabBarItem = UITabBarItem(title: "首页", image: UIImage(systemName: "house"), tag: 0)
        
        // Tab 2: 环球人生 (Global Life) - 地图点亮页面
        let globalLifeVC = GlobalLifeViewController()
        let globalLifeNav = UINavigationController(rootViewController: globalLifeVC)
        globalLifeNav.tabBarItem = UITabBarItem(title: "环球人生", image: UIImage(systemName: "map"), tag: 1)
        
        // Tab 3: 轨迹动画 (Trajectory Animation)
        let trajectoryListVC = TrajectoryListViewController()
        let trajectoryNav = UINavigationController(rootViewController: trajectoryListVC)
        trajectoryNav.tabBarItem = UITabBarItem(title: "轨迹", image: UIImage(systemName: "location.fill"), tag: 2)
        
        // Tab 4: 时光相册 (Photo Album)
        let photoAlbumVC = MapCheckInViewController()
        let photoAlbumNav = UINavigationController(rootViewController: photoAlbumVC)
        photoAlbumNav.tabBarItem = UITabBarItem(title: "时光相册", image: UIImage(systemName: "photo"), tag: 3)
        
        // Tab 5: 运动 (Exercise)
        let exerciseVC = SimpleExerciseTrackingViewController()
        let exerciseNav = UINavigationController(rootViewController: exerciseVC)
        exerciseNav.tabBarItem = UITabBarItem(title: "运动", image: UIImage(systemName: "figure.walk"), tag: 4)
        
        // Set the view controllers
        viewControllers = [homeNav, globalLifeNav, trajectoryNav, photoAlbumNav, exerciseNav]
        
        // Customize tab bar appearance
        tabBar.tintColor = UIColor.systemGreen
        tabBar.backgroundColor = UIColor.systemBackground
        
        // Set the default selected tab
        selectedIndex = 0 // Start with home tab
    }
}
