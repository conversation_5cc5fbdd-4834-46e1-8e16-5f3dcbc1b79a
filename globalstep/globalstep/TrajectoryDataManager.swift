import Foundation
import CoreLocation
import MapKit
import AVFoundation

class TrajectoryDataManager {
    
    // MARK: - Properties
    static let shared = TrajectoryDataManager()
    
    private var trajectoryData: [TrajectoryRecord] = []
    private let fileManager = FileManager.default
    private let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
    
    // MARK: - Initialization
    private init() {
        loadSavedTrajectories()
    }
    
    // MARK: - Data Management
    func saveTrajectory(_ trajectory: TrajectoryRecord) {
        trajectoryData.append(trajectory)
        saveTrajectoriesToDisk()
    }
    
    func getAllTrajectories() -> [TrajectoryRecord] {
        return trajectoryData
    }
    
    func getTrajectory(withId id: String) -> TrajectoryRecord? {
        return trajectoryData.first { $0.id == id }
    }
    
    func deleteTrajectory(withId id: String) {
        trajectoryData.removeAll { $0.id == id }
        saveTrajectoriesToDisk()
    }
    
    // MARK: - Persistence
    private func saveTrajectoriesToDisk() {
        let encoder = JSONEncoder()
        do {
            let data = try encoder.encode(trajectoryData)
            let fileURL = documentsDirectory.appendingPathComponent("trajectories.json")
            try data.write(to: fileURL)
        } catch {
            print("Error saving trajectories: \(error.localizedDescription)")
        }
    }
    
    private func loadSavedTrajectories() {
        let fileURL = documentsDirectory.appendingPathComponent("trajectories.json")
        
        guard fileManager.fileExists(atPath: fileURL.path) else {
            // No saved data yet
            return
        }
        
        do {
            let data = try Data(contentsOf: fileURL)
            let decoder = JSONDecoder()
            trajectoryData = try decoder.decode([TrajectoryRecord].self, from: data)
        } catch {
            print("Error loading trajectories: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Video Generation
    func generateVideo(from trajectory: TrajectoryRecord, 
                      settings: VideoGenerationSettings,
                      progressHandler: @escaping (Float) -> Void,
                      completionHandler: @escaping (URL?, Error?) -> Void) {
        
        // Create a dispatch queue for video generation
        let videoQueue = DispatchQueue(label: "com.globaltrajectory.videoGeneration")
        
        videoQueue.async {
            // Setup video writer
            let videoURL = self.documentsDirectory.appendingPathComponent("trajectory_\(trajectory.id)_\(Int(Date().timeIntervalSince1970)).mp4")
            
            // Remove existing file if needed
            if self.fileManager.fileExists(atPath: videoURL.path) {
                try? self.fileManager.removeItem(at: videoURL)
            }
            
            do {
                // Create asset writer
                let assetWriter = try AVAssetWriter(outputURL: videoURL, fileType: .mp4)
                
                // Video settings
                let videoSettings: [String: Any] = [
                    AVVideoCodecKey: AVVideoCodecType.h264,
                    AVVideoWidthKey: settings.resolution.width,
                    AVVideoHeightKey: settings.resolution.height,
                    AVVideoCompressionPropertiesKey: [
                        AVVideoAverageBitRateKey: settings.bitRate,
                        AVVideoProfileLevelKey: AVVideoProfileLevelH264HighAutoLevel
                    ]
                ]
                
                // Create writer input
                let writerInput = AVAssetWriterInput(mediaType: .video, outputSettings: videoSettings)
                writerInput.expectsMediaDataInRealTime = false
                
                // Create pixel buffer adaptor
                let sourcePixelBufferAttributes: [String: Any] = [
                    kCVPixelBufferPixelFormatTypeKey as String: Int(kCVPixelFormatType_32ARGB),
                    kCVPixelBufferWidthKey as String: settings.resolution.width,
                    kCVPixelBufferHeightKey as String: settings.resolution.height
                ]
                
                let pixelBufferAdaptor = AVAssetWriterInputPixelBufferAdaptor(
                    assetWriterInput: writerInput,
                    sourcePixelBufferAttributes: sourcePixelBufferAttributes
                )
                
                if assetWriter.canAdd(writerInput) {
                    assetWriter.add(writerInput)
                }
                
                assetWriter.startWriting()
                assetWriter.startSession(atSourceTime: CMTime.zero)
                
                // Generate frames for the video
                let frameCount = min(trajectory.points.count, settings.maxFrameCount)
                var frameIndex = 0
                
                // Determine frame duration based on video length
                let frameDuration = CMTimeMake(value: Int64(settings.videoDuration * 600 / Double(frameCount)), timescale: 600)
                
                while frameIndex < frameCount && writerInput.isReadyForMoreMediaData {
                    // Calculate progress
                    let progress = Float(frameIndex) / Float(frameCount)
                    DispatchQueue.main.async {
                        progressHandler(progress)
                    }
                    
                    // Create a frame for the current trajectory point
                    if let pixelBuffer = self.createFramePixelBuffer(
                        forTrajectory: trajectory,
                        atIndex: frameIndex,
                        totalPoints: frameCount,
                        settings: settings
                    ) {
                        // Calculate presentation time
                        let presentationTime = CMTimeMultiply(frameDuration, multiplier: Int32(frameIndex))
                        
                        // Append the frame to the video
                        pixelBufferAdaptor.append(pixelBuffer, withPresentationTime: presentationTime)
                        
                        frameIndex += 1
                    }
                }
                
                // Finalize the video
                writerInput.markAsFinished()
                
                assetWriter.finishWriting {
                    DispatchQueue.main.async {
                        if assetWriter.status == .completed {
                            completionHandler(videoURL, nil)
                        } else {
                            completionHandler(nil, assetWriter.error)
                        }
                    }
                }
                
            } catch {
                DispatchQueue.main.async {
                    completionHandler(nil, error)
                }
            }
        }
    }
    
    private func createFramePixelBuffer(
        forTrajectory trajectory: TrajectoryRecord,
        atIndex index: Int,
        totalPoints: Int,
        settings: VideoGenerationSettings
    ) -> CVPixelBuffer? {
        
        // Create a pixel buffer
        var pixelBuffer: CVPixelBuffer?
        let attrs = [
            kCVPixelBufferCGImageCompatibilityKey: kCFBooleanTrue,
            kCVPixelBufferCGBitmapContextCompatibilityKey: kCFBooleanTrue
        ] as CFDictionary
        
        CVPixelBufferCreate(
            kCFAllocatorDefault,
            Int(settings.resolution.width),
            Int(settings.resolution.height),
            kCVPixelFormatType_32ARGB,
            attrs,
            &pixelBuffer
        )
        
        guard let pixelBuffer = pixelBuffer else { return nil }
        
        CVPixelBufferLockBaseAddress(pixelBuffer, CVPixelBufferLockFlags(rawValue: 0))
        
        // Get pixel buffer base address
        let pixelData = CVPixelBufferGetBaseAddress(pixelBuffer)
        let rgbColorSpace = CGColorSpaceCreateDeviceRGB()
        
        // Create context for drawing
        guard let context = CGContext(
            data: pixelData,
            width: Int(settings.resolution.width),
            height: Int(settings.resolution.height),
            bitsPerComponent: 8,
            bytesPerRow: CVPixelBufferGetBytesPerRow(pixelBuffer),
            space: rgbColorSpace,
            bitmapInfo: CGImageAlphaInfo.noneSkipFirst.rawValue
        ) else {
            CVPixelBufferUnlockBaseAddress(pixelBuffer, CVPixelBufferLockFlags(rawValue: 0))
            return nil
        }
        
        // Draw map background
        drawMapBackground(in: context, size: settings.resolution, mapType: settings.mapType)
        
        // Draw trajectory path
        let pointsToDraw = min(index + 1, trajectory.points.count)
        let pathPoints = Array(trajectory.points[0..<pointsToDraw])
        drawTrajectoryPath(in: context, points: pathPoints, size: settings.resolution, settings: settings)
        
        // Draw current position marker
        if index < trajectory.points.count {
            let currentPoint = trajectory.points[index]
            drawPositionMarker(in: context, at: currentPoint, size: settings.resolution, settings: settings)
        }
        
        // Draw additional information (time, distance, etc.)
        drawInformation(in: context, trajectory: trajectory, currentIndex: index, size: settings.resolution, settings: settings)
        
        CVPixelBufferUnlockBaseAddress(pixelBuffer, CVPixelBufferLockFlags(rawValue: 0))
        
        return pixelBuffer
    }
    
    private func drawMapBackground(in context: CGContext, size: CGSize, mapType: MKMapType) {
        // In a real implementation, this would render a map snapshot
        // For now, we'll just draw a simple gradient background
        
        let colors: [CGColor]
        
        switch mapType {
        case .standard:
            colors = [UIColor.white.cgColor, UIColor(white: 0.9, alpha: 1.0).cgColor]
        case .satellite:
            colors = [UIColor.darkGray.cgColor, UIColor.black.cgColor]
        case .hybrid:
            colors = [UIColor.darkGray.cgColor, UIColor(white: 0.2, alpha: 1.0).cgColor]
        default:
            colors = [UIColor.white.cgColor, UIColor(white: 0.9, alpha: 1.0).cgColor]
        }
        
        let gradient = CGGradient(
            colorsSpace: CGColorSpaceCreateDeviceRGB(),
            colors: colors as CFArray,
            locations: [0.0, 1.0]
        )!
        
        context.drawLinearGradient(
            gradient,
            start: CGPoint(x: 0, y: 0),
            end: CGPoint(x: 0, y: size.height),
            options: []
        )
    }
    
    private func drawTrajectoryPath(in context: CGContext, points: [CLLocationCoordinate2D], size: CGSize, settings: VideoGenerationSettings) {
        guard !points.isEmpty else { return }
        
        // Convert geographic coordinates to screen coordinates
        let screenPoints = convertToScreenCoordinates(points, in: size)
        
        // Set path style
        context.setStrokeColor(settings.pathColor.cgColor)
        context.setLineWidth(settings.pathWidth)
        context.setLineCap(.round)
        context.setLineJoin(.round)
        
        // Begin path
        context.beginPath()
        context.move(to: screenPoints[0])
        
        // Add points to path
        for i in 1..<screenPoints.count {
            context.addLine(to: screenPoints[i])
        }
        
        // Stroke path
        context.strokePath()
    }
    
    private func drawPositionMarker(in context: CGContext, at point: CLLocationCoordinate2D, size: CGSize, settings: VideoGenerationSettings) {
        // Convert geographic coordinate to screen coordinate
        let screenPoint = convertToScreenCoordinate(point, in: size)
        
        // Draw marker
        context.setFillColor(settings.markerColor.cgColor)
        context.fillEllipse(in: CGRect(
            x: screenPoint.x - settings.markerSize / 2,
            y: screenPoint.y - settings.markerSize / 2,
            width: settings.markerSize,
            height: settings.markerSize
        ))
        
        // Draw outer ring
        context.setStrokeColor(UIColor.white.cgColor)
        context.setLineWidth(2)
        context.strokeEllipse(in: CGRect(
            x: screenPoint.x - settings.markerSize / 2 - 2,
            y: screenPoint.y - settings.markerSize / 2 - 2,
            width: settings.markerSize + 4,
            height: settings.markerSize + 4
        ))
    }
    
    private func drawInformation(in context: CGContext, trajectory: TrajectoryRecord, currentIndex: Int, size: CGSize, settings: VideoGenerationSettings) {
        // Set text attributes
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .left
        
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 16, weight: .medium),
            .foregroundColor: UIColor.white,
            .paragraphStyle: paragraphStyle,
            .strokeColor: UIColor.black,
            .strokeWidth: -2.0
        ]
        
        // Draw title
        let titleRect = CGRect(x: 20, y: 20, width: size.width - 40, height: 30)
        trajectory.name.draw(in: titleRect, withAttributes: attributes)
        
        // Draw date
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .short
        
        let dateString = dateFormatter.string(from: trajectory.startTime)
        let dateRect = CGRect(x: 20, y: 50, width: size.width - 40, height: 25)
        dateString.draw(in: dateRect, withAttributes: attributes)
        
        // Draw distance (if we have enough points to calculate)
        if currentIndex > 0 && currentIndex < trajectory.points.count {
            let distance = calculateDistance(forPoints: Array(trajectory.points[0...currentIndex]))
            let distanceString = String(format: "距离: %.2f 公里", distance / 1000)
            let distanceRect = CGRect(x: 20, y: 80, width: size.width - 40, height: 25)
            distanceString.draw(in: distanceRect, withAttributes: attributes)
        }
    }
    
    // MARK: - Helper Methods
    private func convertToScreenCoordinates(_ coordinates: [CLLocationCoordinate2D], in size: CGSize) -> [CGPoint] {
        guard !coordinates.isEmpty else { return [] }
        
        // Find the bounding box of the coordinates
        var minLat = coordinates[0].latitude
        var maxLat = coordinates[0].latitude
        var minLon = coordinates[0].longitude
        var maxLon = coordinates[0].longitude
        
        for coordinate in coordinates {
            minLat = min(minLat, coordinate.latitude)
            maxLat = max(maxLat, coordinate.latitude)
            minLon = min(minLon, coordinate.longitude)
            maxLon = max(maxLon, coordinate.longitude)
        }
        
        // Add some padding
        let latPadding = (maxLat - minLat) * 0.1
        let lonPadding = (maxLon - minLon) * 0.1
        
        minLat -= latPadding
        maxLat += latPadding
        minLon -= lonPadding
        maxLon += lonPadding
        
        // Ensure aspect ratio matches the screen
        let latSpan = maxLat - minLat
        let lonSpan = maxLon - minLon
        
        let screenAspect = size.width / size.height
        let coordinateAspect = lonSpan / latSpan
        
        if coordinateAspect > screenAspect {
            // Coordinate box is wider than screen
            let newLatSpan = lonSpan / screenAspect
            let latPadding = (newLatSpan - latSpan) / 2
            minLat -= latPadding
            maxLat += latPadding
        } else {
            // Coordinate box is taller than screen
            let newLonSpan = latSpan * screenAspect
            let lonPadding = (newLonSpan - lonSpan) / 2
            minLon -= lonPadding
            maxLon += lonPadding
        }
        
        // Convert coordinates to screen points
        return coordinates.map { coordinate in
            let x = (coordinate.longitude - minLon) / (maxLon - minLon) * size.width
            let y = (1 - (coordinate.latitude - minLat) / (maxLat - minLat)) * size.height
            return CGPoint(x: x, y: y)
        }
    }
    
    private func convertToScreenCoordinate(_ coordinate: CLLocationCoordinate2D, in size: CGSize) -> CGPoint {
        return convertToScreenCoordinates([coordinate], in: size)[0]
    }
    
    private func calculateDistance(forPoints points: [CLLocationCoordinate2D]) -> Double {
        guard points.count > 1 else { return 0 }
        
        var totalDistance = 0.0
        
        for i in 0..<points.count - 1 {
            let location1 = CLLocation(latitude: points[i].latitude, longitude: points[i].longitude)
            let location2 = CLLocation(latitude: points[i + 1].latitude, longitude: points[i + 1].longitude)
            
            totalDistance += location1.distance(from: location2)
        }
        
        return totalDistance
    }
}




