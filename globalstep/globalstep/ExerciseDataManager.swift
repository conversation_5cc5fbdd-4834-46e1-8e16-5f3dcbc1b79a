import Foundation
import CoreLocation
import UIKit

class ExerciseDataManager {
    static let shared = ExerciseDataManager()
    
    private var exerciseRecords: [ExerciseRecord] = []
    private var exerciseGoals: [ExerciseGoal] = []
    private let userDefaults = UserDefaults.standard
    
    private let exerciseRecordsKey = "ExerciseRecords"
    private let exerciseGoalsKey = "ExerciseGoals"
    
    private init() {
        loadData()
        createDefaultGoals()
    }
    
    // MARK: - 数据加载和保存
    private func loadData() {
        loadExerciseRecords()
        loadExerciseGoals()
    }
    
    private func loadExerciseRecords() {
        if let data = userDefaults.data(forKey: exerciseRecordsKey),
           let records = try? JSONDecoder().decode([ExerciseRecord].self, from: data) {
            exerciseRecords = records
        }
    }
    
    private func saveExerciseRecords() {
        if let data = try? JSONEncoder().encode(exerciseRecords) {
            userDefaults.set(data, forKey: exerciseRecordsKey)
        }
    }
    
    private func loadExerciseGoals() {
        if let data = userDefaults.data(forKey: exerciseGoalsKey),
           let goals = try? JSONDecoder().decode([ExerciseGoal].self, from: data) {
            exerciseGoals = goals
        }
    }
    
    private func saveExerciseGoals() {
        if let data = try? JSONEncoder().encode(exerciseGoals) {
            userDefaults.set(data, forKey: exerciseGoalsKey)
        }
    }
    
    // MARK: - 运动记录管理
    func addExerciseRecord(_ record: ExerciseRecord) {
        exerciseRecords.append(record)
        exerciseRecords.sort { $0.startTime > $1.startTime }
        saveExerciseRecords()
        updateGoalsProgress()
    }
    
    func updateExerciseRecord(_ record: ExerciseRecord) {
        if let index = exerciseRecords.firstIndex(where: { $0.id == record.id }) {
            exerciseRecords[index] = record
            saveExerciseRecords()
            updateGoalsProgress()
        }
    }
    
    func deleteExerciseRecord(_ recordId: String) {
        exerciseRecords.removeAll { $0.id == recordId }
        saveExerciseRecords()
        updateGoalsProgress()
    }
    
    func getExerciseRecords() -> [ExerciseRecord] {
        return exerciseRecords
    }
    
    func getExerciseRecord(by id: String) -> ExerciseRecord? {
        return exerciseRecords.first { $0.id == id }
    }
    
    func getExerciseRecords(for type: ExerciseType) -> [ExerciseRecord] {
        return exerciseRecords.filter { $0.type == type }
    }
    
    func getRecentExerciseRecords(limit: Int = 10) -> [ExerciseRecord] {
        return Array(exerciseRecords.prefix(limit))
    }
    
    // MARK: - 统计计算
    func getExerciseStats() -> ExerciseStats {
        let totalExercises = exerciseRecords.count
        let totalDuration = exerciseRecords.reduce(0) { $0 + $1.duration }
        let totalDistance = exerciseRecords.reduce(0) { $0 + $1.distance }
        let totalCalories = exerciseRecords.reduce(0) { $0 + $1.calories }
        
        let averageDistance = totalExercises > 0 ? totalDistance / Double(totalExercises) : 0
        let averageDuration = totalExercises > 0 ? totalDuration / Double(totalExercises) : 0
        
        let favoriteExerciseType = calculateFavoriteExerciseType()
        let longestExercise = exerciseRecords.max { $0.duration < $1.duration }
        let fastestExercise = exerciseRecords.max { $0.averageSpeed < $1.averageSpeed }
        
        let thisWeekStats = calculateWeeklyStats()
        let thisMonthStats = calculateMonthlyStats()
        let exerciseTypeStats = calculateExerciseTypeStats()
        
        return ExerciseStats(
            totalExercises: totalExercises,
            totalDuration: totalDuration,
            totalDistance: totalDistance,
            totalCalories: totalCalories,
            averageDistance: averageDistance,
            averageDuration: averageDuration,
            favoriteExerciseType: favoriteExerciseType,
            longestExercise: longestExercise,
            fastestExercise: fastestExercise,
            thisWeekStats: thisWeekStats,
            thisMonthStats: thisMonthStats,
            exerciseTypeStats: exerciseTypeStats
        )
    }
    
    private func calculateFavoriteExerciseType() -> ExerciseType? {
        let typeCount = Dictionary(grouping: exerciseRecords, by: { $0.type })
            .mapValues { $0.count }
        
        return typeCount.max { $0.value < $1.value }?.key
    }
    
    private func calculateWeeklyStats() -> WeeklyStats {
        let calendar = Calendar.current
        let now = Date()
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: now) ?? now
        
        let weekRecords = exerciseRecords.filter { $0.startTime >= weekAgo }
        
        let exerciseCount = weekRecords.count
        let totalDuration = weekRecords.reduce(0) { $0 + $1.duration }
        let totalDistance = weekRecords.reduce(0) { $0 + $1.distance }
        let totalCalories = weekRecords.reduce(0) { $0 + $1.calories }
        
        let exerciseDays = Set(weekRecords.map { 
            calendar.startOfDay(for: $0.startTime) 
        }).count
        
        return WeeklyStats(
            exerciseCount: exerciseCount,
            totalDuration: totalDuration,
            totalDistance: totalDistance,
            totalCalories: totalCalories,
            exerciseDays: exerciseDays
        )
    }
    
    private func calculateMonthlyStats() -> ExerciseMonthlyStats {
        let calendar = Calendar.current
        let now = Date()
        let monthAgo = calendar.date(byAdding: .month, value: -1, to: now) ?? now
        
        let monthRecords = exerciseRecords.filter { $0.startTime >= monthAgo }
        
        let exerciseCount = monthRecords.count
        let totalDuration = monthRecords.reduce(0) { $0 + $1.duration }
        let totalDistance = monthRecords.reduce(0) { $0 + $1.distance }
        let totalCalories = monthRecords.reduce(0) { $0 + $1.calories }
        
        let exerciseDays = Set(monthRecords.map { 
            calendar.startOfDay(for: $0.startTime) 
        }).count
        
        let daysInMonth = calendar.range(of: .day, in: .month, for: now)?.count ?? 30
        let averagePerDay = totalDistance / Double(daysInMonth)
        
        return ExerciseMonthlyStats(
            exerciseCount: exerciseCount,
            totalDuration: totalDuration,
            totalDistance: totalDistance,
            totalCalories: totalCalories,
            exerciseDays: exerciseDays,
            averagePerDay: averagePerDay
        )
    }
    
    private func calculateExerciseTypeStats() -> [ExerciseTypeStats] {
        let totalExercises = exerciseRecords.count
        guard totalExercises > 0 else { return [] }
        
        let groupedRecords = Dictionary(grouping: exerciseRecords, by: { $0.type })
        
        return groupedRecords.map { type, records in
            let count = records.count
            let totalDuration = records.reduce(0) { $0 + $1.duration }
            let totalDistance = records.reduce(0) { $0 + $1.distance }
            let totalCalories = records.reduce(0) { $0 + $1.calories }
            let percentage = Double(count) / Double(totalExercises) * 100
            
            return ExerciseTypeStats(
                type: type,
                count: count,
                totalDuration: totalDuration,
                totalDistance: totalDistance,
                totalCalories: totalCalories,
                percentage: percentage
            )
        }.sorted { $0.count > $1.count }
    }
    
    // MARK: - 目标管理
    func addExerciseGoal(_ goal: ExerciseGoal) {
        exerciseGoals.append(goal)
        saveExerciseGoals()
    }
    
    func updateExerciseGoal(_ goal: ExerciseGoal) {
        if let index = exerciseGoals.firstIndex(where: { $0.id == goal.id }) {
            exerciseGoals[index] = goal
            saveExerciseGoals()
        }
    }
    
    func deleteExerciseGoal(_ goalId: String) {
        exerciseGoals.removeAll { $0.id == goalId }
        saveExerciseGoals()
    }
    
    func getExerciseGoals() -> [ExerciseGoal] {
        return exerciseGoals.filter { $0.isActive }
    }
    
    func getActiveGoals() -> [ExerciseGoal] {
        let now = Date()
        return exerciseGoals.filter { 
            $0.isActive && $0.startDate <= now && $0.endDate >= now 
        }
    }
    
    private func createDefaultGoals() {
        guard exerciseGoals.isEmpty else { return }
        
        let calendar = Calendar.current
        let now = Date()
        
        // 每周跑步目标
        let weeklyRunningGoal = ExerciseGoal(
            id: UUID().uuidString,
            type: .distance,
            targetValue: 10.0, // 10公里
            currentValue: 0.0,
            period: .weekly,
            startDate: calendar.startOfDay(for: now),
            endDate: calendar.date(byAdding: .day, value: 7, to: now) ?? now,
            isActive: true
        )
        
        // 每日运动时长目标
        let dailyDurationGoal = ExerciseGoal(
            id: UUID().uuidString,
            type: .duration,
            targetValue: 30.0, // 30分钟
            currentValue: 0.0,
            period: .daily,
            startDate: calendar.startOfDay(for: now),
            endDate: calendar.date(byAdding: .day, value: 1, to: now) ?? now,
            isActive: true
        )
        
        exerciseGoals = [weeklyRunningGoal, dailyDurationGoal]
        saveExerciseGoals()
    }
    
    private func updateGoalsProgress() {
        let calendar = Calendar.current
        let now = Date()
        
        for (index, goal) in exerciseGoals.enumerated() {
            guard goal.isActive && goal.startDate <= now && goal.endDate >= now else { continue }
            
            var relevantRecords: [ExerciseRecord] = []
            
            switch goal.period {
            case .daily:
                let startOfDay = calendar.startOfDay(for: now)
                relevantRecords = exerciseRecords.filter { 
                    $0.startTime >= startOfDay 
                }
            case .weekly:
                let weekAgo = calendar.date(byAdding: .day, value: -7, to: now) ?? now
                relevantRecords = exerciseRecords.filter { 
                    $0.startTime >= weekAgo 
                }
            case .monthly:
                let monthAgo = calendar.date(byAdding: .month, value: -1, to: now) ?? now
                relevantRecords = exerciseRecords.filter { 
                    $0.startTime >= monthAgo 
                }
            }
            
            var currentValue: Double = 0.0
            
            switch goal.type {
            case .distance:
                currentValue = relevantRecords.reduce(0) { $0 + $1.distanceInKm }
            case .duration:
                currentValue = relevantRecords.reduce(0) { $0 + $1.duration } / 60.0 // 转换为分钟
            case .calories:
                currentValue = relevantRecords.reduce(0) { $0 + $1.calories }
            case .frequency:
                currentValue = Double(relevantRecords.count)
            }
            
            let updatedGoal = ExerciseGoal(
                id: goal.id,
                type: goal.type,
                targetValue: goal.targetValue,
                currentValue: currentValue,
                period: goal.period,
                startDate: goal.startDate,
                endDate: goal.endDate,
                isActive: goal.isActive
            )
            
            exerciseGoals[index] = updatedGoal
        }
        
        saveExerciseGoals()
    }
    
    // MARK: - 卡路里计算
    func calculateCalories(for type: ExerciseType, duration: TimeInterval, distance: Double, weight: Double = 70.0) -> Double {
        // 简化的卡路里计算，基于MET值
        let durationInHours = duration / 3600.0
        
        let metValue: Double
        switch type {
        case .running:
            let speedKmh = (distance / 1000.0) / durationInHours
            if speedKmh < 8 {
                metValue = 8.0
            } else if speedKmh < 10 {
                metValue = 10.0
            } else if speedKmh < 12 {
                metValue = 12.0
            } else {
                metValue = 15.0
            }
        case .walking:
            metValue = 3.5
        case .cycling:
            let speedKmh = (distance / 1000.0) / durationInHours
            if speedKmh < 16 {
                metValue = 6.0
            } else if speedKmh < 20 {
                metValue = 8.0
            } else {
                metValue = 10.0
            }
        case .hiking:
            metValue = 6.0
        case .swimming:
            metValue = 8.0
        case .basketball:
            metValue = 8.0
        case .football:
            metValue = 7.0
        case .tennis:
            metValue = 7.0
        case .badminton:
            metValue = 5.5
        case .yoga:
            metValue = 3.0
        case .fitness:
            metValue = 6.0
        case .other:
            metValue = 5.0
        }
        
        // 卡路里 = MET × 体重(kg) × 时间(小时)
        return metValue * weight * durationInHours
    }
    
    // MARK: - 测试数据
    func addTestData() {
        let calendar = Calendar.current
        let now = Date()
        
        // 添加一些测试运动记录
        let testRecords = [
            createTestRecord(
                type: .running,
                daysAgo: 1,
                duration: 1800, // 30分钟
                distance: 5000, // 5公里
                calendar: calendar,
                now: now
            ),
            createTestRecord(
                type: .walking,
                daysAgo: 2,
                duration: 2400, // 40分钟
                distance: 3000, // 3公里
                calendar: calendar,
                now: now
            ),
            createTestRecord(
                type: .cycling,
                daysAgo: 3,
                duration: 3600, // 60分钟
                distance: 15000, // 15公里
                calendar: calendar,
                now: now
            )
        ]
        
        for record in testRecords {
            addExerciseRecord(record)
        }
    }
    
    private func createTestRecord(type: ExerciseType, daysAgo: Int, duration: TimeInterval, distance: Double, calendar: Calendar, now: Date) -> ExerciseRecord {
        let startTime = calendar.date(byAdding: .day, value: -daysAgo, to: now) ?? now
        let endTime = startTime.addingTimeInterval(duration)
        
        // 生成简单的轨迹数据
        var trajectory: [TrajectoryPoint] = []
        if type.hasTrajectory {
            let pointCount = Int(duration / 30) // 每30秒一个点
            for i in 0..<pointCount {
                let timestamp = startTime.addingTimeInterval(Double(i) * 30)
                let location = CLLocation(
                    coordinate: CLLocationCoordinate2D(
                        latitude: 39.9042 + Double.random(in: -0.01...0.01),
                        longitude: 116.4074 + Double.random(in: -0.01...0.01)
                    ),
                    altitude: 50.0,
                    horizontalAccuracy: 5.0,
                    verticalAccuracy: 5.0,
                    timestamp: timestamp
                )
                trajectory.append(TrajectoryPoint(location: location))
            }
        }
        
        let averageSpeed = distance / duration
        let maxSpeed = averageSpeed * 1.5
        let calories = calculateCalories(for: type, duration: duration, distance: distance)
        
        return ExerciseRecord(
            id: UUID().uuidString,
            type: type,
            startTime: startTime,
            endTime: endTime,
            duration: duration,
            distance: distance,
            calories: calories,
            averageSpeed: averageSpeed,
            maxSpeed: maxSpeed,
            trajectory: trajectory,
            notes: nil,
            weather: "晴朗",
            temperature: 22.0,
            heartRate: nil
        )
    }
} 