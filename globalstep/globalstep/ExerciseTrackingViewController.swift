import UIKit
import MapKit
import CoreLocation

class ExerciseTrackingViewController: UIViewController {
    
    // MARK: - Properties
    var exerciseType: ExerciseType = .running
    
    private let locationManager = CLLocationManager()
    private let dataManager = ExerciseDataManager.shared
    
    private var isTracking = false
    private var isPaused = false
    private var isLocationReady = false  // 新增：位置是否已准备好
    private var startTime: Date?
    private var pausedTime: TimeInterval = 0
    private var currentDistance: Double = 0
    private var trajectoryPoints: [TrajectoryPoint] = []
    private var lastLocation: CLLocation?
    private var currentLocation: CLLocation?  // 新增：当前位置
    
    private var timer: Timer?
    private var currentDuration: TimeInterval = 0
    
    // MARK: - UI Components
    private let mapView = MKMapView()
    private let statsContainerView = UIView()
    
    // 准备状态提示
    private let preparingView = UIView()
    private let preparingLabel = UILabel()
    private let preparingActivityIndicator = UIActivityIndicatorView(style: .large)
    
    // 统计显示
    private let durationLabel = UILabel()
    private let distanceLabel = UILabel()
    private let speedLabel = UILabel()
    private let paceLabel = UILabel()
    
    // 控制按钮
    private let controlButtonsStackView = UIStackView()
    private let startPauseButton = UIButton(type: .system)
    private let stopButton = UIButton(type: .system)
    private let lockButton = UIButton(type: .system)
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupLocationManager()
        setupMapView()
        showPreparingState()  // 显示准备状态
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        // 开始获取用户位置（但不记录轨迹）
        requestUserLocation()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        if isTracking {
            pauseTracking()
        }
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = exerciseType.displayName
        view.backgroundColor = .systemBackground
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelTapped)
        )
        
        setupMapView()
        setupStatsContainer()
        setupControlButtons()
        setupPreparingView()  // 新增：设置准备状态视图
        setupConstraints()
    }
    
    private func setupMapView() {
        mapView.delegate = self
        mapView.mapType = .standard
        mapView.showsUserLocation = true
        mapView.userTrackingMode = .follow
        mapView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(mapView)
    }
    
    private func setupStatsContainer() {
        statsContainerView.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.95)
        statsContainerView.layer.cornerRadius = 12
        statsContainerView.layer.shadowColor = UIColor.black.cgColor
        statsContainerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        statsContainerView.layer.shadowOpacity = 0.1
        statsContainerView.layer.shadowRadius = 4
        statsContainerView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(statsContainerView)
        
        // 创建统计标签网格
        let topStackView = UIStackView()
        topStackView.axis = .horizontal
        topStackView.distribution = .fillEqually
        topStackView.spacing = 20
        topStackView.translatesAutoresizingMaskIntoConstraints = false
        
        let bottomStackView = UIStackView()
        bottomStackView.axis = .horizontal
        bottomStackView.distribution = .fillEqually
        bottomStackView.spacing = 20
        bottomStackView.translatesAutoresizingMaskIntoConstraints = false
        
        // 配置统计标签
        configureStat(label: durationLabel, title: "时长", value: "00:00", color: .systemBlue)
        configureStat(label: distanceLabel, title: "距离", value: "0.00", color: .systemGreen)
        configureStat(label: speedLabel, title: "速度", value: "0.0", color: .systemOrange)
        configureStat(label: paceLabel, title: "配速", value: "--'--\"", color: .systemPurple)
        
        topStackView.addArrangedSubview(createStatContainer(label: durationLabel, title: "时长"))
        topStackView.addArrangedSubview(createStatContainer(label: distanceLabel, title: "距离"))
        
        bottomStackView.addArrangedSubview(createStatContainer(label: speedLabel, title: "速度"))
        bottomStackView.addArrangedSubview(createStatContainer(label: paceLabel, title: "配速"))
        
        statsContainerView.addSubview(topStackView)
        statsContainerView.addSubview(bottomStackView)
        
        NSLayoutConstraint.activate([
            topStackView.topAnchor.constraint(equalTo: statsContainerView.topAnchor, constant: 15),
            topStackView.leadingAnchor.constraint(equalTo: statsContainerView.leadingAnchor, constant: 15),
            topStackView.trailingAnchor.constraint(equalTo: statsContainerView.trailingAnchor, constant: -15),
            topStackView.heightAnchor.constraint(equalToConstant: 65),
            
            bottomStackView.topAnchor.constraint(equalTo: topStackView.bottomAnchor, constant: 15),
            bottomStackView.leadingAnchor.constraint(equalTo: statsContainerView.leadingAnchor, constant: 15),
            bottomStackView.trailingAnchor.constraint(equalTo: statsContainerView.trailingAnchor, constant: -15),
            bottomStackView.heightAnchor.constraint(equalToConstant: 65),
            bottomStackView.bottomAnchor.constraint(equalTo: statsContainerView.bottomAnchor, constant: -15)
        ])
    }
    
    private func configureStat(label: UILabel, title: String, value: String, color: UIColor) {
        label.text = value
        label.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        label.textColor = color
        label.textAlignment = .center
        label.adjustsFontSizeToFitWidth = true
        label.minimumScaleFactor = 0.8
    }
    
    private func createStatContainer(label: UILabel, title: String) -> UIView {
        let container = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        titleLabel.textColor = .secondaryLabel
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        label.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(titleLabel)
        container.addSubview(label)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            
            label.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 5),
            label.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            label.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            label.bottomAnchor.constraint(equalTo: container.bottomAnchor)
        ])
        
        return container
    }
    
    private func setupControlButtons() {
        controlButtonsStackView.axis = .horizontal
        controlButtonsStackView.distribution = .fillEqually
        controlButtonsStackView.spacing = 20
        controlButtonsStackView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(controlButtonsStackView)
        
        // 开始/暂停按钮
        startPauseButton.setTitle("开始", for: .normal)
        startPauseButton.backgroundColor = .systemGreen
        startPauseButton.setTitleColor(.white, for: .normal)
        startPauseButton.layer.cornerRadius = 30
        startPauseButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        startPauseButton.addTarget(self, action: #selector(startPauseButtonTapped), for: .touchUpInside)
        
        // 停止按钮
        stopButton.setTitle("停止", for: .normal)
        stopButton.backgroundColor = .systemRed
        stopButton.setTitleColor(.white, for: .normal)
        stopButton.layer.cornerRadius = 30
        stopButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        stopButton.isEnabled = false
        stopButton.alpha = 0.5
        stopButton.addTarget(self, action: #selector(stopButtonTapped), for: .touchUpInside)
        
        // 锁定按钮
        lockButton.setImage(UIImage(systemName: "lock"), for: .normal)
        lockButton.backgroundColor = .systemGray
        lockButton.tintColor = .white
        lockButton.layer.cornerRadius = 25
        lockButton.addTarget(self, action: #selector(lockButtonTapped), for: .touchUpInside)
        
        controlButtonsStackView.addArrangedSubview(lockButton)
        controlButtonsStackView.addArrangedSubview(startPauseButton)
        controlButtonsStackView.addArrangedSubview(stopButton)
    }
    
    private func setupPreparingView() {
        preparingView.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.95)
        preparingView.layer.cornerRadius = 12
        preparingView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(preparingView)
        
        preparingLabel.text = "正在获取当前位置..."
        preparingLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        preparingLabel.textAlignment = .center
        preparingLabel.numberOfLines = 0
        preparingLabel.translatesAutoresizingMaskIntoConstraints = false
        preparingView.addSubview(preparingLabel)
        
        preparingActivityIndicator.startAnimating()
        preparingActivityIndicator.translatesAutoresizingMaskIntoConstraints = false
        preparingView.addSubview(preparingActivityIndicator)
        
        NSLayoutConstraint.activate([
            preparingView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            preparingView.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            preparingView.widthAnchor.constraint(equalToConstant: 200),
            preparingView.heightAnchor.constraint(equalToConstant: 100),
            
            preparingActivityIndicator.centerXAnchor.constraint(equalTo: preparingView.centerXAnchor),
            preparingActivityIndicator.topAnchor.constraint(equalTo: preparingView.topAnchor, constant: 20),
            
            preparingLabel.topAnchor.constraint(equalTo: preparingActivityIndicator.bottomAnchor, constant: 15),
            preparingLabel.leadingAnchor.constraint(equalTo: preparingView.leadingAnchor, constant: 15),
            preparingLabel.trailingAnchor.constraint(equalTo: preparingView.trailingAnchor, constant: -15),
            preparingLabel.bottomAnchor.constraint(equalTo: preparingView.bottomAnchor, constant: -15)
        ])
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 地图视图
            mapView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            mapView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            mapView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            mapView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            // 统计容器
            statsContainerView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
            statsContainerView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            statsContainerView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            
            // 控制按钮
            controlButtonsStackView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -30),
            controlButtonsStackView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 40),
            controlButtonsStackView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -40),
            controlButtonsStackView.heightAnchor.constraint(equalToConstant: 60),
            
            // 按钮尺寸
            lockButton.widthAnchor.constraint(equalToConstant: 50),
            startPauseButton.heightAnchor.constraint(equalToConstant: 60),
            stopButton.heightAnchor.constraint(equalToConstant: 60)
        ])
    }
    
    // MARK: - Location Manager Setup
    private func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        locationManager.distanceFilter = 5.0
        
        // 不在这里请求权限，等到 viewDidAppear 时再请求
    }
    
    // MARK: - Tracking Control
    private func startTracking() {
        guard !isTracking && isLocationReady else { 
            print("无法开始追踪：isTracking=\(isTracking), isLocationReady=\(isLocationReady)")
            return 
        }
        
        isTracking = true
        isPaused = false
        startTime = Date()
        currentDistance = 0
        trajectoryPoints.removeAll()
        lastLocation = currentLocation  // 使用当前位置作为起始点
        
        // 添加起始点到轨迹
        if let startLocation = currentLocation {
            let startPoint = TrajectoryPoint(location: startLocation)
            trajectoryPoints.append(startPoint)
        }
        
        // 确保位置更新仍在进行（但现在会记录轨迹）
        let needsLocationUpdate = locationManager.location?.timestamp.timeIntervalSinceNow ?? -100 < -10
        if needsLocationUpdate {
            locationManager.startUpdatingLocation()
        }
        
        startTimer()
        updateUI()
        
        print("开始运动追踪")
    }
    
    private func pauseTracking() {
        guard isTracking && !isPaused else { return }
        
        isPaused = true
        locationManager.stopUpdatingLocation()
        stopTimer()
        
        updateUI()
    }
    
    private func resumeTracking() {
        guard isTracking && isPaused else { return }
        
        isPaused = false
        locationManager.startUpdatingLocation()
        startTimer()
        
        updateUI()
    }
    
    private func stopTracking() {
        guard isTracking else { return }
        
        isTracking = false
        isPaused = false
        locationManager.stopUpdatingLocation()
        stopTimer()
        
        saveExerciseRecord()
        updateUI()
    }
    
    private func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateDuration()
        }
    }
    
    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }
    
    private func updateDuration() {
        guard let startTime = startTime else { return }
        currentDuration = Date().timeIntervalSince(startTime) - pausedTime
        updateStatsDisplay()
    }
    
    // MARK: - UI Updates
    private func updateUI() {
        if isTracking {
            if isPaused {
                startPauseButton.setTitle("继续", for: .normal)
                startPauseButton.backgroundColor = .systemGreen
            } else {
                startPauseButton.setTitle("暂停", for: .normal)
                startPauseButton.backgroundColor = .systemOrange
            }
            stopButton.isEnabled = true
            stopButton.alpha = 1.0
        } else {
            startPauseButton.setTitle("开始", for: .normal)
            startPauseButton.backgroundColor = .systemGreen
            stopButton.isEnabled = false
            stopButton.alpha = 0.5
        }
    }
    
    private func updateStatsDisplay() {
        // 时长
        let hours = Int(currentDuration) / 3600
        let minutes = Int(currentDuration) % 3600 / 60
        let seconds = Int(currentDuration) % 60
        
        if hours > 0 {
            durationLabel.text = String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            durationLabel.text = String(format: "%02d:%02d", minutes, seconds)
        }
        
        // 距离
        let distanceKm = currentDistance / 1000.0
        distanceLabel.text = String(format: "%.2f", distanceKm)
        
        // 速度和配速
        if currentDuration > 0 {
            let speedKmh = (currentDistance / 1000.0) / (currentDuration / 3600.0)
            speedLabel.text = String(format: "%.1f", speedKmh)
            
            if currentDistance > 0 {
                let paceSeconds = currentDuration / (currentDistance / 1000.0)
                let paceMinutes = Int(paceSeconds) / 60
                let paceSecondsRemainder = Int(paceSeconds) % 60
                paceLabel.text = String(format: "%d'%02d\"", paceMinutes, paceSecondsRemainder)
            }
        } else {
            speedLabel.text = "0.0"
            paceLabel.text = "--'--\""
        }
    }
    
    // MARK: - Map Updates
    private func updateMapWithNewLocation(_ location: CLLocation) {
        // 添加轨迹点
        let trajectoryPoint = TrajectoryPoint(location: location)
        trajectoryPoints.append(trajectoryPoint)
        
        // 计算距离
        if let lastLocation = lastLocation {
            let distance = location.distance(from: lastLocation)
            if distance > 5.0 && distance < 100.0 { // 过滤异常数据
                currentDistance += distance
            }
        }
        
        lastLocation = location
        
        // 更新地图轨迹
        updateMapTrajectory()
        
        // 更新统计显示
        updateStatsDisplay()
    }
    
    private func updateMapTrajectory() {
        // 移除旧的轨迹
        mapView.removeOverlays(mapView.overlays)
        
        // 添加新的轨迹
        if trajectoryPoints.count > 1 {
            let coordinates = trajectoryPoints.map { $0.coordinate.clLocationCoordinate }
            let polyline = MKPolyline(coordinates: coordinates, count: coordinates.count)
            mapView.addOverlay(polyline)
        }
    }
    
    // MARK: - Save Exercise Record
    private func saveExerciseRecord() {
        guard let startTime = startTime, currentDuration > 0 else { return }
        
        let endTime = Date()
        let averageSpeed = currentDuration > 0 ? currentDistance / currentDuration : 0
        let maxSpeed = trajectoryPoints.map { $0.speed }.max() ?? 0
        let calories = dataManager.calculateCalories(
            for: exerciseType,
            duration: currentDuration,
            distance: currentDistance
        )
        
        let record = ExerciseRecord(
            id: UUID().uuidString,
            type: exerciseType,
            startTime: startTime,
            endTime: endTime,
            duration: currentDuration,
            distance: currentDistance,
            calories: calories,
            averageSpeed: averageSpeed,
            maxSpeed: maxSpeed,
            trajectory: trajectoryPoints,
            notes: nil,
            weather: nil,
            temperature: nil,
            heartRate: nil
        )
        
        dataManager.addExerciseRecord(record)
        
        // 显示完成界面
        showCompletionScreen(record: record)
    }
    
    private func showCompletionScreen(record: ExerciseRecord) {
        let completionVC = ExerciseCompletionViewController()
        completionVC.exerciseRecord = record
        completionVC.modalPresentationStyle = .fullScreen
        present(completionVC, animated: true)
    }
    
    // MARK: - Actions
    @objc private func startPauseButtonTapped() {
        if !isTracking {
            // 检查位置是否准备好
            if !isLocationReady {
                let alert = UIAlertController(
                    title: "位置未准备好",
                    message: "请等待GPS定位完成后再开始运动",
                    preferredStyle: .alert
                )
                alert.addAction(UIAlertAction(title: "确定", style: .default))
                present(alert, animated: true)
                return
            }
            startTracking()
        } else if isPaused {
            resumeTracking()
        } else {
            pauseTracking()
        }
    }
    
    @objc private func stopButtonTapped() {
        let alert = UIAlertController(
            title: "结束运动",
            message: "确定要结束当前运动吗？",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "继续运动", style: .cancel))
        alert.addAction(UIAlertAction(title: "结束运动", style: .destructive) { [weak self] _ in
            self?.stopTracking()
        })
        
        present(alert, animated: true)
    }
    
    @objc private func lockButtonTapped() {
        // 锁定屏幕功能
        let alert = UIAlertController(
            title: "屏幕锁定",
            message: "屏幕已锁定，防止误触。双击解锁。",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "解锁", style: .default))
        present(alert, animated: true)
    }
    
    @objc private func cancelTapped() {
        if isTracking {
            let alert = UIAlertController(
                title: "取消运动",
                message: "当前正在运动中，确定要取消吗？运动数据将不会保存。",
                preferredStyle: .alert
            )
            
            alert.addAction(UIAlertAction(title: "继续运动", style: .cancel))
            alert.addAction(UIAlertAction(title: "取消运动", style: .destructive) { [weak self] _ in
                self?.locationManager.stopUpdatingLocation()
                self?.stopTimer()
                self?.dismiss(animated: true)
            })
            
            present(alert, animated: true)
        } else {
            dismiss(animated: true)
        }
    }
    
    private func showPreparingState() {
        preparingView.isHidden = false
        statsContainerView.isHidden = true
        controlButtonsStackView.isHidden = true
        
        // 禁用开始按钮直到位置准备好
        startPauseButton.isEnabled = false
        startPauseButton.alpha = 0.5
    }
    
    private func hidePreparingState() {
        preparingView.isHidden = true
        statsContainerView.isHidden = false
        controlButtonsStackView.isHidden = false
        
        // 启用开始按钮
        startPauseButton.isEnabled = true
        startPauseButton.alpha = 1.0
        
        preparingActivityIndicator.stopAnimating()
    }
    
    private func requestUserLocation() {
        guard CLLocationManager.locationServicesEnabled() else {
            showLocationPermissionAlert()
            return
        }
        
        switch locationManager.authorizationStatus {
        case .authorizedWhenInUse, .authorizedAlways:
            // 开始获取位置（仅用于定位，不记录轨迹）
            locationManager.startUpdatingLocation()
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .denied, .restricted:
            showLocationPermissionAlert()
        @unknown default:
            break
        }
    }
}

// MARK: - CLLocationManagerDelegate
extension ExerciseTrackingViewController: CLLocationManagerDelegate {
    
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        
        // 过滤精度较差的位置
        if location.horizontalAccuracy > 20 {
            return
        }
        
        // 如果还没有准备好位置，这是初始定位
        if !isLocationReady {
            handleInitialLocation(location)
        }
        
        // 如果正在追踪运动，记录轨迹
        if isTracking && !isPaused {
            updateMapWithNewLocation(location)
        }
    }
    
    private func handleInitialLocation(_ location: CLLocation) {
        currentLocation = location
        isLocationReady = true
        
        // 定位到用户当前位置
        let region = MKCoordinateRegion(
            center: location.coordinate,
            latitudinalMeters: 1000,  // 1公里范围
            longitudinalMeters: 1000
        )
        mapView.setRegion(region, animated: true)
        
        // 隐藏准备状态，显示控制界面
        DispatchQueue.main.async { [weak self] in
            self?.hidePreparingState()
        }
        
        print("用户位置已定位: \(location.coordinate)")
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("Location error: \(error.localizedDescription)")
        
        // 如果获取位置失败，显示错误信息
        DispatchQueue.main.async { [weak self] in
            self?.preparingLabel.text = "无法获取位置信息\n请检查定位权限"
            self?.preparingActivityIndicator.stopAnimating()
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.startUpdatingLocation()
        case .denied, .restricted:
            DispatchQueue.main.async { [weak self] in
                self?.showLocationPermissionAlert()
            }
        case .notDetermined:
            // 等待用户响应权限请求
            break
        @unknown default:
            break
        }
    }
    
    private func showLocationPermissionAlert() {
        preparingActivityIndicator.stopAnimating()
        preparingLabel.text = "需要定位权限来记录运动轨迹"
        
        let alert = UIAlertController(
            title: "需要定位权限",
            message: "请在设置中开启定位权限以记录运动轨迹",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "去设置", style: .default) { _ in
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsUrl)
            }
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel) { [weak self] _ in
            self?.dismiss(animated: true)
        })
        
        present(alert, animated: true)
    }
}

// MARK: - MKMapViewDelegate
extension ExerciseTrackingViewController: MKMapViewDelegate {
    
    func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
        if let polyline = overlay as? MKPolyline {
            let renderer = MKPolylineRenderer(polyline: polyline)
            renderer.strokeColor = exerciseType.color
            renderer.lineWidth = 4.0
            return renderer
        }
        return MKOverlayRenderer(overlay: overlay)
    }
} 