import UIKit

class ExerciseStatsViewController: UIViewController {
    
    // MARK: - Properties
    var exerciseStats: ExerciseStats?
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        displayStats()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "运动统计"
        view.backgroundColor = .systemBackground
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .done,
            target: self,
            action: #selector(doneTapped)
        )
        
        setupScrollView()
    }
    
    private func setupScrollView() {
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor)
        ])
    }
    
    // MARK: - Display Stats
    private func displayStats() {
        guard let stats = exerciseStats else { return }
        
        var lastView: UIView? = nil
        
        // 总体统计卡片
        let overallCard = createOverallStatsCard(stats: stats)
        contentView.addSubview(overallCard)
        overallCard.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20).isActive = true
        overallCard.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20).isActive = true
        overallCard.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20).isActive = true
        lastView = overallCard
        
        // 本周统计卡片
        let weekCard = createWeekStatsCard(stats: stats)
        contentView.addSubview(weekCard)
        weekCard.topAnchor.constraint(equalTo: lastView!.bottomAnchor, constant: 20).isActive = true
        weekCard.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20).isActive = true
        weekCard.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20).isActive = true
        lastView = weekCard
        
        // 本月统计卡片
        let monthCard = createMonthStatsCard(stats: stats)
        contentView.addSubview(monthCard)
        monthCard.topAnchor.constraint(equalTo: lastView!.bottomAnchor, constant: 20).isActive = true
        monthCard.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20).isActive = true
        monthCard.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20).isActive = true
        lastView = monthCard
        
        // 运动类型统计卡片
        let typeCard = createExerciseTypeStatsCard(stats: stats)
        contentView.addSubview(typeCard)
        typeCard.topAnchor.constraint(equalTo: lastView!.bottomAnchor, constant: 20).isActive = true
        typeCard.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20).isActive = true
        typeCard.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20).isActive = true
        lastView = typeCard
        
        // 个人记录卡片
        let recordCard = createPersonalRecordsCard(stats: stats)
        contentView.addSubview(recordCard)
        recordCard.topAnchor.constraint(equalTo: lastView!.bottomAnchor, constant: 20).isActive = true
        recordCard.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20).isActive = true
        recordCard.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20).isActive = true
        recordCard.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20).isActive = true
    }
    
    // MARK: - Card Creation Methods
    private func createOverallStatsCard(stats: ExerciseStats) -> UIView {
        let card = createCard(title: "📊 总体统计", color: .systemBlue)
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 15
        stackView.translatesAutoresizingMaskIntoConstraints = false
        card.addSubview(stackView)
        
        // 创建统计项网格
        let topRow = UIStackView()
        topRow.axis = .horizontal
        topRow.distribution = .fillEqually
        topRow.spacing = 15
        
        let bottomRow = UIStackView()
        bottomRow.axis = .horizontal
        bottomRow.distribution = .fillEqually
        bottomRow.spacing = 15
        
        // 总运动次数
        let exerciseCountView = createStatItem(
            title: "运动次数",
            value: "\(stats.totalExercises)",
            unit: "次",
            color: .systemGreen
        )
        
        // 总时长
        let durationHours = stats.totalDuration / 3600
        let durationView = createStatItem(
            title: "总时长",
            value: String(format: "%.1f", durationHours),
            unit: "小时",
            color: .systemOrange
        )
        
        // 总距离
        let distanceKm = stats.totalDistance / 1000
        let distanceView = createStatItem(
            title: "总距离",
            value: String(format: "%.1f", distanceKm),
            unit: "公里",
            color: .systemPurple
        )
        
        // 总卡路里
        let caloriesView = createStatItem(
            title: "总卡路里",
            value: String(format: "%.0f", stats.totalCalories),
            unit: "kcal",
            color: .systemRed
        )
        
        topRow.addArrangedSubview(exerciseCountView)
        topRow.addArrangedSubview(durationView)
        
        bottomRow.addArrangedSubview(distanceView)
        bottomRow.addArrangedSubview(caloriesView)
        
        stackView.addArrangedSubview(topRow)
        stackView.addArrangedSubview(bottomRow)
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: card.topAnchor, constant: 50),
            stackView.leadingAnchor.constraint(equalTo: card.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: card.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: card.bottomAnchor, constant: -20),
            
            topRow.heightAnchor.constraint(equalToConstant: 60),
            bottomRow.heightAnchor.constraint(equalToConstant: 60)
        ])
        
        return card
    }
    
    private func createWeekStatsCard(stats: ExerciseStats) -> UIView {
        let card = createCard(title: "📅 本周统计", color: .systemGreen)
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.translatesAutoresizingMaskIntoConstraints = false
        card.addSubview(stackView)
        
        let weekStats = stats.thisWeekStats
        
        let statsItems = [
            ("运动次数", "\(weekStats.exerciseCount) 次"),
            ("运动天数", "\(weekStats.exerciseDays) 天"),
            ("总时长", String(format: "%.1f 小时", weekStats.totalDuration / 3600)),
            ("总距离", String(format: "%.1f 公里", weekStats.totalDistance / 1000)),
            ("总卡路里", String(format: "%.0f kcal", weekStats.totalCalories))
        ]
        
        for (title, value) in statsItems {
            let itemView = createDetailItem(title: title, value: value)
            stackView.addArrangedSubview(itemView)
        }
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: card.topAnchor, constant: 50),
            stackView.leadingAnchor.constraint(equalTo: card.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: card.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: card.bottomAnchor, constant: -20)
        ])
        
        return card
    }
    
    private func createMonthStatsCard(stats: ExerciseStats) -> UIView {
        let card = createCard(title: "📆 本月统计", color: .systemPurple)
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.translatesAutoresizingMaskIntoConstraints = false
        card.addSubview(stackView)
        
        let monthStats = stats.thisMonthStats
        
        let statsItems = [
            ("运动次数", "\(monthStats.exerciseCount) 次"),
            ("运动天数", "\(monthStats.exerciseDays) 天"),
            ("总时长", String(format: "%.1f 小时", monthStats.totalDuration / 3600)),
            ("总距离", String(format: "%.1f 公里", monthStats.totalDistance / 1000)),
            ("总卡路里", String(format: "%.0f kcal", monthStats.totalCalories)),
            ("日均距离", String(format: "%.1f 公里", monthStats.averagePerDay / 1000))
        ]
        
        for (title, value) in statsItems {
            let itemView = createDetailItem(title: title, value: value)
            stackView.addArrangedSubview(itemView)
        }
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: card.topAnchor, constant: 50),
            stackView.leadingAnchor.constraint(equalTo: card.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: card.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: card.bottomAnchor, constant: -20)
        ])
        
        return card
    }
    
    private func createExerciseTypeStatsCard(stats: ExerciseStats) -> UIView {
        let card = createCard(title: "🏃‍♂️ 运动类型分布", color: .systemOrange)
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 15
        stackView.translatesAutoresizingMaskIntoConstraints = false
        card.addSubview(stackView)
        
        // 最喜欢的运动类型
        if let favoriteType = stats.favoriteExerciseType {
            let favoriteLabel = UILabel()
            favoriteLabel.text = "最喜欢的运动：\(favoriteType.displayName)"
            favoriteLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
            favoriteLabel.textAlignment = .center
            stackView.addArrangedSubview(favoriteLabel)
        }
        
        // 运动类型列表
        for typeStats in stats.exerciseTypeStats.prefix(5) {
            let typeView = createExerciseTypeItem(typeStats: typeStats)
            stackView.addArrangedSubview(typeView)
        }
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: card.topAnchor, constant: 50),
            stackView.leadingAnchor.constraint(equalTo: card.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: card.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: card.bottomAnchor, constant: -20)
        ])
        
        return card
    }
    
    private func createPersonalRecordsCard(stats: ExerciseStats) -> UIView {
        let card = createCard(title: "🏆 个人记录", color: .systemRed)
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 15
        stackView.translatesAutoresizingMaskIntoConstraints = false
        card.addSubview(stackView)
        
        // 最长运动
        if let longestExercise = stats.longestExercise {
            let longestView = createRecordItem(
                title: "最长运动",
                subtitle: longestExercise.type.displayName,
                value: longestExercise.formattedDuration,
                icon: "clock"
            )
            stackView.addArrangedSubview(longestView)
        }
        
        // 最快运动
        if let fastestExercise = stats.fastestExercise {
            let fastestView = createRecordItem(
                title: "最快速度",
                subtitle: fastestExercise.type.displayName,
                value: String(format: "%.1f km/h", fastestExercise.averageSpeedKmh),
                icon: "speedometer"
            )
            stackView.addArrangedSubview(fastestView)
        }
        
        // 平均数据
        let avgDurationView = createRecordItem(
            title: "平均时长",
            subtitle: "每次运动",
            value: String(format: "%.0f 分钟", stats.averageDuration / 60),
            icon: "timer"
        )
        stackView.addArrangedSubview(avgDurationView)
        
        let avgDistanceView = createRecordItem(
            title: "平均距离",
            subtitle: "每次运动",
            value: String(format: "%.1f 公里", stats.averageDistance / 1000),
            icon: "location"
        )
        stackView.addArrangedSubview(avgDistanceView)
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: card.topAnchor, constant: 50),
            stackView.leadingAnchor.constraint(equalTo: card.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: card.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: card.bottomAnchor, constant: -20)
        ])
        
        return card
    }
    
    // MARK: - Helper Methods
    private func createCard(title: String, color: UIColor) -> UIView {
        let card = UIView()
        card.backgroundColor = .secondarySystemBackground
        card.layer.cornerRadius = 12
        card.layer.shadowColor = UIColor.black.cgColor
        card.layer.shadowOffset = CGSize(width: 0, height: 2)
        card.layer.shadowOpacity = 0.1
        card.layer.shadowRadius = 4
        card.translatesAutoresizingMaskIntoConstraints = false
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        titleLabel.textColor = color
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        card.addSubview(titleLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: card.topAnchor, constant: 15),
            titleLabel.leadingAnchor.constraint(equalTo: card.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: card.trailingAnchor, constant: -20),
            card.heightAnchor.constraint(greaterThanOrEqualToConstant: 120)
        ])
        
        return card
    }
    
    private func createStatItem(title: String, value: String, unit: String, color: UIColor) -> UIView {
        let container = UIView()
        container.backgroundColor = .tertiarySystemBackground
        container.layer.cornerRadius = 8
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        titleLabel.textColor = .secondaryLabel
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        valueLabel.textColor = color
        valueLabel.textAlignment = .center
        valueLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let unitLabel = UILabel()
        unitLabel.text = unit
        unitLabel.font = UIFont.systemFont(ofSize: 10, weight: .medium)
        unitLabel.textColor = .secondaryLabel
        unitLabel.textAlignment = .center
        unitLabel.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(titleLabel)
        container.addSubview(valueLabel)
        container.addSubview(unitLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor, constant: 8),
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 5),
            titleLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -5),
            
            valueLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 5),
            valueLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 5),
            valueLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -5),
            
            unitLabel.topAnchor.constraint(equalTo: valueLabel.bottomAnchor, constant: 2),
            unitLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 5),
            unitLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -5),
            unitLabel.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -8)
        ])
        
        return container
    }
    
    private func createDetailItem(title: String, value: String) -> UIView {
        let container = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = UIFont.systemFont(ofSize: 14)
        valueLabel.textColor = .secondaryLabel
        valueLabel.textAlignment = .right
        valueLabel.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(titleLabel)
        container.addSubview(valueLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            
            valueLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            valueLabel.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            valueLabel.leadingAnchor.constraint(greaterThanOrEqualTo: titleLabel.trailingAnchor, constant: 10),
            
            container.heightAnchor.constraint(equalToConstant: 30)
        ])
        
        return container
    }
    
    private func createExerciseTypeItem(typeStats: ExerciseTypeStats) -> UIView {
        let container = UIView()
        container.backgroundColor = .tertiarySystemBackground
        container.layer.cornerRadius = 8
        
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: typeStats.type.icon)
        iconImageView.tintColor = typeStats.type.color
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        
        let nameLabel = UILabel()
        nameLabel.text = typeStats.type.displayName
        nameLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let countLabel = UILabel()
        countLabel.text = "\(typeStats.count) 次"
        countLabel.font = UIFont.systemFont(ofSize: 12)
        countLabel.textColor = .secondaryLabel
        countLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let percentageLabel = UILabel()
        percentageLabel.text = String(format: "%.1f%%", typeStats.percentage)
        percentageLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        percentageLabel.textColor = typeStats.type.color
        percentageLabel.textAlignment = .right
        percentageLabel.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(iconImageView)
        container.addSubview(nameLabel)
        container.addSubview(countLabel)
        container.addSubview(percentageLabel)
        
        NSLayoutConstraint.activate([
            iconImageView.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 15),
            iconImageView.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            iconImageView.widthAnchor.constraint(equalToConstant: 24),
            iconImageView.heightAnchor.constraint(equalToConstant: 24),
            
            nameLabel.leadingAnchor.constraint(equalTo: iconImageView.trailingAnchor, constant: 12),
            nameLabel.topAnchor.constraint(equalTo: container.topAnchor, constant: 8),
            
            countLabel.leadingAnchor.constraint(equalTo: nameLabel.leadingAnchor),
            countLabel.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 2),
            countLabel.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -8),
            
            percentageLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -15),
            percentageLabel.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            percentageLabel.leadingAnchor.constraint(greaterThanOrEqualTo: nameLabel.trailingAnchor, constant: 10),
            
            container.heightAnchor.constraint(equalToConstant: 50)
        ])
        
        return container
    }
    
    private func createRecordItem(title: String, subtitle: String, value: String, icon: String) -> UIView {
        let container = UIView()
        container.backgroundColor = .tertiarySystemBackground
        container.layer.cornerRadius = 8
        
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = .systemRed
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.font = UIFont.systemFont(ofSize: 12)
        subtitleLabel.textColor = .secondaryLabel
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = UIFont.systemFont(ofSize: 14, weight: .bold)
        valueLabel.textColor = .systemRed
        valueLabel.textAlignment = .right
        valueLabel.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(iconImageView)
        container.addSubview(titleLabel)
        container.addSubview(subtitleLabel)
        container.addSubview(valueLabel)
        
        NSLayoutConstraint.activate([
            iconImageView.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 15),
            iconImageView.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            iconImageView.widthAnchor.constraint(equalToConstant: 24),
            iconImageView.heightAnchor.constraint(equalToConstant: 24),
            
            titleLabel.leadingAnchor.constraint(equalTo: iconImageView.trailingAnchor, constant: 12),
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor, constant: 8),
            
            subtitleLabel.leadingAnchor.constraint(equalTo: titleLabel.leadingAnchor),
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 2),
            subtitleLabel.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -8),
            
            valueLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -15),
            valueLabel.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            valueLabel.leadingAnchor.constraint(greaterThanOrEqualTo: titleLabel.trailingAnchor, constant: 10),
            
            container.heightAnchor.constraint(equalToConstant: 50)
        ])
        
        return container
    }
    
    // MARK: - Actions
    @objc private func doneTapped() {
        dismiss(animated: true)
    }
} 