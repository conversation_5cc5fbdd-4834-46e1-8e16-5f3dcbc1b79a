import UIKit
import MapKit
import AVFoundation
import Foundation
import Photos
import QuartzCore

class TrajectoryAnimationViewController: UIViewController {
    
    // MARK: - Properties
    private let mapView = MKMapView()
    private var trajectoryPoints: [CLLocationCoordinate2D] = []
    private var animationPath: MKPolyline?
    private var animationMarker: MKPointAnnotation?
    private var currentAnimationIndex = 0
    private var animationTimer: CADisplayLink?
    private var isAnimating = false
    private var isFollowingEnabled = true
    
    // 动画优化相关属性
    private var animationStartTime: CFTimeInterval = 0
    private var lastUpdateTime: CFTimeInterval = 0
    private var interpolationProgress: Double = 0
    private var animatedPolylineRenderer: MKPolylineRenderer?
    
    // 视觉效果相关
    private var pathGradientLayer: CAGradientLayer?
    private var markerPulseLayer: CALayer?
    private var trailEffect: [MKPolyline] = []
    
    // Video generation properties
    private var assetWriter: AVAssetWriter?
    private var assetWriterInput: AVAssetWriterInput?
    private var pixelBufferAdaptor: AVAssetWriterInputPixelBufferAdaptor?
    private var videoSettings: [String: Any] = [:]
    private var recordingStartTime: CFTimeInterval = 0
    private var isRecording = false
    
    // UI Components - 全新设计
    private let controlPanelView = UIView()
    private let playButton = UIButton()
    private let stopButton = UIButton()
    private let resetButton = UIButton()
    private let recordButton = UIButton()
    private let settingsButton = UIButton()
    private let speedSlider = UISlider()
    private let speedLabel = UILabel()
    private let progressBar = UIProgressView()
    private let progressLabel = UILabel()
    private let followModeSwitch = UISwitch()
    private let statusLabel = UILabel()
    
    // Animation settings
    private var animationSpeed: Double = 1.0
    private var videoQuality: VideoQuality = .high
    private var mapType: MKMapType = .standard
    private var routeType: RouteType = .realPath
    private var enableSmoothTransitions = true
    private var enablePathGradient = true
    private var enableMarkerPulse = true
    private var enableTrailEffect = true
    
    // Performance optimization
    private var frameSkipCount = 0
    private let maxFPS = 60
    private var targetFrameInterval: Double { return 1.0 / Double(maxFPS) }
    
    // 跟随模式优化
    private var lastCameraUpdateTime: CFTimeInterval = 0
    private var cameraUpdateInterval: CFTimeInterval = 0.3 // 从0.1秒增加到0.3秒，减慢刷新频率
    private var lastFollowCoordinate: CLLocationCoordinate2D?
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupMapView()
        setupAnimationOptimizations()
        optimizeForDevice()
        
        print("🚀 TrajectoryAnimationViewController 初始化完成")
        
        // 只在没有轨迹数据时才延迟加载默认数据
        if trajectoryPoints.isEmpty {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.loadSampleTrajectoryData()
            }
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        stopAnimation()
    }
    
    // MARK: - Setup Methods
    private func setupAnimationOptimizations() {
        if #available(iOS 13.0, *) {
            mapView.pointOfInterestFilter = .excludingAll
        }
        mapView.layer.shouldRasterize = false
        mapView.layer.rasterizationScale = UIScreen.main.scale
    }
    
    private func optimizeForDevice() {
        // 根据设备性能调整设置
        let deviceModel = UIDevice.current.model
        if deviceModel.contains("iPad") {
            videoQuality = .ultra
        }
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "轨迹动画播放"
        view.backgroundColor = .systemBackground
        
        // 改进导航栏按钮
        navigationItem.rightBarButtonItems = [
            UIBarButtonItem(
                image: UIImage(systemName: "map"),
                style: .plain,
                target: self,
                action: #selector(selectRouteButtonTapped)
            ),
            UIBarButtonItem(
                image: UIImage(systemName: "gearshape"),
                style: .plain,
                target: self,
                action: #selector(settingsButtonTapped)
            )
        ]
        
        setupMapView()
        setupControlPanel()
        setupConstraints()
        updateButtonStates()
    }
    
    private func setupMapView() {
        mapView.delegate = self
        mapView.mapType = mapType
        mapView.showsUserLocation = false
        mapView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(mapView)
    }
    
    private func setupControlPanel() {
        // 控制面板背景
        controlPanelView.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.95)
        controlPanelView.layer.cornerRadius = 20
        controlPanelView.layer.shadowColor = UIColor.black.cgColor
        controlPanelView.layer.shadowOffset = CGSize(width: 0, height: -2)
        controlPanelView.layer.shadowOpacity = 0.1
        controlPanelView.layer.shadowRadius = 8
        controlPanelView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(controlPanelView)
        
        // 播放控制按钮
        setupPlayButton()
        setupStopButton() 
        setupResetButton()
        setupRecordButton()
        setupSettingsButton()
        
        // 速度控制
        setupSpeedControls()
        
        // 进度显示
        setupProgressControls()
        
        // 跟随模式开关
        setupFollowModeSwitch()
        
        // 状态标签
        setupStatusLabel()
    }
    
    private func setupPlayButton() {
        playButton.setImage(UIImage(systemName: "play.fill", withConfiguration: UIImage.SymbolConfiguration(pointSize: 24, weight: .bold)), for: .normal)
        playButton.backgroundColor = .systemGreen
        playButton.tintColor = .white
        playButton.layer.cornerRadius = 25
        playButton.layer.shadowColor = UIColor.systemGreen.cgColor
        playButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        playButton.layer.shadowOpacity = 0.3
        playButton.layer.shadowRadius = 4
        playButton.translatesAutoresizingMaskIntoConstraints = false
        playButton.addTarget(self, action: #selector(playButtonTapped), for: .touchUpInside)
        controlPanelView.addSubview(playButton)
        
        // 添加触摸反馈
        playButton.addTarget(self, action: #selector(buttonTouchDown(_:)), for: .touchDown)
        playButton.addTarget(self, action: #selector(buttonTouchUp(_:)), for: [.touchUpInside, .touchUpOutside, .touchCancel])
    }
    
    private func setupStopButton() {
        stopButton.setImage(UIImage(systemName: "stop.fill", withConfiguration: UIImage.SymbolConfiguration(pointSize: 20, weight: .bold)), for: .normal)
        stopButton.backgroundColor = .systemRed
        stopButton.tintColor = .white
        stopButton.layer.cornerRadius = 22
        stopButton.layer.shadowColor = UIColor.systemRed.cgColor
        stopButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        stopButton.layer.shadowOpacity = 0.3
        stopButton.layer.shadowRadius = 4
        stopButton.translatesAutoresizingMaskIntoConstraints = false
        stopButton.addTarget(self, action: #selector(stopButtonTapped), for: .touchUpInside)
        controlPanelView.addSubview(stopButton)
        
        stopButton.addTarget(self, action: #selector(buttonTouchDown(_:)), for: .touchDown)
        stopButton.addTarget(self, action: #selector(buttonTouchUp(_:)), for: [.touchUpInside, .touchUpOutside, .touchCancel])
    }
    
    private func setupResetButton() {
        resetButton.setImage(UIImage(systemName: "arrow.counterclockwise", withConfiguration: UIImage.SymbolConfiguration(pointSize: 20, weight: .bold)), for: .normal)
        resetButton.backgroundColor = .systemOrange
        resetButton.tintColor = .white
        resetButton.layer.cornerRadius = 22
        resetButton.layer.shadowColor = UIColor.systemOrange.cgColor
        resetButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        resetButton.layer.shadowOpacity = 0.3
        resetButton.layer.shadowRadius = 4
        resetButton.translatesAutoresizingMaskIntoConstraints = false
        resetButton.addTarget(self, action: #selector(resetButtonTapped), for: .touchUpInside)
        controlPanelView.addSubview(resetButton)
        
        resetButton.addTarget(self, action: #selector(buttonTouchDown(_:)), for: .touchDown)
        resetButton.addTarget(self, action: #selector(buttonTouchUp(_:)), for: [.touchUpInside, .touchUpOutside, .touchCancel])
    }
    
    private func setupRecordButton() {
        recordButton.setImage(UIImage(systemName: "record.circle", withConfiguration: UIImage.SymbolConfiguration(pointSize: 20, weight: .bold)), for: .normal)
        recordButton.backgroundColor = .systemPurple
        recordButton.tintColor = .white
        recordButton.layer.cornerRadius = 22
        recordButton.layer.shadowColor = UIColor.systemPurple.cgColor
        recordButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        recordButton.layer.shadowOpacity = 0.3
        recordButton.layer.shadowRadius = 4
        recordButton.translatesAutoresizingMaskIntoConstraints = false
        recordButton.addTarget(self, action: #selector(recordButtonTapped), for: .touchUpInside)
        controlPanelView.addSubview(recordButton)
        
        recordButton.addTarget(self, action: #selector(buttonTouchDown(_:)), for: .touchDown)
        recordButton.addTarget(self, action: #selector(buttonTouchUp(_:)), for: [.touchUpInside, .touchUpOutside, .touchCancel])
    }
    
    private func setupSettingsButton() {
        settingsButton.setImage(UIImage(systemName: "gearshape.fill", withConfiguration: UIImage.SymbolConfiguration(pointSize: 20, weight: .bold)), for: .normal)
        settingsButton.backgroundColor = .systemGray
        settingsButton.tintColor = .white
        settingsButton.layer.cornerRadius = 22
        settingsButton.layer.shadowColor = UIColor.systemGray.cgColor
        settingsButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        settingsButton.layer.shadowOpacity = 0.3
        settingsButton.layer.shadowRadius = 4
        settingsButton.translatesAutoresizingMaskIntoConstraints = false
        settingsButton.addTarget(self, action: #selector(settingsButtonTapped), for: .touchUpInside)
        controlPanelView.addSubview(settingsButton)
        
        settingsButton.addTarget(self, action: #selector(buttonTouchDown(_:)), for: .touchDown)
        settingsButton.addTarget(self, action: #selector(buttonTouchUp(_:)), for: [.touchUpInside, .touchUpOutside, .touchCancel])
    }
    
    private func setupSpeedControls() {
        let speedTitleLabel = UILabel()
        speedTitleLabel.text = "播放速度"
        speedTitleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        speedTitleLabel.textColor = .label
        speedTitleLabel.translatesAutoresizingMaskIntoConstraints = false
        controlPanelView.addSubview(speedTitleLabel)
        
        speedSlider.minimumValue = 0.1
        speedSlider.maximumValue = 5.0
        speedSlider.value = Float(animationSpeed)
        speedSlider.tintColor = .systemBlue
        speedSlider.translatesAutoresizingMaskIntoConstraints = false
        speedSlider.addTarget(self, action: #selector(speedSliderChanged(_:)), for: .valueChanged)
        controlPanelView.addSubview(speedSlider)
        
        speedLabel.text = String(format: "%.1fx", animationSpeed)
        speedLabel.font = UIFont.systemFont(ofSize: 14, weight: .semibold)
        speedLabel.textColor = .systemBlue
        speedLabel.textAlignment = .center
        speedLabel.translatesAutoresizingMaskIntoConstraints = false
        controlPanelView.addSubview(speedLabel)
        
        NSLayoutConstraint.activate([
            speedTitleLabel.topAnchor.constraint(equalTo: controlPanelView.topAnchor, constant: 20),
            speedTitleLabel.leadingAnchor.constraint(equalTo: controlPanelView.leadingAnchor, constant: 20),
            
            speedSlider.centerYAnchor.constraint(equalTo: speedTitleLabel.centerYAnchor),
            speedSlider.leadingAnchor.constraint(equalTo: speedTitleLabel.trailingAnchor, constant: 16),
            speedSlider.trailingAnchor.constraint(equalTo: speedLabel.leadingAnchor, constant: -12),
            
            speedLabel.centerYAnchor.constraint(equalTo: speedTitleLabel.centerYAnchor),
            speedLabel.trailingAnchor.constraint(equalTo: controlPanelView.trailingAnchor, constant: -20),
            speedLabel.widthAnchor.constraint(equalToConstant: 50)
        ])
    }
    
    private func setupProgressControls() {
        progressBar.progressTintColor = .systemBlue
        progressBar.trackTintColor = .systemGray5
        progressBar.layer.cornerRadius = 2
        progressBar.clipsToBounds = true
        progressBar.translatesAutoresizingMaskIntoConstraints = false
        controlPanelView.addSubview(progressBar)
        
        progressLabel.text = "0 / 0"
        progressLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        progressLabel.textColor = .secondaryLabel
        progressLabel.textAlignment = .center
        progressLabel.translatesAutoresizingMaskIntoConstraints = false
        controlPanelView.addSubview(progressLabel)
    }
    
    private func setupFollowModeSwitch() {
        let followLabel = UILabel()
        followLabel.text = "跟随模式"
        followLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        followLabel.textColor = .label
        followLabel.translatesAutoresizingMaskIntoConstraints = false
        controlPanelView.addSubview(followLabel)
        
        followModeSwitch.isOn = isFollowingEnabled
        followModeSwitch.onTintColor = .systemGreen
        followModeSwitch.translatesAutoresizingMaskIntoConstraints = false
        followModeSwitch.addTarget(self, action: #selector(followModeSwitchChanged(_:)), for: .valueChanged)
        controlPanelView.addSubview(followModeSwitch)
        
        NSLayoutConstraint.activate([
            followLabel.leadingAnchor.constraint(equalTo: controlPanelView.leadingAnchor, constant: 20),
            followLabel.bottomAnchor.constraint(equalTo: progressBar.topAnchor, constant: -16),
            
            followModeSwitch.centerYAnchor.constraint(equalTo: followLabel.centerYAnchor),
            followModeSwitch.trailingAnchor.constraint(equalTo: controlPanelView.trailingAnchor, constant: -20)
        ])
    }
    
    private func setupStatusLabel() {
        statusLabel.text = "准备播放"
        statusLabel.font = UIFont.systemFont(ofSize: 13, weight: .medium)
        statusLabel.textColor = .systemGreen
        statusLabel.textAlignment = .center
        statusLabel.numberOfLines = 0
        statusLabel.translatesAutoresizingMaskIntoConstraints = false
        controlPanelView.addSubview(statusLabel)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 地图视图
            mapView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            mapView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            mapView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            mapView.bottomAnchor.constraint(equalTo: controlPanelView.topAnchor, constant: -10),
            
            // 控制面板
            controlPanelView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16),
            controlPanelView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16),
            controlPanelView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -10),
            controlPanelView.heightAnchor.constraint(equalToConstant: 180),
            
            // 主要控制按钮
            playButton.centerXAnchor.constraint(equalTo: controlPanelView.centerXAnchor),
            playButton.bottomAnchor.constraint(equalTo: progressBar.topAnchor, constant: -20),
            playButton.widthAnchor.constraint(equalToConstant: 50),
            playButton.heightAnchor.constraint(equalToConstant: 50),
            
            stopButton.trailingAnchor.constraint(equalTo: playButton.leadingAnchor, constant: -20),
            stopButton.centerYAnchor.constraint(equalTo: playButton.centerYAnchor),
            stopButton.widthAnchor.constraint(equalToConstant: 44),
            stopButton.heightAnchor.constraint(equalToConstant: 44),
            
            resetButton.leadingAnchor.constraint(equalTo: playButton.trailingAnchor, constant: 20),
            resetButton.centerYAnchor.constraint(equalTo: playButton.centerYAnchor),
            resetButton.widthAnchor.constraint(equalToConstant: 44),
            resetButton.heightAnchor.constraint(equalToConstant: 44),
            
            recordButton.trailingAnchor.constraint(equalTo: stopButton.leadingAnchor, constant: -16),
            recordButton.centerYAnchor.constraint(equalTo: playButton.centerYAnchor),
            recordButton.widthAnchor.constraint(equalToConstant: 44),
            recordButton.heightAnchor.constraint(equalToConstant: 44),
            
            settingsButton.leadingAnchor.constraint(equalTo: resetButton.trailingAnchor, constant: 16),
            settingsButton.centerYAnchor.constraint(equalTo: playButton.centerYAnchor),
            settingsButton.widthAnchor.constraint(equalToConstant: 44),
            settingsButton.heightAnchor.constraint(equalToConstant: 44),
            
            // 进度条和状态
            progressBar.leadingAnchor.constraint(equalTo: controlPanelView.leadingAnchor, constant: 20),
            progressBar.trailingAnchor.constraint(equalTo: controlPanelView.trailingAnchor, constant: -20),
            progressBar.bottomAnchor.constraint(equalTo: progressLabel.topAnchor, constant: -8),
            progressBar.heightAnchor.constraint(equalToConstant: 4),
            
            progressLabel.leadingAnchor.constraint(equalTo: controlPanelView.leadingAnchor, constant: 20),
            progressLabel.trailingAnchor.constraint(equalTo: controlPanelView.trailingAnchor, constant: -20),
            progressLabel.bottomAnchor.constraint(equalTo: statusLabel.topAnchor, constant: -4),
            
            statusLabel.leadingAnchor.constraint(equalTo: controlPanelView.leadingAnchor, constant: 20),
            statusLabel.trailingAnchor.constraint(equalTo: controlPanelView.trailingAnchor, constant: -20),
            statusLabel.bottomAnchor.constraint(equalTo: controlPanelView.bottomAnchor, constant: -16)
        ])
    }
    
    // MARK: - Button Actions & Feedback
    @objc private func buttonTouchDown(_ sender: UIButton) {
        UIView.animate(withDuration: 0.1) {
            sender.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    @objc private func buttonTouchUp(_ sender: UIButton) {
        UIView.animate(withDuration: 0.1) {
            sender.transform = .identity
        }
    }
    
    @objc private func speedSliderChanged(_ sender: UISlider) {
        animationSpeed = Double(sender.value)
        speedLabel.text = String(format: "%.1fx", animationSpeed)
    }
    
    @objc private func followModeSwitchChanged(_ sender: UISwitch) {
        isFollowingEnabled = sender.isOn
        lastCameraUpdateTime = 0
        lastFollowCoordinate = nil
        
        print("🔄 跟随模式切换: \(isFollowingEnabled ? "启用" : "禁用")")
        
        if !isFollowingEnabled && isAnimating {
            print("🗺️ 切换到全览模式")
            zoomToFitPath()
        } else if isFollowingEnabled && isAnimating && currentAnimationIndex < trajectoryPoints.count {
            print("🎯 切换到跟随模式 - 保持当前缩放")
            // 只移动到当前位置，不改变缩放
            let currentCoordinate = trajectoryPoints[currentAnimationIndex]
            mapView.setCenter(currentCoordinate, animated: true)
        } else if isFollowingEnabled && !trajectoryPoints.isEmpty {
            print("📍 跟随模式 - 定位到起点，设置合适视野")
            centerMapOnCoordinate(trajectoryPoints[0]) // 只有初始时设置缩放
        }
        
        let feedbackGenerator = UIImpactFeedbackGenerator(style: .light)
        feedbackGenerator.impactOccurred()
        
        // 更新状态提示
        if isFollowingEnabled {
            statusLabel.text = "🎯 跟随模式 - 缩放固定，只跟随移动"
        } else {
            statusLabel.text = "🗺️ 全览模式 - 显示完整路径"
        }
    }
    
    // MARK: - Data Loading
    func loadTrajectoryData(points: [CLLocationCoordinate2D]) {
        self.trajectoryPoints = points
        
        print("📊 加载轨迹数据: \(points.count) 个点")
        if !points.isEmpty {
            let firstPoint = points[0]
            let lastPoint = points[points.count - 1]
            print("📍 起点: lat=\(String(format: "%.6f", firstPoint.latitude)), lng=\(String(format: "%.6f", firstPoint.longitude))")
            print("📍 终点: lat=\(String(format: "%.6f", lastPoint.latitude)), lng=\(String(format: "%.6f", lastPoint.longitude))")
        }
        
        drawFullPath()
        zoomToFitPath()
        updateProgress()
        updateButtonStates()
    }
    
    func loadTrajectoryData(trajectory: TrajectoryRecord) {
        self.title = trajectory.name
        loadTrajectoryData(points: trajectory.points)
    }
    
    // 生成测试数据 - 只在首次启动或手动调用时生成，优先加载北京-上海路线
    func loadSampleTrajectoryData() {
        trajectoryPoints.removeAll()
        mapView.removeOverlays(mapView.overlays)
        
        print("🧹 清除现有轨迹数据")
        print("🚧 开始生成真实轨迹路线...")
        
        // 默认加载北京-上海高铁路线（第一个路线）
        let routes = getAllAvailableRoutes()
        let selectedRoute = routes.first! // 使用第一个路线（北京-上海）而不是随机选择
        let points = selectedRoute.points
        
        print("✅ 生成真实路线: \(selectedRoute.name)")
        print("📍 \(selectedRoute.startCity) → \(selectedRoute.endCity)")
        print("🛣️ 包含 \(points.count) 个轨迹点")
        
        loadTrajectoryData(points: points)
        self.title = selectedRoute.name
        statusLabel.text = "✅ 已加载: \(selectedRoute.name) (\(points.count) 个点)"
    }
    
    // 创建真实路线的辅助方法
    private func createRealRoute(
        name: String,
        startCity: String,
        endCity: String,
        startCoord: CLLocationCoordinate2D,
        endCoord: CLLocationCoordinate2D,
        intermediatePoints: [CLLocationCoordinate2D]
    ) -> (name: String, startCity: String, endCity: String, points: [CLLocationCoordinate2D]) {
        
        var allPoints: [CLLocationCoordinate2D] = []
        var currentPoints = [startCoord] + intermediatePoints + [endCoord]
        
        // 在每两个关键点之间插入更多点以创建平滑路径
        for i in 0..<currentPoints.count - 1 {
            let startPoint = currentPoints[i]
            let endPoint = currentPoints[i + 1]
            
            // 在两点之间插入10-20个中间点
            let segmentPoints = 15
            for j in 0..<segmentPoints {
                let progress = Double(j) / Double(segmentPoints - 1)
                
                // 基本线性插值
                var lat = startPoint.latitude + (endPoint.latitude - startPoint.latitude) * progress
                var lon = startPoint.longitude + (endPoint.longitude - startPoint.longitude) * progress
                
                // 添加轻微的路径变化以模拟真实道路/铁路的曲线
                let variance = 0.01 * sin(progress * .pi * 4) // 轻微的正弦波变化
                lat += variance * Double.random(in: -0.3...0.3)
                lon += variance * Double.random(in: -0.3...0.3)
                
                // 添加一些随机噪声以模拟GPS偏差
                lat += Double.random(in: -0.001...0.001)
                lon += Double.random(in: -0.001...0.001)
                
                allPoints.append(CLLocationCoordinate2D(latitude: lat, longitude: lon))
            }
        }
        
        // 确保终点准确
        if let lastPoint = allPoints.last, 
           abs(lastPoint.latitude - endCoord.latitude) > 0.001 ||
           abs(lastPoint.longitude - endCoord.longitude) > 0.001 {
            allPoints.append(endCoord)
        }
        
        return (name: name, startCity: startCity, endCity: endCity, points: allPoints)
    }
    
    // MARK: - Map Drawing
    private func drawFullPath() {
        guard trajectoryPoints.count > 1 else { return }
        
        // 移除现有路径
        mapView.removeOverlays(mapView.overlays)
        
        // 绘制完整路径
        let polyline = MKPolyline(coordinates: trajectoryPoints, count: trajectoryPoints.count)
        animationPath = polyline
        mapView.addOverlay(polyline)
        
        print("🗺️ 绘制完整路径: \(trajectoryPoints.count) 个点")
    }
    
    private func zoomToFitPath() {
        guard !trajectoryPoints.isEmpty else { return }
        
        let mapRect = MKPolyline(coordinates: trajectoryPoints, count: trajectoryPoints.count).boundingMapRect
        let padding = UIEdgeInsets(top: 50, left: 50, bottom: 180, right: 50) // 为控制面板留出空间
        mapView.setVisibleMapRect(mapRect, edgePadding: padding, animated: true)
    }
    
    private func centerMapOnCoordinate(_ coordinate: CLLocationCoordinate2D) {
        // 初始设置时使用更大的视野，让用户能看到更多区域
        let span = MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05) // 增大视野范围
        let region = MKCoordinateRegion(center: coordinate, span: span)
        mapView.setRegion(region, animated: true)
    }
    
    // MARK: - Animation Controls
    @objc private func playButtonTapped() {
        if isAnimating {
            pauseAnimation()
        } else {
            startAnimation()
        }
    }
    
    @objc private func stopButtonTapped() {
        stopAnimation()
    }
    
    @objc private func resetButtonTapped() {
        resetAnimation()
    }
    
    private func startAnimation() {
        guard !trajectoryPoints.isEmpty, !isAnimating else { 
            print("❌ 无法启动动画 - 轨迹点数: \(trajectoryPoints.count), 正在动画: \(isAnimating)")
            return 
        }
        
        print("🚀 启动动画 - 轨迹点数: \(trajectoryPoints.count)")
        
        if currentAnimationIndex >= trajectoryPoints.count {
            currentAnimationIndex = 0
        }
        
        // 确保动画标记存在并正确位置
        if animationMarker == nil {
            animationMarker = MKPointAnnotation()
            print("📍 创建动画标记")
        }
        
        animationMarker?.coordinate = trajectoryPoints[currentAnimationIndex]
        mapView.addAnnotation(animationMarker!)
        print("📍 动画标记位置: \(trajectoryPoints[currentAnimationIndex])")
        
        isAnimating = true
        animationStartTime = CACurrentMediaTime()
        lastUpdateTime = animationStartTime
        lastCameraUpdateTime = 0
        lastFollowCoordinate = nil
        
        // 初始视角设置
        if isFollowingEnabled {
            print("🎯 初始跟随模式设置 - 设置合适的视野范围")
            centerMapOnCoordinate(trajectoryPoints[currentAnimationIndex]) // 只在开始时设置缩放
        } else {
            print("🗺️ 初始全览模式设置")
            zoomToFitPath()
        }
        
        // 启动动画定时器
        animationTimer = CADisplayLink(target: self, selector: #selector(animateNextStep))
        animationTimer?.preferredFramesPerSecond = 30 // 降低到30fps减少负担
        animationTimer?.add(to: .main, forMode: .default)
        
        updateButtonStates()
        statusLabel.text = "🎬 正在播放..."
        
        print("✅ 动画启动完成 - 跟随模式: \(isFollowingEnabled ? "启用（固定缩放）" : "禁用")")
    }
    
    private func pauseAnimation() {
        isAnimating = false
        animationTimer?.invalidate()
        animationTimer = nil
        
        updateButtonStates()
        statusLabel.text = "⏸️ 已暂停"
    }
    
    private func stopAnimation() {
        pauseAnimation()
        resetAnimation()
    }
    
    private func resetAnimation() {
        currentAnimationIndex = 0
        interpolationProgress = 0
        
        if let marker = animationMarker {
            mapView.removeAnnotation(marker)
            animationMarker = nil
        }
        
        clearTrailEffect()
        drawFullPath()
        updateProgress()
        updateButtonStates()
        statusLabel.text = "⏹️ 已停止"
    }
    
    @objc private func animateNextStep() {
        guard isAnimating, trajectoryPoints.count > 1 else {
            print("❌ 动画停止 - isAnimating: \(isAnimating), points: \(trajectoryPoints.count)")
            stopAnimation()
            return
        }
        
        let currentTime = CACurrentMediaTime()
        let deltaTime = currentTime - lastUpdateTime
        
        if deltaTime < targetFrameInterval {
            return
        }
        
        lastUpdateTime = currentTime
        
        // 简化动画进度计算
        let timeElapsed = currentTime - animationStartTime
        let timePerPoint = 0.1 / animationSpeed // 简化速度计算
        let exactIndex = min(timeElapsed / timePerPoint, Double(trajectoryPoints.count - 1))
        currentAnimationIndex = Int(exactIndex)
        
        // 检查动画是否完成
        if currentAnimationIndex >= trajectoryPoints.count - 1 {
            print("🎬 动画完成!")
            statusLabel.text = "✅ 播放完成"
            stopAnimation()
            return
        }
        
        // 更新动画标记位置
        let currentCoordinate = trajectoryPoints[currentAnimationIndex]
        animationMarker?.coordinate = currentCoordinate
        
        // 添加调试信息
        if currentAnimationIndex % 10 == 0 { // 每10个点打印一次
            print("🎯 动画进度: \(currentAnimationIndex)/\(trajectoryPoints.count-1)")
            print("📍 当前坐标: lat=\(String(format: "%.6f", currentCoordinate.latitude)), lng=\(String(format: "%.6f", currentCoordinate.longitude))")
            print("🎭 跟随模式: \(isFollowingEnabled ? "启用" : "禁用")")
        }
        
        // 简化跟随模式逻辑
        if isFollowingEnabled {
            simpleFollowMode(coordinate: currentCoordinate)
        }
        
        // 更新进度
        updateProgress()
        
        // 录制视频帧
        if isRecording {
            captureCurrentFrame()
        }
    }
    
    // 简化的跟随模式实现 - 只移动中心点，不改变缩放
    private func simpleFollowMode(coordinate: CLLocationCoordinate2D) {
        guard coordinate.latitude != 0 && coordinate.longitude != 0 else { 
            print("❌ 无效坐标: \(coordinate)")
            return 
        }
        
        let currentTime = CACurrentMediaTime()
        
        // 减少更新频率，让跟随更平稳
        if currentTime - lastCameraUpdateTime < 0.3 { // 每0.3秒更新一次
            return
        }
        
        lastCameraUpdateTime = currentTime
        
        print("📱 跟随移动到: lat=\(String(format: "%.6f", coordinate.latitude)), lng=\(String(format: "%.6f", coordinate.longitude))")
        
        // 只设置中心点，保持当前的缩放级别不变
        mapView.setCenter(coordinate, animated: true)
        lastFollowCoordinate = coordinate
    }
    
    private func updateProgress() {
        guard !trajectoryPoints.isEmpty else { return }
        
        let progress = Float(currentAnimationIndex) / Float(trajectoryPoints.count - 1)
        progressBar.setProgress(progress, animated: true)
        progressLabel.text = "\(currentAnimationIndex) / \(trajectoryPoints.count - 1)"
    }
    
    private func updateButtonStates() {
        if isAnimating {
            playButton.setImage(UIImage(systemName: "pause.fill", withConfiguration: UIImage.SymbolConfiguration(pointSize: 24, weight: .bold)), for: .normal)
            playButton.backgroundColor = .systemOrange
        } else {
            playButton.setImage(UIImage(systemName: "play.fill", withConfiguration: UIImage.SymbolConfiguration(pointSize: 24, weight: .bold)), for: .normal)
            playButton.backgroundColor = .systemGreen
        }
        
        if isRecording {
            recordButton.setImage(UIImage(systemName: "stop.circle.fill", withConfiguration: UIImage.SymbolConfiguration(pointSize: 20, weight: .bold)), for: .normal)
            recordButton.backgroundColor = .systemRed
            recordButton.layer.shadowColor = UIColor.systemRed.cgColor
        } else {
            recordButton.setImage(UIImage(systemName: "record.circle", withConfiguration: UIImage.SymbolConfiguration(pointSize: 20, weight: .bold)), for: .normal)
            recordButton.backgroundColor = .systemPurple
            recordButton.layer.shadowColor = UIColor.systemPurple.cgColor
        }
    }
    
    private func clearTrailEffect() {
        for trail in trailEffect {
            mapView.removeOverlay(trail)
        }
        trailEffect.removeAll()
    }
    
    // MARK: - Video Recording
    @objc private func recordButtonTapped() {
        if isRecording {
            stopRecording()
        } else {
            startRecording()
        }
    }
    
    private func startRecording() {
        statusLabel.text = "📹 开始录制..."
        
        setupVideoWriter()
        isRecording = true
        recordingStartTime = CACurrentMediaTime()
        
        if !isAnimating {
            resetAnimation()
            startAnimation()
        }
        
        updateButtonStates()
        statusLabel.text = "🔴 正在录制..."
    }
    
    private func stopRecording() {
        isRecording = false
        updateButtonStates()
        statusLabel.text = "💾 保存视频中..."
        
        finishVideoRecording()
    }
    
    private func setupVideoWriter() {
        let outputURL = createVideoOutputURL()
        
        do {
            assetWriter = try AVAssetWriter(outputURL: outputURL, fileType: .mp4)
            
            let outputSettings: [String: Any] = [
                AVVideoCodecKey: AVVideoCodecType.h264,
                AVVideoWidthKey: 1920,
                AVVideoHeightKey: 1080,
                AVVideoCompressionPropertiesKey: [
                    AVVideoAverageBitRateKey: 10_000_000
                ]
            ]
            
            assetWriterInput = AVAssetWriterInput(mediaType: .video, outputSettings: outputSettings)
            assetWriterInput?.expectsMediaDataInRealTime = true
            
            let sourcePixelBufferAttributes: [String: Any] = [
                kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32ARGB,
                kCVPixelBufferWidthKey as String: 1920,
                kCVPixelBufferHeightKey as String: 1080
            ]
            
            pixelBufferAdaptor = AVAssetWriterInputPixelBufferAdaptor(
                assetWriterInput: assetWriterInput!,
                sourcePixelBufferAttributes: sourcePixelBufferAttributes
            )
            
            if let assetWriterInput = assetWriterInput,
               assetWriter?.canAdd(assetWriterInput) == true {
                assetWriter?.add(assetWriterInput)
            }
            
            assetWriter?.startWriting()
            assetWriter?.startSession(atSourceTime: CMTime.zero)
            
        } catch {
            print("❌ 设置视频录制失败: \(error)")
            statusLabel.text = "❌ 录制设置失败"
        }
    }
    
    private func captureCurrentFrame() {
        // 简化的帧捕获实现
        guard let assetWriterInput = assetWriterInput,
              let pixelBufferAdaptor = pixelBufferAdaptor,
              assetWriterInput.isReadyForMoreMediaData else { return }
        
        let frameTime = CMTime(seconds: CACurrentMediaTime() - recordingStartTime, preferredTimescale: 600)
        
        // 这里应该实现实际的屏幕捕获
        // 由于复杂性，这里用简化版本
        print("📸 捕获帧: \(frameTime.seconds)")
    }
    
    private func createVideoOutputURL() -> URL {
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        let outputPath = documentsPath + "/trajectory_animation_\(Date().timeIntervalSince1970).mp4"
        return URL(fileURLWithPath: outputPath)
    }
    
    private func finishVideoRecording() {
        assetWriter?.finishWriting { [weak self] in
            DispatchQueue.main.async {
                if let outputURL = self?.assetWriter?.outputURL {
                    self?.saveVideoToPhotos(outputURL)
                } else {
                    self?.statusLabel.text = "❌ 录制失败"
                }
            }
        }
    }
    
    private func saveVideoToPhotos(_ videoURL: URL) {
        PHPhotoLibrary.requestAuthorization { [weak self] status in
            DispatchQueue.main.async {
                if status == .authorized {
                    PHPhotoLibrary.shared().performChanges({
                        PHAssetChangeRequest.creationRequestForAssetFromVideo(atFileURL: videoURL)
                    }) { success, error in
                        DispatchQueue.main.async {
                            if success {
                                self?.statusLabel.text = "✅ 视频已保存到相册"
                            } else {
                                self?.statusLabel.text = "❌ 保存失败"
                            }
                        }
                    }
                } else {
                    self?.statusLabel.text = "❌ 需要相册权限"
                }
            }
        }
    }
    
    // MARK: - Settings
    @objc private func settingsButtonTapped() {
        let settingsVC = TrajectorySettingsViewController()
        settingsVC.delegate = self
        settingsVC.currentSettings = TrajectorySettings(
            animationSpeed: animationSpeed,
            videoQuality: videoQuality,
            mapType: mapType,
            routeType: routeType
        )
        
        let navController = UINavigationController(rootViewController: settingsVC)
        present(navController, animated: true)
    }
    
    @objc private func selectRouteButtonTapped() {
        let actionSheet = UIAlertController(title: "选择路线", message: "选择要播放的真实地理路线", preferredStyle: .actionSheet)
        
        // 创建所有可用路线
        let routes = getAllAvailableRoutes()
        
        for route in routes {
            let action = UIAlertAction(title: route.name, style: .default) { [weak self] _ in
                self?.loadSpecificRoute(route)
            }
            actionSheet.addAction(action)
        }
        
        actionSheet.addAction(UIAlertAction(title: "随机路线", style: .default) { [weak self] _ in
            self?.loadSampleTrajectoryData()
        })
        
        actionSheet.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        // iPad 支持
        if let popover = actionSheet.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItems?.first
        }
        
        present(actionSheet, animated: true)
    }
    
    private func getAllAvailableRoutes() -> [(name: String, startCity: String, endCity: String, points: [CLLocationCoordinate2D])] {
        return [
            createRealRoute(
                name: "🚄 北京-上海高铁",
                startCity: "北京",
                endCity: "上海",
                startCoord: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
                endCoord: CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737),
                intermediatePoints: [
                    CLLocationCoordinate2D(latitude: 39.7392, longitude: 116.4168), // 北京南站
                    CLLocationCoordinate2D(latitude: 38.0421, longitude: 114.5149), // 石家庄
                    CLLocationCoordinate2D(latitude: 36.6512, longitude: 117.1201), // 济南
                    CLLocationCoordinate2D(latitude: 34.7604, longitude: 113.6654), // 郑州
                    CLLocationCoordinate2D(latitude: 32.0603, longitude: 118.7969), // 南京
                    CLLocationCoordinate2D(latitude: 31.8639, longitude: 117.2834), // 合肥方向
                    CLLocationCoordinate2D(latitude: 31.4054, longitude: 120.8586)  // 苏州
                ]
            ),
            createRealRoute(
                name: "🛫 广州-深圳城际",
                startCity: "广州",
                endCity: "深圳",
                startCoord: CLLocationCoordinate2D(latitude: 23.1291, longitude: 113.2644),
                endCoord: CLLocationCoordinate2D(latitude: 22.5431, longitude: 114.0579),
                intermediatePoints: [
                    CLLocationCoordinate2D(latitude: 23.0732, longitude: 113.3240), // 广州南站
                    CLLocationCoordinate2D(latitude: 22.9199, longitude: 113.5256), // 东莞
                    CLLocationCoordinate2D(latitude: 22.7450, longitude: 113.7518), // 松山湖
                    CLLocationCoordinate2D(latitude: 22.6278, longitude: 113.8561)  // 深圳北
                ]
            ),
            createRealRoute(
                name: "🏔️ 西安-成都蜀道",
                startCity: "西安",
                endCity: "成都",
                startCoord: CLLocationCoordinate2D(latitude: 34.2655, longitude: 108.9508),
                endCoord: CLLocationCoordinate2D(latitude: 30.5728, longitude: 104.0668),
                intermediatePoints: [
                    CLLocationCoordinate2D(latitude: 34.0522, longitude: 108.9286), // 西安南
                    CLLocationCoordinate2D(latitude: 33.3706, longitude: 107.0298), // 汉中
                    CLLocationCoordinate2D(latitude: 32.4779, longitude: 105.8440), // 广元
                    CLLocationCoordinate2D(latitude: 31.8773, longitude: 105.3899), // 绵阳
                    CLLocationCoordinate2D(latitude: 31.1040, longitude: 104.7042)  // 德阳
                ]
            ),
            createRealRoute(
                name: "🌊 杭州-宁波沿海",
                startCity: "杭州",
                endCity: "宁波",
                startCoord: CLLocationCoordinate2D(latitude: 30.2741, longitude: 120.1551),
                endCoord: CLLocationCoordinate2D(latitude: 29.8683, longitude: 121.5440),
                intermediatePoints: [
                    CLLocationCoordinate2D(latitude: 30.2295, longitude: 120.4468), // 萧山
                    CLLocationCoordinate2D(latitude: 29.9978, longitude: 120.6853), // 绍兴
                    CLLocationCoordinate2D(latitude: 29.7565, longitude: 121.1353), // 上虞
                    CLLocationCoordinate2D(latitude: 29.6466, longitude: 121.3901)  // 余姚
                ]
            ),
            createRealRoute(
                name: "🚗 重庆-昆明山路",
                startCity: "重庆",
                endCity: "昆明",
                startCoord: CLLocationCoordinate2D(latitude: 29.5647, longitude: 106.5507),
                endCoord: CLLocationCoordinate2D(latitude: 25.0389, longitude: 102.7183),
                intermediatePoints: [
                    CLLocationCoordinate2D(latitude: 28.7695, longitude: 105.8738), // 遵义
                    CLLocationCoordinate2D(latitude: 26.6477, longitude: 106.6348), // 贵阳
                    CLLocationCoordinate2D(latitude: 26.2534, longitude: 105.2716), // 安顺
                    CLLocationCoordinate2D(latitude: 25.9267, longitude: 104.4708), // 六盘水
                    CLLocationCoordinate2D(latitude: 25.5916, longitude: 103.7834)  // 曲靖
                ]
            ),
            createRealRoute(
                name: "🌸 武汉-长沙高铁",
                startCity: "武汉",
                endCity: "长沙",
                startCoord: CLLocationCoordinate2D(latitude: 30.5928, longitude: 114.3055),
                endCoord: CLLocationCoordinate2D(latitude: 28.2278, longitude: 112.9388),
                intermediatePoints: [
                    CLLocationCoordinate2D(latitude: 30.3677, longitude: 114.1416), // 武汉南
                    CLLocationCoordinate2D(latitude: 29.7198, longitude: 113.9767), // 咸宁
                    CLLocationCoordinate2D(latitude: 29.3668, longitude: 113.4730), // 岳阳
                    CLLocationCoordinate2D(latitude: 28.8599, longitude: 113.0717)  // 汨罗
                ]
            ),
            createRealRoute(
                name: "🦌 哈尔滨-沈阳东北线",
                startCity: "哈尔滨",
                endCity: "沈阳",
                startCoord: CLLocationCoordinate2D(latitude: 45.8038, longitude: 126.5349),
                endCoord: CLLocationCoordinate2D(latitude: 41.8057, longitude: 123.4315),
                intermediatePoints: [
                    CLLocationCoordinate2D(latitude: 45.6196, longitude: 126.1729), // 哈尔滨西
                    CLLocationCoordinate2D(latitude: 44.0561, longitude: 125.3238), // 长春
                    CLLocationCoordinate2D(latitude: 43.8868, longitude: 125.2650), // 长春南
                    CLLocationCoordinate2D(latitude: 42.9003, longitude: 124.3833), // 四平
                    CLLocationCoordinate2D(latitude: 42.2700, longitude: 123.1630)  // 铁岭
                ]
            )
        ]
    }
    
    private func loadSpecificRoute(_ route: (name: String, startCity: String, endCity: String, points: [CLLocationCoordinate2D])) {
        // 先停止当前动画
        stopAnimation()
        resetAnimation()
        
        // 清除现有数据
        trajectoryPoints.removeAll()
        mapView.removeOverlays(mapView.overlays)
        if let marker = animationMarker {
            mapView.removeAnnotation(marker)
            animationMarker = nil
        }
        
        print("🎯 用户选择路线: \(route.name)")
        print("📍 \(route.startCity) → \(route.endCity)")
        print("🛣️ 包含 \(route.points.count) 个轨迹点")
        
        // 验证路线数据
        if route.points.isEmpty {
            print("❌ 路线数据为空!")
            statusLabel.text = "❌ 路线数据错误"
            return
        }
        
        // 打印前几个坐标点进行验证
        let samplePoints = Array(route.points.prefix(3))
        for (index, point) in samplePoints.enumerated() {
            print("📍 点\(index): lat=\(String(format: "%.6f", point.latitude)), lng=\(String(format: "%.6f", point.longitude))")
        }
        
        // 加载新的轨迹数据
        loadTrajectoryData(points: route.points)
        self.title = route.name
        statusLabel.text = "✅ 已加载: \(route.name) (\(route.points.count) 个点)"
        
        // 触感反馈
        let feedbackGenerator = UIImpactFeedbackGenerator(style: .medium)
        feedbackGenerator.impactOccurred()
        
        print("✅ 路线加载完成，标题已更新为: \(self.title ?? "未知")")
    }
}

// MARK: - MKMapViewDelegate
extension TrajectoryAnimationViewController: MKMapViewDelegate {
    func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
        if let polyline = overlay as? MKPolyline {
            let renderer = MKPolylineRenderer(polyline: polyline)
            
            if polyline === animationPath {
                renderer.strokeColor = .systemBlue
                renderer.lineWidth = 4
            } else if trailEffect.contains(polyline) {
                // 拖尾效果
                let alpha = CGFloat(trailEffect.firstIndex(of: polyline) ?? 0) / CGFloat(trailEffect.count)
                renderer.strokeColor = UIColor.systemRed.withAlphaComponent(0.3 + alpha * 0.7)
                renderer.lineWidth = 3
            } else {
                renderer.strokeColor = .systemGray
                renderer.lineWidth = 2
            }
            
            return renderer
        }
        
        return MKOverlayRenderer(overlay: overlay)
    }
    
    func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
        if annotation === animationMarker {
            let identifier = "AnimationMarker"
            var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)
            
            if annotationView == nil {
                annotationView = MKAnnotationView(annotation: annotation, reuseIdentifier: identifier)
                
                // 创建更大更明显的自定义标记
                let markerSize: CGFloat = 30 // 增大尺寸
                let markerView = UIView(frame: CGRect(x: 0, y: 0, width: markerSize, height: markerSize))
                markerView.backgroundColor = .systemRed
                markerView.layer.cornerRadius = markerSize / 2
                markerView.layer.borderWidth = 4
                markerView.layer.borderColor = UIColor.white.cgColor
                markerView.layer.shadowColor = UIColor.black.cgColor
                markerView.layer.shadowOffset = CGSize(width: 0, height: 3)
                markerView.layer.shadowOpacity = 0.5
                markerView.layer.shadowRadius = 5
                
                // 添加内部小圆点
                let innerDot = UIView(frame: CGRect(x: markerSize/4, y: markerSize/4, width: markerSize/2, height: markerSize/2))
                innerDot.backgroundColor = .white
                innerDot.layer.cornerRadius = markerSize/4
                markerView.addSubview(innerDot)
                
                annotationView?.addSubview(markerView)
                annotationView?.frame = CGRect(x: 0, y: 0, width: markerSize, height: markerSize)
                
                // 添加脉冲动画
                let pulseAnimation = CABasicAnimation(keyPath: "transform.scale")
                pulseAnimation.duration = 1.0
                pulseAnimation.fromValue = 1.0
                pulseAnimation.toValue = 1.3
                pulseAnimation.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
                pulseAnimation.autoreverses = true
                pulseAnimation.repeatCount = .infinity
                markerView.layer.add(pulseAnimation, forKey: "pulse")
                
                print("✨ 创建增强版动画标记")
            }
            
            annotationView?.annotation = annotation
            return annotationView
        }
        
        return nil
    }
}

// MARK: - Settings Delegate
extension TrajectoryAnimationViewController: TrajectorySettingsViewController.TrajectorySettingsDelegate {
    func didUpdateSettings(_ settings: TrajectorySettings) {
        self.animationSpeed = settings.animationSpeed
        self.videoQuality = settings.videoQuality
        self.mapType = settings.mapType
        self.routeType = settings.routeType
        
        // 应用设置
        mapView.mapType = settings.mapType
        speedSlider.value = Float(settings.animationSpeed)
        speedLabel.text = String(format: "%.1fx", settings.animationSpeed)
        
        // 重绘路径（如果路线类型改变）
        if routeType != settings.routeType {
            routeType = settings.routeType
            drawFullPath()
        }
        
        statusLabel.text = "⚙️ 设置已更新"
    }
}





