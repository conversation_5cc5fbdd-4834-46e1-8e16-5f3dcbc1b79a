import Foundation
import CoreLocation
import UIKit

// MARK: - 运动类型
enum ExerciseType: String, CaseIterable, Codable {
    case running = "running"
    case walking = "walking"
    case cycling = "cycling"
    case hiking = "hiking"
    case swimming = "swimming"
    case basketball = "basketball"
    case football = "football"
    case tennis = "tennis"
    case badminton = "badminton"
    case yoga = "yoga"
    case fitness = "fitness"
    case other = "other"
    
    var displayName: String {
        switch self {
        case .running: return "跑步"
        case .walking: return "步行"
        case .cycling: return "骑行"
        case .hiking: return "徒步"
        case .swimming: return "游泳"
        case .basketball: return "篮球"
        case .football: return "足球"
        case .tennis: return "网球"
        case .badminton: return "羽毛球"
        case .yoga: return "瑜伽"
        case .fitness: return "健身"
        case .other: return "其他"
        }
    }
    
    var icon: String {
        switch self {
        case .running: return "figure.run"
        case .walking: return "figure.walk"
        case .cycling: return "bicycle"
        case .hiking: return "figure.hiking"
        case .swimming: return "figure.pool.swim"
        case .basketball: return "basketball"
        case .football: return "soccerball"
        case .tennis: return "tennis.racket"
        case .badminton: return "figure.badminton"
        case .yoga: return "figure.yoga"
        case .fitness: return "dumbbell"
        case .other: return "figure.mixed.cardio"
        }
    }
    
    var color: UIColor {
        switch self {
        case .running: return .systemRed
        case .walking: return .systemGreen
        case .cycling: return .systemBlue
        case .hiking: return .systemOrange
        case .swimming: return .systemCyan
        case .basketball: return .systemPurple
        case .football: return .systemBrown
        case .tennis: return .systemYellow
        case .badminton: return .systemPink
        case .yoga: return .systemIndigo
        case .fitness: return .systemTeal
        case .other: return .systemGray
        }
    }
    
    var hasTrajectory: Bool {
        switch self {
        case .running, .walking, .cycling, .hiking:
            return true
        default:
            return false
        }
    }
    
    var requiresGPS: Bool {
        return hasTrajectory
    }
    
    var unit: String {
        switch self {
        case .running, .walking, .cycling, .hiking:
            return "公里"
        case .swimming:
            return "米"
        default:
            return "分钟"
        }
    }
    
    var description: String {
        switch self {
        case .running: return "户外跑步，GPS轨迹记录"
        case .walking: return "户外步行，GPS轨迹记录"
        case .cycling: return "户外骑行，GPS轨迹记录"
        case .hiking: return "户外徒步，GPS轨迹记录"
        case .swimming: return "游泳运动，手动记录"
        case .basketball: return "篮球运动，手动记录"
        case .football: return "足球运动，手动记录"
        case .tennis: return "网球运动，手动记录"
        case .badminton: return "羽毛球运动，手动记录"
        case .yoga: return "瑜伽练习，手动记录"
        case .fitness: return "健身训练，手动记录"
        case .other: return "其他运动，手动记录"
        }
    }
}

// MARK: - 轨迹点
struct TrajectoryPoint: Codable {
    let coordinate: LocationCoordinate
    let timestamp: Date
    let altitude: Double
    let speed: Double // 米/秒
    let accuracy: Double // 精度（米）
    
    init(location: CLLocation) {
        self.coordinate = LocationCoordinate(location.coordinate)
        self.timestamp = location.timestamp
        self.altitude = location.altitude
        self.speed = location.speed
        self.accuracy = location.horizontalAccuracy
    }
}

// 可编码的坐标结构
struct LocationCoordinate: Codable {
    let latitude: Double
    let longitude: Double
    
    init(_ coordinate: CLLocationCoordinate2D) {
        self.latitude = coordinate.latitude
        self.longitude = coordinate.longitude
    }
    
    var clLocationCoordinate: CLLocationCoordinate2D {
        return CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    }
}

// MARK: - 运动记录
struct ExerciseRecord: Codable, Identifiable {
    let id: String
    let type: ExerciseType
    let startTime: Date
    let endTime: Date?
    let duration: TimeInterval // 运动时长（秒）
    let distance: Double // 距离（米）
    let calories: Double // 卡路里
    let averageSpeed: Double // 平均速度（米/秒）
    let maxSpeed: Double // 最大速度（米/秒）
    let trajectory: [TrajectoryPoint] // 轨迹点
    let notes: String? // 备注
    let weather: String? // 天气
    let temperature: Double? // 温度
    let heartRate: [HeartRatePoint]? // 心率数据
    
    // 计算属性
    var distanceInKm: Double {
        return distance / 1000.0
    }
    
    var averageSpeedKmh: Double {
        return averageSpeed * 3.6
    }
    
    var maxSpeedKmh: Double {
        return maxSpeed * 3.6
    }
    
    var pace: String {
        guard distance > 0 else { return "--'--\"" }
        let paceSeconds = duration / (distance / 1000.0)
        let minutes = Int(paceSeconds) / 60
        let seconds = Int(paceSeconds) % 60
        return String(format: "%d'%02d\"", minutes, seconds)
    }
    
    var isCompleted: Bool {
        return endTime != nil
    }
    
    var formattedDuration: String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        let seconds = Int(duration) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
    
    var formattedDistance: String {
        if type.hasTrajectory {
            return String(format: "%.2f %@", distanceInKm, type.unit)
        } else if type == .swimming {
            return String(format: "%.0f %@", distance, type.unit)
        } else {
            return String(format: "%.0f %@", duration / 60, type.unit)
        }
    }
}

// MARK: - 心率数据点
struct HeartRatePoint: Codable {
    let timestamp: Date
    let heartRate: Int // 心率（次/分钟）
}

// MARK: - 运动统计
struct ExerciseStats {
    let totalExercises: Int
    let totalDuration: TimeInterval
    let totalDistance: Double
    let totalCalories: Double
    let averageDistance: Double
    let averageDuration: TimeInterval
    let favoriteExerciseType: ExerciseType?
    let longestExercise: ExerciseRecord?
    let fastestExercise: ExerciseRecord?
    let thisWeekStats: WeeklyStats
    let thisMonthStats: ExerciseMonthlyStats
    let exerciseTypeStats: [ExerciseTypeStats]
}

struct WeeklyStats {
    let exerciseCount: Int
    let totalDuration: TimeInterval
    let totalDistance: Double
    let totalCalories: Double
    let exerciseDays: Int
}

struct ExerciseMonthlyStats {
    let exerciseCount: Int
    let totalDuration: TimeInterval
    let totalDistance: Double
    let totalCalories: Double
    let exerciseDays: Int
    let averagePerDay: Double
}

struct ExerciseTypeStats {
    let type: ExerciseType
    let count: Int
    let totalDuration: TimeInterval
    let totalDistance: Double
    let totalCalories: Double
    let percentage: Double
}

// MARK: - 运动目标
struct ExerciseGoal: Codable {
    let id: String
    let type: GoalType
    let targetValue: Double
    let currentValue: Double
    let period: GoalPeriod
    let startDate: Date
    let endDate: Date
    let isActive: Bool
    
    var progress: Double {
        return min(currentValue / targetValue, 1.0)
    }
    
    var isCompleted: Bool {
        return currentValue >= targetValue
    }
}

enum GoalType: String, Codable, CaseIterable {
    case distance = "distance"
    case duration = "duration"
    case calories = "calories"
    case frequency = "frequency"
    
    var displayName: String {
        switch self {
        case .distance: return "距离目标"
        case .duration: return "时长目标"
        case .calories: return "卡路里目标"
        case .frequency: return "频次目标"
        }
    }
    
    var unit: String {
        switch self {
        case .distance: return "公里"
        case .duration: return "分钟"
        case .calories: return "卡路里"
        case .frequency: return "次"
        }
    }
}

enum GoalPeriod: String, Codable, CaseIterable {
    case daily = "daily"
    case weekly = "weekly"
    case monthly = "monthly"
    
    var displayName: String {
        switch self {
        case .daily: return "每日"
        case .weekly: return "每周"
        case .monthly: return "每月"
        }
    }
}

// MARK: - 运动配置
struct ExerciseConfig {
    let autoStartThreshold: Double = 5.0 // 自动开始阈值（米/秒）
    let autoPauseThreshold: Double = 1.0 // 自动暂停阈值（米/秒）
    let gpsAccuracyThreshold: Double = 20.0 // GPS精度阈值（米）
    let minDistanceFilter: Double = 5.0 // 最小距离过滤（米）
    let voicePromptInterval: TimeInterval = 300 // 语音提示间隔（秒）
    let enableVoicePrompt: Bool = true
    let enableAutoLock: Bool = false
    let enableHapticFeedback: Bool = true
} 