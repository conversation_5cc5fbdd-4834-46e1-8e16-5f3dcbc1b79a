import UIKit
import Photos

class PhotoAlbumViewController: UIViewController {
    
    // MARK: - Properties
    private let dataManager = PhotoAlbumDataManager.shared
    private var photoRecords: [PhotoRecord] = []
    private var currentFilter = PhotoFilter.all
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    // 头部统计卡片
    private let statsCardView = UIView()
    private let todayCountLabel = UILabel()
    private let weekCountLabel = UILabel()
    private let totalCountLabel = UILabel()
    
    // 快速拍照按钮
    private let quickCameraButton = UIButton(type: .system)
    
    // 筛选栏
    private let filterScrollView = UIScrollView()
    private let filterStackView = UIStackView()
    
    // 照片网格
    private let photosCollectionView: UICollectionView
    
    // MARK: - Lifecycle
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        // 设置集合视图布局
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 2
        layout.minimumLineSpacing = 2
        
        photosCollectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
        
        // 添加测试数据
        dataManager.addTestData()
        loadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        refreshData()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "时光相册"
        view.backgroundColor = .systemBackground
        
        // 导航栏按钮
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "gearshape"),
            style: .plain,
            target: self,
            action: #selector(settingsTapped)
        )
        
        setupScrollView()
        setupStatsCard()
        setupQuickCameraButton()
        setupFilterBar()
        setupPhotosCollectionView()
        setupConstraints()
    }
    
    private func setupScrollView() {
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
    }
    
    private func setupStatsCard() {
        statsCardView.backgroundColor = .systemPurple
        statsCardView.layer.cornerRadius = 12
        statsCardView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(statsCardView)
        
        // 今日统计
        todayCountLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        todayCountLabel.textColor = .white
        todayCountLabel.textAlignment = .center
        todayCountLabel.numberOfLines = 0
        todayCountLabel.translatesAutoresizingMaskIntoConstraints = false
        statsCardView.addSubview(todayCountLabel)
        
        // 本周统计
        weekCountLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        weekCountLabel.textColor = .white
        weekCountLabel.textAlignment = .center
        weekCountLabel.numberOfLines = 0
        weekCountLabel.translatesAutoresizingMaskIntoConstraints = false
        statsCardView.addSubview(weekCountLabel)
        
        // 总计统计
        totalCountLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        totalCountLabel.textColor = .white
        totalCountLabel.textAlignment = .center
        totalCountLabel.numberOfLines = 0
        totalCountLabel.translatesAutoresizingMaskIntoConstraints = false
        statsCardView.addSubview(totalCountLabel)
    }
    
    private func setupQuickCameraButton() {
        quickCameraButton.backgroundColor = .systemBlue
        quickCameraButton.layer.cornerRadius = 30
        quickCameraButton.setImage(UIImage(systemName: "camera.fill"), for: .normal)
        quickCameraButton.tintColor = .white
        quickCameraButton.translatesAutoresizingMaskIntoConstraints = false
        quickCameraButton.addTarget(self, action: #selector(quickCameraTapped), for: .touchUpInside)
        
        // 添加阴影
        quickCameraButton.layer.shadowColor = UIColor.black.cgColor
        quickCameraButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        quickCameraButton.layer.shadowRadius = 4
        quickCameraButton.layer.shadowOpacity = 0.3
        
        contentView.addSubview(quickCameraButton)
    }
    
    private func setupFilterBar() {
        filterScrollView.showsHorizontalScrollIndicator = false
        filterScrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(filterScrollView)
        
        filterStackView.axis = .horizontal
        filterStackView.spacing = 12
        filterStackView.translatesAutoresizingMaskIntoConstraints = false
        filterScrollView.addSubview(filterStackView)
        
        // 添加筛选按钮
        addFilterButton(title: "全部", filter: .all, isSelected: true)
        
        for dateRange in DateRange.allCases {
            addFilterButton(title: dateRange.displayName, filter: PhotoFilter(
                dateRange: dateRange,
                location: nil,
                weather: nil,
                mood: nil,
                tags: []
            ))
        }
        
        for mood in MoodType.allCases {
            addFilterButton(title: mood.emoji, filter: PhotoFilter(
                dateRange: nil,
                location: nil,
                weather: nil,
                mood: mood,
                tags: []
            ))
        }
    }
    
    private func addFilterButton(title: String, filter: PhotoFilter, isSelected: Bool = false) {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        button.backgroundColor = isSelected ? .systemBlue : .secondarySystemBackground
        button.setTitleColor(isSelected ? .white : .label, for: .normal)
        button.layer.cornerRadius = 16
        button.contentEdgeInsets = UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16)
        button.translatesAutoresizingMaskIntoConstraints = false
        
        button.addTarget(self, action: #selector(filterButtonTapped(_:)), for: .touchUpInside)
        
        // 存储筛选条件
        button.tag = filterStackView.arrangedSubviews.count
        
        filterStackView.addArrangedSubview(button)
        
        NSLayoutConstraint.activate([
            button.heightAnchor.constraint(equalToConstant: 32)
        ])
    }
    
    private func setupPhotosCollectionView() {
        photosCollectionView.delegate = self
        photosCollectionView.dataSource = self
        photosCollectionView.register(PhotoCollectionViewCell.self, forCellWithReuseIdentifier: "PhotoCell")
        photosCollectionView.backgroundColor = .clear
        photosCollectionView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(photosCollectionView)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 滚动视图
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            // 统计卡片
            statsCardView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            statsCardView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            statsCardView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            statsCardView.heightAnchor.constraint(equalToConstant: 80),
            
            todayCountLabel.leadingAnchor.constraint(equalTo: statsCardView.leadingAnchor, constant: 15),
            todayCountLabel.centerYAnchor.constraint(equalTo: statsCardView.centerYAnchor),
            todayCountLabel.widthAnchor.constraint(equalTo: statsCardView.widthAnchor, multiplier: 0.3),
            
            weekCountLabel.centerXAnchor.constraint(equalTo: statsCardView.centerXAnchor),
            weekCountLabel.centerYAnchor.constraint(equalTo: statsCardView.centerYAnchor),
            weekCountLabel.widthAnchor.constraint(equalTo: statsCardView.widthAnchor, multiplier: 0.3),
            
            totalCountLabel.trailingAnchor.constraint(equalTo: statsCardView.trailingAnchor, constant: -15),
            totalCountLabel.centerYAnchor.constraint(equalTo: statsCardView.centerYAnchor),
            totalCountLabel.widthAnchor.constraint(equalTo: statsCardView.widthAnchor, multiplier: 0.3),
            
            // 快速拍照按钮
            quickCameraButton.topAnchor.constraint(equalTo: statsCardView.bottomAnchor, constant: 20),
            quickCameraButton.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            quickCameraButton.widthAnchor.constraint(equalToConstant: 60),
            quickCameraButton.heightAnchor.constraint(equalToConstant: 60),
            
            // 筛选栏
            filterScrollView.topAnchor.constraint(equalTo: quickCameraButton.bottomAnchor, constant: 20),
            filterScrollView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            filterScrollView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            filterScrollView.heightAnchor.constraint(equalToConstant: 50),
            
            filterStackView.topAnchor.constraint(equalTo: filterScrollView.topAnchor, constant: 9),
            filterStackView.leadingAnchor.constraint(equalTo: filterScrollView.leadingAnchor, constant: 20),
            filterStackView.trailingAnchor.constraint(equalTo: filterScrollView.trailingAnchor, constant: -20),
            filterStackView.bottomAnchor.constraint(equalTo: filterScrollView.bottomAnchor, constant: -9),
            
            // 照片网格
            photosCollectionView.topAnchor.constraint(equalTo: filterScrollView.bottomAnchor, constant: 20),
            photosCollectionView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            photosCollectionView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            photosCollectionView.heightAnchor.constraint(equalToConstant: 600), // 固定高度
            photosCollectionView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20)
        ])
    }
    
    // MARK: - Data Loading
    private func loadData() {
        photoRecords = dataManager.getPhotoRecords(filter: currentFilter)
        updateUI()
    }
    
    private func refreshData() {
        loadData()
    }
    
    private func updateUI() {
        updateStatsCard()
        photosCollectionView.reloadData()
    }
    
    private func updateStatsCard() {
        let stats = dataManager.getPhotoStats()
        
        todayCountLabel.text = "今日\n\(stats.todayPhotos) 张"
        weekCountLabel.text = "本周\n\(stats.thisWeekPhotos) 张"
        totalCountLabel.text = "总计\n\(stats.totalPhotos) 张"
    }
    
    // MARK: - Actions
    @objc private func quickCameraTapped() {
        presentCameraOptions()
    }
    
    @objc private func filterButtonTapped(_ sender: UIButton) {
        // 更新按钮状态
        for (index, button) in filterStackView.arrangedSubviews.enumerated() {
            if let btn = button as? UIButton {
                let isSelected = index == sender.tag
                btn.backgroundColor = isSelected ? .systemBlue : .secondarySystemBackground
                btn.setTitleColor(isSelected ? .white : .label, for: .normal)
            }
        }
        
        // 应用筛选
        switch sender.tag {
        case 0:
            currentFilter = .all
        case 1:
            currentFilter = PhotoFilter(dateRange: .today, location: nil, weather: nil, mood: nil, tags: [])
        case 2:
            currentFilter = PhotoFilter(dateRange: .thisWeek, location: nil, weather: nil, mood: nil, tags: [])
        case 3:
            currentFilter = PhotoFilter(dateRange: .thisMonth, location: nil, weather: nil, mood: nil, tags: [])
        case 4:
            currentFilter = PhotoFilter(dateRange: .thisYear, location: nil, weather: nil, mood: nil, tags: [])
        default:
            // 心情筛选
            let moodIndex = sender.tag - 5
            if moodIndex >= 0 && moodIndex < MoodType.allCases.count {
                let mood = MoodType.allCases[moodIndex]
                currentFilter = PhotoFilter(dateRange: nil, location: nil, weather: nil, mood: mood, tags: [])
            }
        }
        
        loadData()
    }
    
    @objc private func settingsTapped() {
        let settingsVC = PhotoAlbumSettingsViewController()
        let navController = UINavigationController(rootViewController: settingsVC)
        present(navController, animated: true)
    }
    
    private func presentCameraOptions() {
        let alertController = UIAlertController(title: "拍照打卡", message: "选择拍照方式", preferredStyle: .actionSheet)
        
        alertController.addAction(UIAlertAction(title: "拍照", style: .default) { _ in
            self.presentCamera()
        })
        
        alertController.addAction(UIAlertAction(title: "从相册选择", style: .default) { _ in
            self.presentPhotoLibrary()
        })
        
        alertController.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        // iPad 支持
        if let popover = alertController.popoverPresentationController {
            popover.sourceView = quickCameraButton
            popover.sourceRect = quickCameraButton.bounds
        }
        
        present(alertController, animated: true)
    }
    
    private func presentCamera() {
        guard UIImagePickerController.isSourceTypeAvailable(.camera) else {
            showAlert(title: "相机不可用", message: "设备不支持相机功能")
            return
        }
        
        let picker = UIImagePickerController()
        picker.sourceType = .camera
        picker.delegate = self
        picker.allowsEditing = true
        present(picker, animated: true)
    }
    
    private func presentPhotoLibrary() {
        let picker = UIImagePickerController()
        picker.sourceType = .photoLibrary
        picker.delegate = self
        picker.allowsEditing = true
        present(picker, animated: true)
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - UICollectionViewDataSource
extension PhotoAlbumViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return photoRecords.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "PhotoCell", for: indexPath) as! PhotoCollectionViewCell
        cell.configure(with: photoRecords[indexPath.item])
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension PhotoAlbumViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let record = photoRecords[indexPath.item]
        let detailVC = PhotoDetailViewController()
        detailVC.photoRecord = record
        navigationController?.pushViewController(detailVC, animated: true)
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension PhotoAlbumViewController: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = (collectionView.frame.width - 4) / 3 // 3列，间距2
        return CGSize(width: width, height: width)
    }
}

// MARK: - UIImagePickerControllerDelegate
extension PhotoAlbumViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true) {
            guard let image = info[.editedImage] as? UIImage ?? info[.originalImage] as? UIImage else { return }
            
            // 显示编辑页面
            let editVC = PhotoEditViewController()
            editVC.originalImage = image
            editVC.delegate = self
            let navController = UINavigationController(rootViewController: editVC)
            navController.modalPresentationStyle = .fullScreen
            self.present(navController, animated: true)
        }
    }
    
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }
}

// MARK: - PhotoEditViewControllerDelegate
extension PhotoAlbumViewController: PhotoEditViewControllerDelegate {
    func photoEditViewController(_ controller: PhotoEditViewController, didFinishEditing image: UIImage, customNote: String?, mood: MoodType?, tags: [String]) {
        controller.dismiss(animated: true) {
            // 保存照片记录
            if let record = self.dataManager.addPhotoRecord(image, customNote: customNote, mood: mood, tags: tags) {
                self.refreshData()
                self.showAlert(title: "保存成功", message: "照片已保存到时光相册和系统相册")
            } else {
                self.showAlert(title: "保存失败", message: "照片保存失败，请重试")
            }
        }
    }
    
    func photoEditViewControllerDidCancel(_ controller: PhotoEditViewController) {
        controller.dismiss(animated: true)
    }
}

// MARK: - Custom Cell
class PhotoCollectionViewCell: UICollectionViewCell {
    
    private let imageView = UIImageView()
    private let overlayView = UIView()
    private let timeLabel = UILabel()
    private let moodLabel = UILabel()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.layer.cornerRadius = 8
        contentView.clipsToBounds = true
        
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(imageView)
        
        overlayView.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        overlayView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(overlayView)
        
        timeLabel.font = UIFont.systemFont(ofSize: 10, weight: .medium)
        timeLabel.textColor = .white
        timeLabel.translatesAutoresizingMaskIntoConstraints = false
        overlayView.addSubview(timeLabel)
        
        moodLabel.font = UIFont.systemFont(ofSize: 16)
        moodLabel.translatesAutoresizingMaskIntoConstraints = false
        overlayView.addSubview(moodLabel)
        
        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            overlayView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            overlayView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            overlayView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            overlayView.heightAnchor.constraint(equalToConstant: 30),
            
            timeLabel.leadingAnchor.constraint(equalTo: overlayView.leadingAnchor, constant: 4),
            timeLabel.centerYAnchor.constraint(equalTo: overlayView.centerYAnchor),
            
            moodLabel.trailingAnchor.constraint(equalTo: overlayView.trailingAnchor, constant: -4),
            moodLabel.centerYAnchor.constraint(equalTo: overlayView.centerYAnchor)
        ])
    }
    
    func configure(with record: PhotoRecord) {
        // 加载缩略图
        if let image = PhotoAlbumDataManager.shared.loadImage(from: record.thumbnailPath) {
            imageView.image = image
        } else {
            imageView.image = UIImage(systemName: "photo")
        }
        
        timeLabel.text = record.formattedDateTime
        moodLabel.text = record.mood?.emoji ?? ""
    }
} 