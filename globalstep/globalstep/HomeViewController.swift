//
//  HomeViewController.swift
//  globalstep
//
//  Created by AI Assistant on 2024/12/19.
//

import UIKit

class HomeViewController: UIViewController {
    
    // MARK: - UI Components
    // Smart planning section
    private let smartPlanningContainer = UIView()
    private let smartPlanningButton = UIButton(type: .system)
    
    // Segmented control for ongoing/completed trips
    private let tripStatusSegmentedControl = UISegmentedControl(items: ["进行中", "已结束"])
    
    // Trip list
    private let tripTableView = UITableView()
    
    // 数据管理器
    private let tripDataManager = TripDataManager.shared
    
    // 真实行程数据
    private var ongoingTrips: [TripRecord] = []
    private var completedTrips: [TripRecord] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        configureNavigationBar()
        loadTripData()
        
        // 监听行程数据更新通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(tripDataDidUpdate),
            name: .tripDataUpdated,
            object: nil
        )
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        refreshTripData()
    }
    
    // MARK: - 数据加载
    private func loadTripData() {
        // 从数据管理器获取真实的行程数据
        let allTrips = tripDataManager.getAllTrips()
        
        // 分类进行中和已结束的行程
        // 将规划中的行程也归类到进行中
        ongoingTrips = allTrips.filter { $0.status == .ongoing || $0.status == .planning }
        completedTrips = allTrips.filter { $0.status == .completed }
        
        print("📊 加载行程数据: 进行中(含规划中) \(ongoingTrips.count) 个，已结束 \(completedTrips.count) 个")
        
        // 如果没有真实数据，创建一些示例数据用于展示
        if ongoingTrips.isEmpty && completedTrips.isEmpty {
            createSampleTripData()
        }
        
        // 刷新表格视图
        DispatchQueue.main.async {
            self.tripTableView.reloadData()
            self.updateTableViewLayout()
        }
    }
    
    private func updateTableViewLayout() {
        // 强制更新表格布局
        tripTableView.setNeedsLayout()
        tripTableView.layoutIfNeeded()
        
        print("🔧 更新表格布局完成")
    }
    
    private func refreshTripData() {
        loadTripData()
    }
    
    @objc private func tripDataDidUpdate() {
        print("📢 收到行程数据更新通知，刷新首页列表")
        DispatchQueue.main.async {
            self.refreshTripData()
        }
    }
    
    deinit {
        // 移除通知监听
        NotificationCenter.default.removeObserver(self)
    }
    
    private func createSampleTripData() {
        print("🎯 创建示例行程数据")
        
        // 创建一些示例的进行中行程
        let ongoingTrip1 = TripRecord(
            id: UUID().uuidString,
            title: "北京文化探索之旅",
            destination: "北京市",
            departure: "天津市",
            startDate: Date(),
            endDate: Calendar.current.date(byAdding: .day, value: 3, to: Date()) ?? Date(),
            dayCount: 3,
            attractions: ["故宫博物院", "天安门广场", "八达岭长城", "颐和园", "北海公园"],
            currentDay: 2,
            status: .ongoing,
            createdDate: Date(),
            lastUpdated: Date()
        )
        
        let ongoingTrip2 = TripRecord(
            id: UUID().uuidString,
            title: "上海现代都市游",
            destination: "上海市",
            departure: "杭州市",
            startDate: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date(),
            endDate: Calendar.current.date(byAdding: .day, value: 2, to: Date()) ?? Date(),
            dayCount: 3,
            attractions: ["外滩", "东方明珠塔", "南京路步行街", "豫园", "田子坊", "新天地"],
            currentDay: 1,
            status: .ongoing,
            createdDate: Calendar.current.date(byAdding: .day, value: -2, to: Date()) ?? Date(),
            lastUpdated: Date()
        )
        
        // 创建一些示例的已结束行程
        let completedTrip1 = TripRecord(
            id: UUID().uuidString,
            title: "西安古都文化游",
            destination: "西安市",
            departure: "郑州市",
            startDate: Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date(),
            endDate: Calendar.current.date(byAdding: .day, value: -4, to: Date()) ?? Date(),
            dayCount: 3,
            attractions: ["兵马俑", "西安城墙", "大雁塔", "回民街", "华清宫"],
            currentDay: 3,
            status: .completed,
            createdDate: Calendar.current.date(byAdding: .day, value: -10, to: Date()) ?? Date(),
            lastUpdated: Calendar.current.date(byAdding: .day, value: -4, to: Date()) ?? Date()
        )
        
        let completedTrip2 = TripRecord(
            id: UUID().uuidString,
            title: "成都美食文化之旅",
            destination: "成都市",
            departure: "重庆市",
            startDate: Calendar.current.date(byAdding: .day, value: -14, to: Date()) ?? Date(),
            endDate: Calendar.current.date(byAdding: .day, value: -12, to: Date()) ?? Date(),
            dayCount: 2,
            attractions: ["熊猫基地", "锦里古街", "宽窄巷子", "春熙路"],
            currentDay: 2,
            status: .completed,
            createdDate: Calendar.current.date(byAdding: .day, value: -20, to: Date()) ?? Date(),
            lastUpdated: Calendar.current.date(byAdding: .day, value: -12, to: Date()) ?? Date()
        )
        
        // 保存到数据管理器
        tripDataManager.saveTrip(ongoingTrip1)
        tripDataManager.saveTrip(ongoingTrip2)
        tripDataManager.saveTrip(completedTrip1)
        tripDataManager.saveTrip(completedTrip2)
        
        // 更新本地数组
        ongoingTrips = [ongoingTrip1, ongoingTrip2]
        completedTrips = [completedTrip1, completedTrip2]
        
        print("✅ 示例数据创建完成")
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // Update gradient frames after layout
        for layer in smartPlanningButton.layer.sublayers ?? [] {
            if layer is CAGradientLayer {
                layer.frame = smartPlanningButton.bounds
            }
        }
    }
    
    private func configureNavigationBar() {
        view.backgroundColor = UIColor.systemGray6
        
        // 移除导航栏按钮
        navigationItem.leftBarButtonItem = nil
        navigationItem.rightBarButtonItem = nil
        
        // Hide navigation bar title
        navigationItem.title = ""
    }
    
    private func setupUI() {
        // 直接添加到主视图，不使用滚动视图
        view.addSubview(smartPlanningContainer)
        view.addSubview(tripStatusSegmentedControl)
        view.addSubview(tripTableView)
        
        // Setup smart planning section
        setupSmartPlanningSection()
        
        // Setup segmented control
        setupSegmentedControl()
        
        // Setup trip table view
        setupTripTableView()
    }
    

    
    private func setupSmartPlanningSection() {
        smartPlanningContainer.translatesAutoresizingMaskIntoConstraints = false
        smartPlanningContainer.backgroundColor = .systemBackground
        smartPlanningContainer.layer.cornerRadius = 20
        smartPlanningContainer.layer.shadowColor = UIColor.black.cgColor
        smartPlanningContainer.layer.shadowOffset = CGSize(width: 0, height: 6)
        smartPlanningContainer.layer.shadowRadius = 12
        smartPlanningContainer.layer.shadowOpacity = 0.15
        
        // Title with icon
        let titleStackView = UIStackView()
        titleStackView.axis = .horizontal
        titleStackView.spacing = 8
        titleStackView.alignment = .center
        titleStackView.translatesAutoresizingMaskIntoConstraints = false
        
        let magicIcon = UILabel()
        magicIcon.text = "✨"
        magicIcon.font = UIFont.systemFont(ofSize: 20)
        
        let titleLabel = UILabel()
        titleLabel.text = "智能规划"
        titleLabel.font = UIFont.boldSystemFont(ofSize: 20)
        titleLabel.textColor = .label
        
        titleStackView.addArrangedSubview(magicIcon)
        titleStackView.addArrangedSubview(titleLabel)
        
        // Description text
        let descriptionLabel = UILabel()
        descriptionLabel.text = "快速生成个性化行程方案"
        descriptionLabel.font = UIFont.systemFont(ofSize: 16)
        descriptionLabel.textColor = .secondaryLabel
        descriptionLabel.numberOfLines = 0
        descriptionLabel.textAlignment = .center
        descriptionLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // Smart planning button with gradient
        smartPlanningButton.translatesAutoresizingMaskIntoConstraints = false
        smartPlanningButton.setTitle("✏️  智能规划我的行程", for: .normal)
        smartPlanningButton.setTitleColor(.white, for: .normal)
        smartPlanningButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        smartPlanningButton.layer.cornerRadius = 25
        smartPlanningButton.addTarget(self, action: #selector(smartPlanningTapped), for: .touchUpInside)
        
        // Add gradient background to button
        let buttonGradientLayer = CAGradientLayer()
        buttonGradientLayer.colors = [
            UIColor.systemBlue.cgColor,
            UIColor.systemPurple.cgColor
        ]
        buttonGradientLayer.startPoint = CGPoint(x: 0, y: 0)
        buttonGradientLayer.endPoint = CGPoint(x: 1, y: 0)
        buttonGradientLayer.cornerRadius = 25
        smartPlanningButton.layer.insertSublayer(buttonGradientLayer, at: 0)
        
        // Add shadow to button
        smartPlanningButton.layer.shadowColor = UIColor.systemBlue.cgColor
        smartPlanningButton.layer.shadowOffset = CGSize(width: 0, height: 4)
        smartPlanningButton.layer.shadowRadius = 8
        smartPlanningButton.layer.shadowOpacity = 0.3
        
        // Update gradient frame when layout changes
        DispatchQueue.main.async {
            buttonGradientLayer.frame = self.smartPlanningButton.bounds
        }
        
        smartPlanningContainer.addSubview(titleStackView)
        smartPlanningContainer.addSubview(descriptionLabel)
        smartPlanningContainer.addSubview(smartPlanningButton)
        
        NSLayoutConstraint.activate([
            titleStackView.centerXAnchor.constraint(equalTo: smartPlanningContainer.centerXAnchor),
            titleStackView.topAnchor.constraint(equalTo: smartPlanningContainer.topAnchor, constant: 24),
            
            descriptionLabel.centerXAnchor.constraint(equalTo: smartPlanningContainer.centerXAnchor),
            descriptionLabel.topAnchor.constraint(equalTo: titleStackView.bottomAnchor, constant: 16),
            descriptionLabel.leadingAnchor.constraint(greaterThanOrEqualTo: smartPlanningContainer.leadingAnchor, constant: 20),
            descriptionLabel.trailingAnchor.constraint(lessThanOrEqualTo: smartPlanningContainer.trailingAnchor, constant: -20),
            
            smartPlanningButton.leadingAnchor.constraint(equalTo: smartPlanningContainer.leadingAnchor, constant: 20),
            smartPlanningButton.trailingAnchor.constraint(equalTo: smartPlanningContainer.trailingAnchor, constant: -20),
            smartPlanningButton.topAnchor.constraint(equalTo: descriptionLabel.bottomAnchor, constant: 24),
            smartPlanningButton.bottomAnchor.constraint(equalTo: smartPlanningContainer.bottomAnchor, constant: -24),
            smartPlanningButton.heightAnchor.constraint(equalToConstant: 50)
        ])
    }
    

    
    private func setupSegmentedControl() {
        tripStatusSegmentedControl.translatesAutoresizingMaskIntoConstraints = false
        tripStatusSegmentedControl.selectedSegmentIndex = 0
        tripStatusSegmentedControl.addTarget(self, action: #selector(segmentedControlChanged), for: .valueChanged)
        
        // Enhanced styling
        if #available(iOS 13.0, *) {
            tripStatusSegmentedControl.selectedSegmentTintColor = UIColor.systemGreen
            tripStatusSegmentedControl.backgroundColor = UIColor.systemGray6
            tripStatusSegmentedControl.layer.cornerRadius = 10
            tripStatusSegmentedControl.layer.borderWidth = 1
            tripStatusSegmentedControl.layer.borderColor = UIColor.systemGray4.cgColor
            
            // Style the text
            tripStatusSegmentedControl.setTitleTextAttributes([
                .foregroundColor: UIColor.secondaryLabel,
                .font: UIFont.systemFont(ofSize: 14, weight: .medium)
            ], for: .normal)
            
            tripStatusSegmentedControl.setTitleTextAttributes([
                .foregroundColor: UIColor.white,
                .font: UIFont.systemFont(ofSize: 14, weight: .semibold)
            ], for: .selected)
        }
    }
    
    private func setupTripTableView() {
        tripTableView.translatesAutoresizingMaskIntoConstraints = false
        tripTableView.delegate = self
        tripTableView.dataSource = self
        tripTableView.backgroundColor = .clear
        tripTableView.separatorStyle = .none
        tripTableView.register(TripTableViewCell.self, forCellReuseIdentifier: "TripCell")
        tripTableView.isScrollEnabled = true // 恢复表格滚动
        tripTableView.showsVerticalScrollIndicator = true
        
        // 添加调试背景色，方便查看表格位置
        tripTableView.backgroundColor = UIColor.systemRed.withAlphaComponent(0.2)
        tripTableView.layer.borderWidth = 2
        tripTableView.layer.borderColor = UIColor.systemRed.cgColor
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // Smart planning container - 现在位于顶部
            smartPlanningContainer.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 24),
            smartPlanningContainer.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            smartPlanningContainer.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            
            // Segmented control
            tripStatusSegmentedControl.topAnchor.constraint(equalTo: smartPlanningContainer.bottomAnchor, constant: 36),
            tripStatusSegmentedControl.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            tripStatusSegmentedControl.widthAnchor.constraint(equalToConstant: 160),
            tripStatusSegmentedControl.heightAnchor.constraint(equalToConstant: 36),
            
            // Trip table view - 占用剩余空间
            tripTableView.topAnchor.constraint(equalTo: tripStatusSegmentedControl.bottomAnchor, constant: 24),
            tripTableView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            tripTableView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            tripTableView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -30)
        ])
    }
    
    // MARK: - Actions
    @objc private func smartPlanningTapped() {
        let smartPlanningVC = SmartPlanningViewController()
        let navController = UINavigationController(rootViewController: smartPlanningVC)
        navController.modalPresentationStyle = .fullScreen
        present(navController, animated: true, completion: nil)
    }
    
    @objc private func segmentedControlChanged() {
        print("🔄 切换到: \(tripStatusSegmentedControl.selectedSegmentIndex == 0 ? "进行中" : "已结束")")
        tripTableView.reloadData()
        updateTableViewLayout()
    }
}

// MARK: - UITableViewDataSource, UITableViewDelegate
extension HomeViewController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        let count = tripStatusSegmentedControl.selectedSegmentIndex == 0 ? ongoingTrips.count : completedTrips.count
        print("📊 当前显示行程数量: \(count)")
        return count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        print("📝 创建表格行 \(indexPath.row)")
        let cell = tableView.dequeueReusableCell(withIdentifier: "TripCell", for: indexPath) as! TripTableViewCell
        
        let trip = tripStatusSegmentedControl.selectedSegmentIndex == 0 ? ongoingTrips[indexPath.row] : completedTrips[indexPath.row]
        cell.configure(with: trip)
        
        print("✅ 配置完成: \(trip.title)")
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 120
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let trip = tripStatusSegmentedControl.selectedSegmentIndex == 0 ? ongoingTrips[indexPath.row] : completedTrips[indexPath.row]
        
        // 跳转到地图页面显示行程详情
        let mapVC = TripMapViewController()
        
        // 创建临时的TripPlan用于地图显示
        let tripPlan = TripPlan(
            title: trip.title,
            description: "查看您的行程详情",
            dayCount: trip.dayCount,
            attractionCount: trip.attractions.count,
            attractions: trip.attractions,
            dailyPlans: nil
        )
        
        mapVC.tripPlans = [tripPlan]
        mapVC.currentPlanIndex = 0
        mapVC.departureCity = trip.departure
        mapVC.destinationCity = trip.destination
        
        navigationController?.pushViewController(mapVC, animated: true)
        
        print("📍 跳转到行程地图: \(trip.title)")
    }
    
    // 支持滑动操作
    func tableView(_ tableView: UITableView, trailingSwipeActionsConfigurationForRowAt indexPath: IndexPath) -> UISwipeActionsConfiguration? {
        let trip = tripStatusSegmentedControl.selectedSegmentIndex == 0 ? ongoingTrips[indexPath.row] : completedTrips[indexPath.row]
        
        var actions: [UIContextualAction] = []
        
        // 删除操作
        let deleteAction = UIContextualAction(style: .destructive, title: "删除") { [weak self] _, _, completion in
            self?.deleteTrip(trip, at: indexPath)
            completion(true)
        }
        deleteAction.backgroundColor = .systemRed
        actions.append(deleteAction)
        
        // 状态切换操作
        if trip.status == .planning {
            let startAction = UIContextualAction(style: .normal, title: "开始") { [weak self] _, _, completion in
                self?.markTripAsOngoing(trip, at: indexPath)
                completion(true)
            }
            startAction.backgroundColor = .systemGreen
            actions.append(startAction)
        } else if trip.status == .ongoing {
            let completeAction = UIContextualAction(style: .normal, title: "完成") { [weak self] _, _, completion in
                self?.markTripAsCompleted(trip, at: indexPath)
                completion(true)
            }
            completeAction.backgroundColor = .systemBlue
            actions.append(completeAction)
        } else if trip.status == .completed {
            let restartAction = UIContextualAction(style: .normal, title: "重新开始") { [weak self] _, _, completion in
                self?.markTripAsOngoing(trip, at: indexPath)
                completion(true)
            }
            restartAction.backgroundColor = .systemGreen
            actions.append(restartAction)
        }
        
        return UISwipeActionsConfiguration(actions: actions)
    }
    
    // 删除行程
    private func deleteTrip(_ trip: TripRecord, at indexPath: IndexPath) {
        let alert = UIAlertController(title: "确认删除", message: "确定要删除行程「\(trip.title)」吗？", preferredStyle: .alert)
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "删除", style: .destructive) { [weak self] _ in
            // 从数据管理器删除
            TripDataManager.shared.deleteTrip(withId: trip.id)
            
            // 从本地数组删除
            if trip.status == .ongoing {
                self?.ongoingTrips.remove(at: indexPath.row)
            } else {
                self?.completedTrips.remove(at: indexPath.row)
            }
            
            // 更新表格视图
            self?.tripTableView.deleteRows(at: [indexPath], with: .fade)
            
            print("🗑️ 已删除行程: \(trip.title)")
        })
        
        present(alert, animated: true)
    }
    
    // 标记为已完成
    private func markTripAsCompleted(_ trip: TripRecord, at indexPath: IndexPath) {
        TripDataManager.shared.updateTripStatus(trip.id, newStatus: .completed, currentDay: trip.dayCount)
        refreshTripData()
        
        print("✅ 行程已标记为完成: \(trip.title)")
    }
    
    // 重新开始行程
    private func markTripAsOngoing(_ trip: TripRecord, at indexPath: IndexPath) {
        TripDataManager.shared.updateTripStatus(trip.id, newStatus: .ongoing, currentDay: 1)
        refreshTripData()
        
        print("🔄 行程已重新开始: \(trip.title)")
    }
}

// MARK: - 导入共享模型
// TripRecord, TripStatus, TripDataManager 现在定义在 TripModels.swift 中

// MARK: - Models (保留原有的，但现在使用 TripRecord)
struct TripItem {
    let title: String
    let duration: String
    let locations: String
    let members: String
    let isOngoing: Bool
}

// MARK: - Custom Table View Cell  
class TripTableViewCell: UITableViewCell {
    private let containerView = UIView()
    private let backgroundImageView = UIImageView()
    private let titleLabel = UILabel()
    private let durationLabel = UILabel()
    private let locationLabel = UILabel()
    private let progressLabel = UILabel()
    private let statusIndicator = UIView()
    private let cloudIcon1 = UILabel()
    private let cloudIcon2 = UILabel()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear
        
        // Container view with enhanced styling
        containerView.translatesAutoresizingMaskIntoConstraints = false
        containerView.layer.cornerRadius = 20
        containerView.layer.masksToBounds = false
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 4)
        containerView.layer.shadowRadius = 8
        containerView.layer.shadowOpacity = 0.1
        contentView.addSubview(containerView)
        
        // Background with gradient
        backgroundImageView.translatesAutoresizingMaskIntoConstraints = false
        backgroundImageView.layer.cornerRadius = 20
        backgroundImageView.layer.masksToBounds = true
        containerView.addSubview(backgroundImageView)
        
        // Add gradient background
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.systemBlue.withAlphaComponent(0.7).cgColor,
            UIColor.systemTeal.withAlphaComponent(0.5).cgColor,
            UIColor.systemCyan.withAlphaComponent(0.3).cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        gradientLayer.cornerRadius = 20
        backgroundImageView.layer.insertSublayer(gradientLayer, at: 0)
        
        DispatchQueue.main.async {
            gradientLayer.frame = self.backgroundImageView.bounds
        }
        
        // Status indicator
        statusIndicator.translatesAutoresizingMaskIntoConstraints = false
        statusIndicator.backgroundColor = .systemGreen
        statusIndicator.layer.cornerRadius = 6
        containerView.addSubview(statusIndicator)
        
        // Cloud icons
        cloudIcon1.translatesAutoresizingMaskIntoConstraints = false
        cloudIcon1.text = "☁️"
        cloudIcon1.font = UIFont.systemFont(ofSize: 24)
        containerView.addSubview(cloudIcon1)
        
        cloudIcon2.translatesAutoresizingMaskIntoConstraints = false
        cloudIcon2.text = "☁️"
        cloudIcon2.font = UIFont.systemFont(ofSize: 20)
        containerView.addSubview(cloudIcon2)
        
        // Title label
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textColor = .label
        containerView.addSubview(titleLabel)
        
        // Duration label
        durationLabel.translatesAutoresizingMaskIntoConstraints = false
        durationLabel.font = UIFont.systemFont(ofSize: 14)
        durationLabel.textColor = .secondaryLabel
        containerView.addSubview(durationLabel)
        
        // Location label
        locationLabel.translatesAutoresizingMaskIntoConstraints = false
        locationLabel.font = UIFont.systemFont(ofSize: 14)
        locationLabel.textColor = .secondaryLabel
        containerView.addSubview(locationLabel)
        
        // Progress label with icon
        progressLabel.translatesAutoresizingMaskIntoConstraints = false
        progressLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        progressLabel.textColor = .label
        containerView.addSubview(progressLabel)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),
            
            backgroundImageView.topAnchor.constraint(equalTo: containerView.topAnchor),
            backgroundImageView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            backgroundImageView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            backgroundImageView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
            
            statusIndicator.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 12),
            statusIndicator.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            statusIndicator.widthAnchor.constraint(equalToConstant: 12),
            statusIndicator.heightAnchor.constraint(equalToConstant: 12),
            
            cloudIcon1.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            cloudIcon1.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            
            cloudIcon2.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 12),
            cloudIcon2.leadingAnchor.constraint(equalTo: cloudIcon1.trailingAnchor, constant: 12),
            
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: cloudIcon2.trailingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(lessThanOrEqualTo: statusIndicator.leadingAnchor, constant: -12),
            
            durationLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 12),
            durationLabel.leadingAnchor.constraint(equalTo: titleLabel.leadingAnchor),
            durationLabel.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor, constant: -20),
            
            locationLabel.topAnchor.constraint(equalTo: durationLabel.bottomAnchor, constant: 6),
            locationLabel.leadingAnchor.constraint(equalTo: titleLabel.leadingAnchor),
            locationLabel.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor, constant: -20),
            
            progressLabel.topAnchor.constraint(equalTo: locationLabel.bottomAnchor, constant: 6),
            progressLabel.leadingAnchor.constraint(equalTo: titleLabel.leadingAnchor),
            progressLabel.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor, constant: -20),
            progressLabel.bottomAnchor.constraint(lessThanOrEqualTo: containerView.bottomAnchor, constant: -16)
        ])
    }
    
    func configure(with trip: TripRecord) {
        titleLabel.text = trip.title
        durationLabel.text = "🕐 \(trip.formattedDuration)"
        locationLabel.text = "📍 \(trip.formattedLocations)"
        
        // 根据状态显示不同的进度信息
        switch trip.status {
        case .ongoing:
            progressLabel.text = "🔥 \(trip.progress)"
            statusIndicator.backgroundColor = .systemGreen
        case .completed:
            progressLabel.text = "✅ \(trip.progress)"
            statusIndicator.backgroundColor = .systemBlue
        case .planning:
            progressLabel.text = "📋 \(trip.progress)"
            statusIndicator.backgroundColor = .systemOrange
        }
    }
    
    // 保留原有的方法用于兼容性
    func configure(with trip: TripItem) {
        titleLabel.text = trip.title
        durationLabel.text = "🕐 \(trip.duration)"
        locationLabel.text = "📍 \(trip.locations)"
        progressLabel.text = "👤 \(trip.members)"
        
        statusIndicator.backgroundColor = trip.isOngoing ? .systemGreen : .systemBlue
    }
} 