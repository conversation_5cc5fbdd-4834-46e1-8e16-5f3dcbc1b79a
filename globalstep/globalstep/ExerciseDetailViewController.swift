import UIKit
import MapKit

class ExerciseDetailViewController: UIViewController {
    
    // MARK: - Properties
    var exerciseRecord: ExerciseRecord?
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let headerView = UIView()
    private let exerciseTypeLabel = UILabel()
    private let dateLabel = UILabel()
    
    private let mapView = MKMapView()
    private let statsContainerView = UIView()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        displayExerciseData()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "运动详情"
        view.backgroundColor = .systemBackground
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .action,
            target: self,
            action: #selector(shareButtonTapped)
        )
        
        setupScrollView()
        setupHeader()
        setupMapView()
        setupStatsContainer()
        setupConstraints()
    }
    
    private func setupScrollView() {
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
    }
    
    private func setupHeader() {
        headerView.backgroundColor = .secondarySystemBackground
        headerView.layer.cornerRadius = 12
        headerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(headerView)
        
        exerciseTypeLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        exerciseTypeLabel.textAlignment = .center
        exerciseTypeLabel.translatesAutoresizingMaskIntoConstraints = false
        headerView.addSubview(exerciseTypeLabel)
        
        dateLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        dateLabel.textColor = .secondaryLabel
        dateLabel.textAlignment = .center
        dateLabel.translatesAutoresizingMaskIntoConstraints = false
        headerView.addSubview(dateLabel)
    }
    
    private func setupMapView() {
        mapView.delegate = self
        mapView.mapType = .standard
        mapView.layer.cornerRadius = 12
        mapView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(mapView)
    }
    
    private func setupStatsContainer() {
        statsContainerView.backgroundColor = .secondarySystemBackground
        statsContainerView.layer.cornerRadius = 12
        statsContainerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(statsContainerView)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 滚动视图
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            // 头部视图
            headerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            headerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            headerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            headerView.heightAnchor.constraint(equalToConstant: 80),
            
            exerciseTypeLabel.topAnchor.constraint(equalTo: headerView.topAnchor, constant: 15),
            exerciseTypeLabel.leadingAnchor.constraint(equalTo: headerView.leadingAnchor, constant: 20),
            exerciseTypeLabel.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -20),
            
            dateLabel.topAnchor.constraint(equalTo: exerciseTypeLabel.bottomAnchor, constant: 5),
            dateLabel.leadingAnchor.constraint(equalTo: headerView.leadingAnchor, constant: 20),
            dateLabel.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -20),
            dateLabel.bottomAnchor.constraint(equalTo: headerView.bottomAnchor, constant: -15),
            
            // 地图视图
            mapView.topAnchor.constraint(equalTo: headerView.bottomAnchor, constant: 20),
            mapView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            mapView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            mapView.heightAnchor.constraint(equalToConstant: 200),
            
            // 统计容器
            statsContainerView.topAnchor.constraint(equalTo: mapView.bottomAnchor, constant: 20),
            statsContainerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            statsContainerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            statsContainerView.heightAnchor.constraint(greaterThanOrEqualToConstant: 200),
            statsContainerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20)
        ])
    }
    
    // MARK: - Display Exercise Data
    private func displayExerciseData() {
        guard let record = exerciseRecord else { return }
        
        // 更新头部信息
        exerciseTypeLabel.text = record.type.displayName
        exerciseTypeLabel.textColor = record.type.color
        
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日 HH:mm"
        dateLabel.text = formatter.string(from: record.startTime)
        
        // 显示地图轨迹
        displayMapTrajectory()
        
        // 显示统计信息
        displayStats()
    }
    
    private func displayMapTrajectory() {
        guard let record = exerciseRecord, !record.trajectory.isEmpty else {
            mapView.isHidden = true
            return
        }
        
        // 添加轨迹线
        let coordinates = record.trajectory.map { $0.coordinate.clLocationCoordinate }
        let polyline = MKPolyline(coordinates: coordinates, count: coordinates.count)
        mapView.addOverlay(polyline)
        
        // 添加起点和终点标记
        if let startPoint = record.trajectory.first {
            let startAnnotation = MKPointAnnotation()
            startAnnotation.coordinate = startPoint.coordinate.clLocationCoordinate
            startAnnotation.title = "起点"
            mapView.addAnnotation(startAnnotation)
        }
        
        if let endPoint = record.trajectory.last {
            let endAnnotation = MKPointAnnotation()
            endAnnotation.coordinate = endPoint.coordinate.clLocationCoordinate
            endAnnotation.title = "终点"
            mapView.addAnnotation(endAnnotation)
        }
        
        // 设置地图显示区域
        let region = MKCoordinateRegion(coordinates: coordinates)
        mapView.setRegion(region, animated: false)
    }
    
    private func displayStats() {
        guard let record = exerciseRecord else { return }
        
        // 清除现有内容
        statsContainerView.subviews.forEach { $0.removeFromSuperview() }
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 20
        stackView.translatesAutoresizingMaskIntoConstraints = false
        statsContainerView.addSubview(stackView)
        
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "运动数据"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        titleLabel.textAlignment = .center
        stackView.addArrangedSubview(titleLabel)
        
        // 主要数据网格
        let mainStatsGrid = createMainStatsGrid(record: record)
        stackView.addArrangedSubview(mainStatsGrid)
        
        // 详细数据
        let detailStats = createDetailStats(record: record)
        stackView.addArrangedSubview(detailStats)
        
        // 备注
        if let notes = record.notes, !notes.isEmpty {
            let notesView = createNotesView(notes: notes)
            stackView.addArrangedSubview(notesView)
        }
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: statsContainerView.topAnchor, constant: 20),
            stackView.leadingAnchor.constraint(equalTo: statsContainerView.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: statsContainerView.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: statsContainerView.bottomAnchor, constant: -20)
        ])
    }
    
    private func createMainStatsGrid(record: ExerciseRecord) -> UIView {
        let container = UIView()
        
        let topRow = UIStackView()
        topRow.axis = .horizontal
        topRow.distribution = .fillEqually
        topRow.spacing = 20
        topRow.translatesAutoresizingMaskIntoConstraints = false
        
        let bottomRow = UIStackView()
        bottomRow.axis = .horizontal
        bottomRow.distribution = .fillEqually
        bottomRow.spacing = 20
        bottomRow.translatesAutoresizingMaskIntoConstraints = false
        
        // 时长
        let durationView = createStatView(
            title: "时长",
            value: record.formattedDuration,
            color: .systemBlue
        )
        
        // 距离
        let distanceView = createStatView(
            title: "距离",
            value: record.formattedDistance,
            color: .systemGreen
        )
        
        // 平均速度
        let speedView = createStatView(
            title: "平均速度",
            value: String(format: "%.1f km/h", record.averageSpeedKmh),
            color: .systemOrange
        )
        
        // 卡路里
        let caloriesView = createStatView(
            title: "卡路里",
            value: String(format: "%.0f kcal", record.calories),
            color: .systemRed
        )
        
        topRow.addArrangedSubview(durationView)
        topRow.addArrangedSubview(distanceView)
        
        bottomRow.addArrangedSubview(speedView)
        bottomRow.addArrangedSubview(caloriesView)
        
        container.addSubview(topRow)
        container.addSubview(bottomRow)
        
        NSLayoutConstraint.activate([
            topRow.topAnchor.constraint(equalTo: container.topAnchor),
            topRow.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            topRow.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            topRow.heightAnchor.constraint(equalToConstant: 60),
            
            bottomRow.topAnchor.constraint(equalTo: topRow.bottomAnchor, constant: 15),
            bottomRow.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            bottomRow.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            bottomRow.heightAnchor.constraint(equalToConstant: 60),
            bottomRow.bottomAnchor.constraint(equalTo: container.bottomAnchor)
        ])
        
        return container
    }
    
    private func createStatView(title: String, value: String, color: UIColor) -> UIView {
        let container = UIView()
        container.backgroundColor = .tertiarySystemBackground
        container.layer.cornerRadius = 8
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        titleLabel.textColor = .secondaryLabel
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        valueLabel.textColor = color
        valueLabel.textAlignment = .center
        valueLabel.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(titleLabel)
        container.addSubview(valueLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor, constant: 8),
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 5),
            titleLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -5),
            
            valueLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 5),
            valueLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 5),
            valueLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -5),
            valueLabel.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -8)
        ])
        
        return container
    }
    
    private func createDetailStats(record: ExerciseRecord) -> UIView {
        let container = UIView()
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 8
        stackView.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(stackView)
        
        // 详细统计项
        var detailItems = [
            ("配速", record.pace),
            ("最大速度", String(format: "%.1f km/h", record.maxSpeedKmh)),
            ("开始时间", formatTime(record.startTime)),
        ]
        
        if let endTime = record.endTime {
            detailItems.append(("结束时间", formatTime(endTime)))
        }
        
        if let weather = record.weather {
            detailItems.append(("天气", weather))
        }
        
        if let temperature = record.temperature {
            detailItems.append(("温度", String(format: "%.1f°C", temperature)))
        }
        
        for (title, value) in detailItems {
            let itemView = createDetailItem(title: title, value: value)
            stackView.addArrangedSubview(itemView)
        }
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: container.topAnchor),
            stackView.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            stackView.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            stackView.bottomAnchor.constraint(equalTo: container.bottomAnchor)
        ])
        
        return container
    }
    
    private func createDetailItem(title: String, value: String) -> UIView {
        let container = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = UIFont.systemFont(ofSize: 14)
        valueLabel.textColor = .secondaryLabel
        valueLabel.textAlignment = .right
        valueLabel.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(titleLabel)
        container.addSubview(valueLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            
            valueLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            valueLabel.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            valueLabel.leadingAnchor.constraint(greaterThanOrEqualTo: titleLabel.trailingAnchor, constant: 10),
            
            container.heightAnchor.constraint(equalToConstant: 30)
        ])
        
        return container
    }
    
    private func createNotesView(notes: String) -> UIView {
        let container = UIView()
        container.backgroundColor = .systemYellow.withAlphaComponent(0.1)
        container.layer.cornerRadius = 8
        
        let titleLabel = UILabel()
        titleLabel.text = "📝 备注"
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let notesLabel = UILabel()
        notesLabel.text = notes
        notesLabel.font = UIFont.systemFont(ofSize: 14)
        notesLabel.textColor = .secondaryLabel
        notesLabel.numberOfLines = 0
        notesLabel.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(titleLabel)
        container.addSubview(notesLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor, constant: 15),
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 15),
            titleLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -15),
            
            notesLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 10),
            notesLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 15),
            notesLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -15),
            notesLabel.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -15)
        ])
        
        return container
    }
    
    // MARK: - Helper Methods
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    // MARK: - Actions
    @objc private func shareButtonTapped() {
        guard let record = exerciseRecord else { return }
        
        let shareText = """
        我刚刚完成了一次\(record.type.displayName)！
        
        📊 运动数据：
        ⏱ 时长：\(record.formattedDuration)
        📏 距离：\(record.formattedDistance)
        🔥 卡路里：\(String(format: "%.0f", record.calories)) kcal
        ⚡️ 平均速度：\(String(format: "%.1f", record.averageSpeedKmh)) km/h
        
        #环球足迹 #运动打卡
        """
        
        let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)
        activityVC.popoverPresentationController?.barButtonItem = navigationItem.rightBarButtonItem
        present(activityVC, animated: true)
    }
}

// MARK: - MKMapViewDelegate
extension ExerciseDetailViewController: MKMapViewDelegate {
    
    func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
        if let polyline = overlay as? MKPolyline {
            let renderer = MKPolylineRenderer(polyline: polyline)
            renderer.strokeColor = exerciseRecord?.type.color ?? .systemBlue
            renderer.lineWidth = 4.0
            return renderer
        }
        return MKOverlayRenderer(overlay: overlay)
    }
    
    func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
        if annotation is MKPointAnnotation {
            let identifier = "PointAnnotation"
            var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)
            
            if annotationView == nil {
                annotationView = MKAnnotationView(annotation: annotation, reuseIdentifier: identifier)
                annotationView?.canShowCallout = true
            }
            
            // 设置起点和终点图标
            if annotation.title == "起点" {
                annotationView?.image = UIImage(systemName: "play.circle.fill")?.withTintColor(.systemGreen, renderingMode: .alwaysOriginal)
            } else if annotation.title == "终点" {
                annotationView?.image = UIImage(systemName: "stop.circle.fill")?.withTintColor(.systemRed, renderingMode: .alwaysOriginal)
            }
            
            return annotationView
        }
        
        return nil
    }
} 