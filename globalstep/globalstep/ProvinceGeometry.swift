import Foundation
import CoreLocation
import MapKit

// 定义多边形数据结构
struct ProvincePolygon {
    let outerBoundary: [CLLocationCoordinate2D]  // 外边界
    let holes: [[CLLocationCoordinate2D]]        // 内部孔洞（如果有的话）
}

class ProvinceGeometry {
    static let shared = ProvinceGeometry()
    
    private var provincePolygonsCache: [String: [ProvincePolygon]] = [:]
    
    private init() {
        loadGeoJsonData()
    }
    
    // 获取省份的多边形数据
    func getProvincePolygons(for provinceId: String) -> [ProvincePolygon] {
        return provincePolygonsCache[provinceId] ?? []
    }
    
    // 获取省份的所有边界坐标（向后兼容）
    func getProvinceCoordinates(for provinceId: String) -> [CLLocationCoordinate2D] {
        let polygons = getProvincePolygons(for: provinceId)
        return polygons.first?.outerBoundary ?? []
    }
    
    // 获取省份的 MKPolygon 覆盖层，用于地图渲染
    func getProvinceOverlays(for provinceId: String, provinceName: String) -> [MKPolygon] {
        let polygons = getProvincePolygons(for: provinceId)
        var overlays: [MKPolygon] = []
        
        for polygon in polygons {
            // 确保多边形是闭合的
            var coordinates = polygon.outerBoundary
            if !coordinates.isEmpty {
                let firstCoordinate = coordinates[0]
                let lastCoordinate = coordinates[coordinates.count - 1]
                
                // 如果起点和终点不同，添加起点到末尾以闭合多边形
                if abs(firstCoordinate.latitude - lastCoordinate.latitude) > 0.000001 ||
                   abs(firstCoordinate.longitude - lastCoordinate.longitude) > 0.000001 {
                    coordinates.append(firstCoordinate)
                }
                
                let mkPolygon: MKPolygon
                
                // 如果有孔洞，创建带孔洞的多边形
                if !polygon.holes.isEmpty {
                    let interiorPolygons = polygon.holes.map { hole in
                        MKPolygon(coordinates: hole, count: hole.count)
                    }
                    mkPolygon = MKPolygon(coordinates: coordinates, count: coordinates.count, interiorPolygons: interiorPolygons)
                } else {
                    mkPolygon = MKPolygon(coordinates: coordinates, count: coordinates.count)
                }
                
                mkPolygon.title = provinceName
                overlays.append(mkPolygon)
            }
        }
        
        return overlays
    }
    
    // 省份ID到行政代码的映射
    private let provinceIdToCodeMap: [String: String] = [
        "beijing": "110000",
        "tianjin": "120000",
        "hebei": "130000",
        "shanxi": "140000",
        "neimenggu": "150000",
        "liaoning": "210000",
        "jilin": "220000",
        "heilongjiang": "230000",
        "shanghai": "310000",
        "jiangsu": "320000",
        "zhejiang": "330000",
        "anhui": "340000",
        "fujian": "350000",
        "jiangxi": "360000",
        "shandong": "370000",
        "henan": "410000",
        "hubei": "420000",
        "hunan": "430000",
        "guangdong": "440000",
        "guangxi": "450000",
        "hainan": "460000",
        "chongqing": "500000",
        "sichuan": "510000",
        "guizhou": "520000",
        "yunnan": "530000",
        "xizang": "540000",
        "shaanxi": "610000",
        "gansu": "620000",
        "qinghai": "630000",
        "ningxia": "640000",
        "xinjiang": "650000",
        "taiwan": "710000",
        "hongkong": "810000",
        "macau": "820000"
    ]
    
    // 加载 geoJson 数据
    private func loadGeoJsonData() {
        // 直接使用嵌入的地理数据，避免文件读取问题
        let embeddedGeoData = getEmbeddedGeoData()
        
        for (provinceId, code) in provinceIdToCodeMap {
            if let geoData = embeddedGeoData[code],
               let polygons = parseGeoJsonString(geoData) {
                provincePolygonsCache[provinceId] = polygons
                print("✅ 成功加载 \(provinceId) 的嵌入地理数据，多边形数量: \(polygons.count)")
            } else {
                print("❌ 使用 \(provinceId) 的简化数据")
                // 如果没有嵌入数据，使用简化的边界数据作为备用
                provincePolygonsCache[provinceId] = [getSimplifiedPolygon(for: provinceId)]
            }
        }
    }
    
    // 获取嵌入的地理数据
    private func getEmbeddedGeoData() -> [String: String] {
        var geoData: [String: String] = [:]
        
        // 打印 bundle 信息用于调试
        print("🔍 Bundle 主路径: \(Bundle.main.bundlePath)")
        print("🔍 Bundle 资源路径: \(Bundle.main.resourcePath ?? "无")")
        
        // 列出 bundle 根目录的内容
        if let resourcePath = Bundle.main.resourcePath {
            do {
                let contents = try FileManager.default.contentsOfDirectory(atPath: resourcePath)
                print("🔍 Bundle 根目录内容: \(contents)")
            } catch {
                print("❌ 无法列出 bundle 内容: \(error)")
            }
        }
        
        // 遍历所有省份代码，从 bundle 中加载对应的 geoJson 文件
        for (_, code) in provinceIdToCodeMap {
            var foundFile = false
            
            // 尝试多种可能的路径
            let possiblePaths = [
                "geoJsonData/100000/\(code)",
                "globalstep/geoJsonData/100000/\(code)",
                "geoJsonData/\(code)",
                "\(code)"
            ]
            
            for pathPrefix in possiblePaths {
                if let path = Bundle.main.path(forResource: pathPrefix, ofType: "geoJson"),
                   let content = try? String(contentsOfFile: path, encoding: .utf8) {
                    geoData[code] = content
                    print("✅ 成功从 bundle 加载 \(pathPrefix).geoJson 文件")
                    foundFile = true
                    break
                }
            }
            
            // 如果上述方法都失败，尝试直接在资源路径中查找
            if !foundFile, let resourcePath = Bundle.main.resourcePath {
                let directPath = "\(resourcePath)/geoJsonData/100000/\(code).geoJson"
                if FileManager.default.fileExists(atPath: directPath),
                   let content = try? String(contentsOfFile: directPath, encoding: .utf8) {
                    geoData[code] = content
                    print("✅ 通过直接路径加载 \(code).geoJson 文件")
                    foundFile = true
                }
            }
            
            // Fallback: 尝试从项目源代码目录读取（仅用于开发/调试）
            if !foundFile {
                // 获取当前工作目录或项目目录
                let projectPaths = [
                    "./globalstep/geoJsonData/100000/\(code).geoJson",
                    "../globalstep/geoJsonData/100000/\(code).geoJson",
                    "../../globalstep/geoJsonData/100000/\(code).geoJson",
                    "./geoJsonData/100000/\(code).geoJson",
                    "../geoJsonData/100000/\(code).geoJson"
                ]
                
                for projectPath in projectPaths {
                    if FileManager.default.fileExists(atPath: projectPath),
                       let content = try? String(contentsOfFile: projectPath, encoding: .utf8) {
                        geoData[code] = content
                        print("✅ 通过项目路径加载 \(code).geoJson 文件: \(projectPath)")
                        foundFile = true
                        break
                    }
                }
            }
            
            if !foundFile {
                print("❌ 无法加载 \(code).geoJson 文件")
            }
        }
        
        return geoData
    }
    
    // 解析 geoJson 字符串
    private func parseGeoJsonString(_ geoJsonString: String) -> [ProvincePolygon]? {
        guard let data = geoJsonString.data(using: .utf8) else {
            return nil
        }
        
        do {
            guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                  let features = json["features"] as? [[String: Any]] else {
                return nil
            }
            
            var allPolygons: [ProvincePolygon] = []
            
            for feature in features {
                if let geometry = feature["geometry"] as? [String: Any],
                   let type = geometry["type"] as? String,
                   let coordinates = geometry["coordinates"] as? [Any] {
                    
                    let polygons = extractPolygons(from: coordinates, geometryType: type)
                    allPolygons.append(contentsOf: polygons)
                }
            }
            
            return allPolygons.isEmpty ? nil : allPolygons
            
        } catch {
            print("解析嵌入 geoJson 数据失败: \(error)")
            return nil
        }
    }
    
    // 从坐标数据中提取多边形
    private func extractPolygons(from coordinates: [Any], geometryType: String) -> [ProvincePolygon] {
        var result: [ProvincePolygon] = []
        
        switch geometryType {
        case "MultiPolygon":
            // MultiPolygon: [[[[lng, lat], [lng, lat], ...]]]
            for polygon in coordinates {
                if let polygonArray = polygon as? [Any] {
                    var rings: [[CLLocationCoordinate2D]] = []
                    
                    for ring in polygonArray {
                        if let ringArray = ring as? [[Double]] {
                            let coordinates = ringArray.compactMap { coordinate -> CLLocationCoordinate2D? in
                                guard coordinate.count >= 2 else { return nil }
                                return CLLocationCoordinate2D(
                                    latitude: coordinate[1],
                                    longitude: coordinate[0]
                                )
                            }
                            if !coordinates.isEmpty {
                                rings.append(coordinates)
                            }
                        }
                    }
                    
                    if !rings.isEmpty {
                        // 第一个环是外边界，其余的是孔洞
                        let outerBoundary = rings[0]
                        let holes = Array(rings.dropFirst())
                        result.append(ProvincePolygon(outerBoundary: outerBoundary, holes: holes))
                    }
                }
            }
            
        case "Polygon":
            // Polygon: [[[lng, lat], [lng, lat], ...]]
            var rings: [[CLLocationCoordinate2D]] = []
            
            for ring in coordinates {
                if let ringArray = ring as? [[Double]] {
                    let coordinates = ringArray.compactMap { coordinate -> CLLocationCoordinate2D? in
                        guard coordinate.count >= 2 else { return nil }
                        return CLLocationCoordinate2D(
                            latitude: coordinate[1],
                            longitude: coordinate[0]
                        )
                    }
                    if !coordinates.isEmpty {
                        rings.append(coordinates)
                    }
                }
            }
            
            if !rings.isEmpty {
                // 第一个环是外边界，其余的是孔洞
                let outerBoundary = rings[0]
                let holes = Array(rings.dropFirst())
                result.append(ProvincePolygon(outerBoundary: outerBoundary, holes: holes))
            }
            
        default:
            break
        }
        
        return result
    }
    
    // 简化的边界数据作为备用
    private func getSimplifiedPolygon(for provinceId: String) -> ProvincePolygon {
        let simplifiedData: [String: [CLLocationCoordinate2D]] = [
            "beijing": [
                CLLocationCoordinate2D(latitude: 39.4, longitude: 115.7),
                CLLocationCoordinate2D(latitude: 39.4, longitude: 117.4),
                CLLocationCoordinate2D(latitude: 40.9, longitude: 117.4),
                CLLocationCoordinate2D(latitude: 40.9, longitude: 115.7),
                CLLocationCoordinate2D(latitude: 39.4, longitude: 115.7)  // 闭合多边形
            ],
            "tianjin": [
                CLLocationCoordinate2D(latitude: 38.7, longitude: 116.8),
                CLLocationCoordinate2D(latitude: 38.7, longitude: 118.1),
                CLLocationCoordinate2D(latitude: 40.2, longitude: 118.1),
                CLLocationCoordinate2D(latitude: 40.2, longitude: 116.8),
                CLLocationCoordinate2D(latitude: 38.7, longitude: 116.8)  // 闭合多边形
            ],
            "hebei": [
                CLLocationCoordinate2D(latitude: 36.0, longitude: 113.4),
                CLLocationCoordinate2D(latitude: 36.0, longitude: 119.8),
                CLLocationCoordinate2D(latitude: 42.6, longitude: 119.8),
                CLLocationCoordinate2D(latitude: 42.6, longitude: 113.4),
                CLLocationCoordinate2D(latitude: 36.0, longitude: 113.4)  // 闭合多边形
            ],
            "shanxi": [
                CLLocationCoordinate2D(latitude: 34.6, longitude: 110.2),
                CLLocationCoordinate2D(latitude: 34.6, longitude: 114.6),
                CLLocationCoordinate2D(latitude: 40.7, longitude: 114.6),
                CLLocationCoordinate2D(latitude: 40.7, longitude: 110.2),
                CLLocationCoordinate2D(latitude: 34.6, longitude: 110.2)  // 闭合多边形
            ],
            "guangdong": [
                CLLocationCoordinate2D(latitude: 20.2, longitude: 109.8),
                CLLocationCoordinate2D(latitude: 20.2, longitude: 117.2),
                CLLocationCoordinate2D(latitude: 25.5, longitude: 117.2),
                CLLocationCoordinate2D(latitude: 25.5, longitude: 109.8),
                CLLocationCoordinate2D(latitude: 20.2, longitude: 109.8)  // 闭合多边形
            ]
        ]
        
        let coordinates = simplifiedData[provinceId] ?? []
        return ProvincePolygon(outerBoundary: coordinates, holes: [])
    }
} 