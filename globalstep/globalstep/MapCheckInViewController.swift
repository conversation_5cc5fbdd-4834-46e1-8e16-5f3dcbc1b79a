import UIKit
import MapKit
import CoreLocation
import Photos

class MapCheckInViewController: UIViewController {
    
    // MARK: - Properties
    private let dataManager = PhotoAlbumDataManager.shared
    private let locationManager = CLLocationManager()
    private var photoRecords: [PhotoRecord] = []
    private var currentLocation: CLLocation?
    private var selectedAnnotation: PhotoAnnotation?
    
    // MARK: - UI Components
    private let mapView = MKMapView()
    private let checkInButton = UIButton(type: .system)
    private let locationButton = UIButton(type: .system)
    private let recordsListButton = UIButton(type: .system)
    private let searchBar = UISearchBar()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupLocationManager()
        loadPhotoRecords()
        requestLocationPermission()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        refreshPhotoRecords()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "地图打卡"
        view.backgroundColor = .systemBackground
        
        // 导航栏配置
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            title: "历史",
            style: .plain,
            target: self,
            action: #selector(showHistoryTapped)
        )
        
        setupMapView()
        setupSearchBar()
        setupButtons()
        setupConstraints()
    }
    
    private func setupMapView() {
        mapView.delegate = self
        mapView.mapType = .standard
        mapView.showsUserLocation = true
        mapView.userTrackingMode = .none
        mapView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(mapView)
        
        // 添加长按手势
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(mapLongPressed(_:)))
        mapView.addGestureRecognizer(longPress)
    }
    
    private func setupSearchBar() {
        searchBar.delegate = self
        searchBar.placeholder = "搜索地点"
        searchBar.searchBarStyle = .minimal
        searchBar.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(searchBar)
    }
    
    private func setupButtons() {
        // 打卡按钮
        checkInButton.backgroundColor = .systemBlue
        checkInButton.setTitle("打卡", for: .normal)
        checkInButton.setTitleColor(.white, for: .normal)
        checkInButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        checkInButton.layer.cornerRadius = 25
        checkInButton.layer.shadowColor = UIColor.black.cgColor
        checkInButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        checkInButton.layer.shadowOpacity = 0.3
        checkInButton.layer.shadowRadius = 4
        checkInButton.translatesAutoresizingMaskIntoConstraints = false
        checkInButton.addTarget(self, action: #selector(checkInTapped), for: .touchUpInside)
        view.addSubview(checkInButton)
        
        // 定位按钮
        locationButton.backgroundColor = .systemBackground
        locationButton.setImage(UIImage(systemName: "location.fill"), for: .normal)
        locationButton.tintColor = .systemBlue
        locationButton.layer.cornerRadius = 20
        locationButton.layer.borderWidth = 1
        locationButton.layer.borderColor = UIColor.systemGray4.cgColor
        locationButton.layer.shadowColor = UIColor.black.cgColor
        locationButton.layer.shadowOffset = CGSize(width: 0, height: 1)
        locationButton.layer.shadowOpacity = 0.2
        locationButton.layer.shadowRadius = 2
        locationButton.translatesAutoresizingMaskIntoConstraints = false
        locationButton.addTarget(self, action: #selector(locationTapped), for: .touchUpInside)
        view.addSubview(locationButton)
        
        // 记录列表按钮
        recordsListButton.backgroundColor = .systemBackground
        recordsListButton.setImage(UIImage(systemName: "list.bullet"), for: .normal)
        recordsListButton.tintColor = .systemBlue
        recordsListButton.layer.cornerRadius = 20
        recordsListButton.layer.borderWidth = 1
        recordsListButton.layer.borderColor = UIColor.systemGray4.cgColor
        recordsListButton.layer.shadowColor = UIColor.black.cgColor
        recordsListButton.layer.shadowOffset = CGSize(width: 0, height: 1)
        recordsListButton.layer.shadowOpacity = 0.2
        recordsListButton.layer.shadowRadius = 2
        recordsListButton.translatesAutoresizingMaskIntoConstraints = false
        recordsListButton.addTarget(self, action: #selector(recordsListTapped), for: .touchUpInside)
        view.addSubview(recordsListButton)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 搜索栏
            searchBar.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            searchBar.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16),
            searchBar.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16),
            
            // 地图视图 - 不覆盖底部安全区域
            mapView.topAnchor.constraint(equalTo: searchBar.bottomAnchor),
            mapView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            mapView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            mapView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor),
            
            // 打卡按钮
            checkInButton.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -20),
            checkInButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            checkInButton.widthAnchor.constraint(equalToConstant: 100),
            checkInButton.heightAnchor.constraint(equalToConstant: 50),
            
            // 定位按钮
            locationButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            locationButton.bottomAnchor.constraint(equalTo: checkInButton.topAnchor, constant: -20),
            locationButton.widthAnchor.constraint(equalToConstant: 40),
            locationButton.heightAnchor.constraint(equalToConstant: 40),
            
            // 记录列表按钮
            recordsListButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            recordsListButton.bottomAnchor.constraint(equalTo: locationButton.topAnchor, constant: -10),
            recordsListButton.widthAnchor.constraint(equalToConstant: 40),
            recordsListButton.heightAnchor.constraint(equalToConstant: 40)
        ])
    }
    
    // MARK: - Location Manager
    private func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        locationManager.distanceFilter = 10.0
    }
    
    private func requestLocationPermission() {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.startUpdatingLocation()
        case .denied, .restricted:
            showLocationPermissionAlert()
        @unknown default:
            break
        }
    }
    
    private func showLocationPermissionAlert() {
        let alert = UIAlertController(
            title: "需要位置权限",
            message: "请在设置中允许应用使用位置服务以进行地图打卡",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "设置", style: .default) { _ in
            if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsURL)
            }
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        present(alert, animated: true)
    }
    
    // MARK: - Data Loading
    private func loadPhotoRecords() {
        photoRecords = dataManager.getPhotoRecords()
        addAnnotationsToMap()
    }
    
    private func refreshPhotoRecords() {
        loadPhotoRecords()
    }
    
    private func addAnnotationsToMap() {
        // 移除现有的标注
        mapView.removeAnnotations(mapView.annotations.filter { $0 is PhotoAnnotation })
        
        // 添加照片标注
        for record in photoRecords {
            if let location = record.location {
                let annotation = PhotoAnnotation(photoRecord: record)
                annotation.coordinate = location.coordinate.clLocationCoordinate
                annotation.title = record.location?.displayAddress
                annotation.subtitle = record.formattedDateTime
                mapView.addAnnotation(annotation)
            }
        }
    }
    
    // MARK: - Actions
    @objc private func checkInTapped() {
        guard let currentLocation = currentLocation else {
            showAlert(title: "位置未就绪", message: "请等待GPS定位完成后再打卡")
            return
        }
        
        showCheckInOptions(at: currentLocation)
    }
    
    @objc private func locationTapped() {
        guard let userLocation = currentLocation else {
            showAlert(title: "位置未获取", message: "正在获取您的位置...")
            return
        }
        
        let region = MKCoordinateRegion(
            center: userLocation.coordinate,
            latitudinalMeters: 1000,
            longitudinalMeters: 1000
        )
        mapView.setRegion(region, animated: true)
    }
    
    @objc private func recordsListTapped() {
        let recordsVC = PhotoRecordsListViewController()
        recordsVC.photoRecords = photoRecords
        recordsVC.delegate = self
        let navController = UINavigationController(rootViewController: recordsVC)
        present(navController, animated: true)
    }
    
    @objc private func showHistoryTapped() {
        let historyVC = PhotoHistoryViewController()
        navigationController?.pushViewController(historyVC, animated: true)
    }
    
    @objc private func mapLongPressed(_ gesture: UILongPressGestureRecognizer) {
        guard gesture.state == .began else { return }
        
        let point = gesture.location(in: mapView)
        let coordinate = mapView.convert(point, toCoordinateFrom: mapView)
        let location = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        
        showCheckInOptions(at: location)
    }
    
    private func showCheckInOptions(at location: CLLocation) {
        let alert = UIAlertController(title: "选择打卡方式", message: nil, preferredStyle: .actionSheet)
        
        alert.addAction(UIAlertAction(title: "拍照打卡", style: .default) { [weak self] _ in
            self?.checkInWithCamera(at: location)
        })
        
        alert.addAction(UIAlertAction(title: "从相册选择", style: .default) { [weak self] _ in
            self?.checkInWithPhotoLibrary(at: location)
        })
        
        alert.addAction(UIAlertAction(title: "文字打卡", style: .default) { [weak self] _ in
            self?.checkInWithText(at: location)
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        if let popover = alert.popoverPresentationController {
            popover.sourceView = checkInButton
            popover.sourceRect = checkInButton.bounds
        }
        
        present(alert, animated: true)
    }
    
    private func checkInWithCamera(at location: CLLocation) {
        guard UIImagePickerController.isSourceTypeAvailable(.camera) else {
            showAlert(title: "相机不可用", message: "此设备不支持相机功能")
            return
        }
        
        let picker = UIImagePickerController()
        picker.sourceType = .camera
        picker.delegate = self
        picker.modalPresentationStyle = .fullScreen
        
        // 存储位置信息供稍后使用
        picker.accessibilityHint = "\(location.coordinate.latitude),\(location.coordinate.longitude)"
        
        present(picker, animated: true)
    }
    
    private func checkInWithPhotoLibrary(at location: CLLocation) {
        guard UIImagePickerController.isSourceTypeAvailable(.photoLibrary) else {
            showAlert(title: "相册不可用", message: "无法访问相册")
            return
        }
        
        let picker = UIImagePickerController()
        picker.sourceType = .photoLibrary
        picker.delegate = self
        
        // 存储位置信息供稍后使用
        picker.accessibilityHint = "\(location.coordinate.latitude),\(location.coordinate.longitude)"
        
        present(picker, animated: true)
    }
    
    private func checkInWithText(at location: CLLocation) {
        let alert = UIAlertController(title: "文字打卡", message: "在这里记录您的心情或想法", preferredStyle: .alert)
        
        alert.addTextField { textField in
            textField.placeholder = "输入打卡内容..."
            textField.autocapitalizationType = .sentences
        }
        
        alert.addAction(UIAlertAction(title: "打卡", style: .default) { [weak self] _ in
            guard let text = alert.textFields?.first?.text, !text.isEmpty else { return }
            self?.createTextCheckIn(text: text, at: location)
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        present(alert, animated: true)
    }
    
    private func createTextCheckIn(text: String, at location: CLLocation) {
        // 创建一个简单的文字图片
        let textImage = createTextImage(text: text)
        processCheckIn(image: textImage, location: location, customNote: text)
    }
    
    private func createTextImage(text: String) -> UIImage {
        let size = CGSize(width: 300, height: 200)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        
        // 设置背景
        UIColor.systemBackground.setFill()
        UIRectFill(CGRect(origin: .zero, size: size))
        
        // 设置文字属性
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .center
        
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 18, weight: .medium),
            .foregroundColor: UIColor.label,
            .paragraphStyle: paragraphStyle
        ]
        
        // 绘制文字
        let textRect = CGRect(x: 20, y: 50, width: 260, height: 100)
        text.draw(in: textRect, withAttributes: attributes)
        
        let image = UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
        UIGraphicsEndImageContext()
        
        return image
    }
    
    private func processCheckIn(image: UIImage, location: CLLocation, customNote: String? = nil) {
        // 获取地址信息
        getLocationInfo(for: location) { [weak self] locationInfo in
            DispatchQueue.main.async {
                // 创建照片记录
                if let photoRecord = self?.dataManager.addPhotoRecord(
                    image,
                    customNote: customNote,
                    mood: nil,
                    tags: []
                ) {
                    // 更新位置信息
                    self?.dataManager.updatePhotoRecord(photoRecord.id, location: locationInfo)
                    
                    // 刷新地图
                    self?.refreshPhotoRecords()
                    
                    // 显示成功提示
                    self?.showAlert(title: "打卡成功", message: "已成功记录您的足迹")
                }
            }
        }
    }
    
    private func getLocationInfo(for location: CLLocation, completion: @escaping (LocationInfo?) -> Void) {
        let geocoder = CLGeocoder()
        geocoder.reverseGeocodeLocation(location) { placemarks, error in
            if let placemark = placemarks?.first {
                let locationInfo = LocationInfo(
                    coordinate: LocationCoordinate(location.coordinate),
                    address: [placemark.thoroughfare, placemark.subThoroughfare]
                        .compactMap { $0 }
                        .joined(separator: " "),
                    city: placemark.locality,
                    country: placemark.country,
                    placeName: placemark.name
                )
                completion(locationInfo)
            } else {
                completion(nil)
            }
        }
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - CLLocationManagerDelegate
extension MapCheckInViewController: CLLocationManagerDelegate {
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let newLocation = locations.last else { return }
        currentLocation = newLocation
        
        // 首次获取位置时居中地图
        if mapView.region.span.latitudeDelta > 1.0 {
            let region = MKCoordinateRegion(
                center: newLocation.coordinate,
                latitudinalMeters: 2000,
                longitudinalMeters: 2000
            )
            mapView.setRegion(region, animated: true)
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.startUpdatingLocation()
        case .denied, .restricted:
            showLocationPermissionAlert()
        case .notDetermined:
            break
        @unknown default:
            break
        }
    }
}

// MARK: - MKMapViewDelegate
extension MapCheckInViewController: MKMapViewDelegate {
    func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
        if annotation is MKUserLocation {
            return nil
        }
        
        guard let photoAnnotation = annotation as? PhotoAnnotation else {
            return nil
        }
        
        let identifier = "PhotoAnnotation"
        var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)
        
        if annotationView == nil {
            annotationView = MKAnnotationView(annotation: annotation, reuseIdentifier: identifier)
            annotationView?.canShowCallout = true
            annotationView?.rightCalloutAccessoryView = UIButton(type: .detailDisclosure)
        } else {
            annotationView?.annotation = annotation
        }
        
        // 设置标注图标
        if let mood = photoAnnotation.photoRecord.mood {
            annotationView?.image = createAnnotationImage(emoji: mood.emoji)
        } else {
            annotationView?.image = createAnnotationImage(emoji: "📍")
        }
        
        return annotationView
    }
    
    func mapView(_ mapView: MKMapView, annotationView view: MKAnnotationView, calloutAccessoryControlTapped control: UIControl) {
        guard let photoAnnotation = view.annotation as? PhotoAnnotation else { return }
        
        let detailVC = PhotoDetailViewController()
        detailVC.photoRecord = photoAnnotation.photoRecord
        navigationController?.pushViewController(detailVC, animated: true)
    }
    
    private func createAnnotationImage(emoji: String) -> UIImage {
        let size = CGSize(width: 30, height: 30)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        
        // 绘制背景圆圈
        UIColor.white.setFill()
        let circleRect = CGRect(origin: .zero, size: size)
        UIBezierPath(ovalIn: circleRect).fill()
        
        // 绘制边框
        UIColor.systemBlue.setStroke()
        let borderPath = UIBezierPath(ovalIn: circleRect.insetBy(dx: 1, dy: 1))
        borderPath.lineWidth = 2
        borderPath.stroke()
        
        // 绘制emoji
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 16),
            .foregroundColor: UIColor.label
        ]
        
        let textSize = emoji.size(withAttributes: attributes)
        let textRect = CGRect(
            x: (size.width - textSize.width) / 2,
            y: (size.height - textSize.height) / 2,
            width: textSize.width,
            height: textSize.height
        )
        
        emoji.draw(in: textRect, withAttributes: attributes)
        
        let image = UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
        UIGraphicsEndImageContext()
        
        return image
    }
}

// MARK: - UIImagePickerControllerDelegate
extension MapCheckInViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true)
        
        guard let image = info[.originalImage] as? UIImage else { return }
        
        // 获取位置信息
        if let locationString = picker.accessibilityHint,
           let locationComponents = locationString.components(separatedBy: ",") as [String]?,
           locationComponents.count == 2,
           let latitude = Double(locationComponents[0]),
           let longitude = Double(locationComponents[1]) {
            
            let location = CLLocation(latitude: latitude, longitude: longitude)
            processCheckIn(image: image, location: location)
        }
    }
    
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }
}

// MARK: - UISearchBarDelegate
extension MapCheckInViewController: UISearchBarDelegate {
    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
        searchBar.resignFirstResponder()
        
        guard let searchText = searchBar.text, !searchText.isEmpty else { return }
        
        let geocoder = CLGeocoder()
        geocoder.geocodeAddressString(searchText) { [weak self] placemarks, error in
            DispatchQueue.main.async {
                if let placemark = placemarks?.first,
                   let location = placemark.location {
                    let region = MKCoordinateRegion(
                        center: location.coordinate,
                        latitudinalMeters: 1000,
                        longitudinalMeters: 1000
                    )
                    self?.mapView.setRegion(region, animated: true)
                } else {
                    self?.showAlert(title: "搜索失败", message: "未找到相关地点")
                }
            }
        }
    }
}

// MARK: - PhotoRecordsListDelegate
protocol PhotoRecordsListDelegate: AnyObject {
    func didSelectPhotoRecord(_ record: PhotoRecord)
}

extension MapCheckInViewController: PhotoRecordsListDelegate {
    func didSelectPhotoRecord(_ record: PhotoRecord) {
        // 在地图上定位到选中的照片
        if let location = record.location {
            let region = MKCoordinateRegion(
                center: location.coordinate.clLocationCoordinate,
                latitudinalMeters: 500,
                longitudinalMeters: 500
            )
            mapView.setRegion(region, animated: true)
            
            // 找到对应的标注并选中
            for annotation in mapView.annotations {
                if let photoAnnotation = annotation as? PhotoAnnotation,
                   photoAnnotation.photoRecord.id == record.id {
                    mapView.selectAnnotation(photoAnnotation, animated: true)
                    break
                }
            }
        }
    }
}

// MARK: - PhotoAnnotation
class PhotoAnnotation: NSObject, MKAnnotation {
    let photoRecord: PhotoRecord
    var coordinate: CLLocationCoordinate2D
    var title: String?
    var subtitle: String?
    
    init(photoRecord: PhotoRecord) {
        self.photoRecord = photoRecord
        self.coordinate = photoRecord.location?.coordinate.clLocationCoordinate ?? CLLocationCoordinate2D()
        super.init()
    }
} 