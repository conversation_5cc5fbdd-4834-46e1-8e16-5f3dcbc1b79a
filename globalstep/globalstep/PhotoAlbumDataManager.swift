import Foundation
import UIKit
import CoreLocation
import Photos

class PhotoAlbumDataManager: NSObject {
    static let shared = PhotoAlbumDataManager()
    
    private var photoRecords: [PhotoRecord] = []
    private let userDefaults = UserDefaults.standard
    private let locationManager = CLLocationManager()
    private var currentLocation: CLLocation?
    private var watermarkConfig = WatermarkConfig.default
    
    private let photoRecordsKey = "PhotoRecords"
    private let watermarkConfigKey = "WatermarkConfig"
    
    // 文档目录路径
    private lazy var documentsDirectory: URL = {
        return FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
    }()
    
    // 照片存储目录
    private lazy var photosDirectory: URL = {
        let url = documentsDirectory.appendingPathComponent("Photos")
        try? FileManager.default.createDirectory(at: url, withIntermediateDirectories: true)
        return url
    }()
    
    // 缩略图存储目录
    private lazy var thumbnailsDirectory: URL = {
        let url = documentsDirectory.appendingPathComponent("Thumbnails")
        try? FileManager.default.createDirectory(at: url, withIntermediateDirectories: true)
        return url
    }()
    
    override init() {
        super.init()
        setupLocationManager()
        loadData()
    }
    
    // MARK: - 位置管理
    private func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        locationManager.requestWhenInUseAuthorization()
    }
    
    func requestLocationPermission() {
        locationManager.requestWhenInUseAuthorization()
    }
    
    func startLocationUpdates() {
        guard CLLocationManager.locationServicesEnabled() else { return }
        locationManager.startUpdatingLocation()
    }
    
    func stopLocationUpdates() {
        locationManager.stopUpdatingLocation()
    }
    
    // MARK: - 数据加载和保存
    private func loadData() {
        loadPhotoRecords()
        loadWatermarkConfig()
    }
    
    private func loadPhotoRecords() {
        if let data = userDefaults.data(forKey: photoRecordsKey),
           let records = try? JSONDecoder().decode([PhotoRecord].self, from: data) {
            photoRecords = records.sorted { $0.timestamp > $1.timestamp }
        }
    }
    
    private func savePhotoRecords() {
        if let data = try? JSONEncoder().encode(photoRecords) {
            userDefaults.set(data, forKey: photoRecordsKey)
        }
    }
    
    private func loadWatermarkConfig() {
        if let data = userDefaults.data(forKey: watermarkConfigKey),
           let config = try? JSONDecoder().decode(WatermarkConfig.self, from: data) {
            watermarkConfig = config
        }
    }
    
    private func saveWatermarkConfig() {
        if let data = try? JSONEncoder().encode(watermarkConfig) {
            userDefaults.set(data, forKey: watermarkConfigKey)
        }
    }
    
    // MARK: - 照片管理
    func addPhotoRecord(_ image: UIImage, customNote: String?, mood: MoodType?, tags: [String] = []) -> PhotoRecord? {
        let id = UUID().uuidString
        let timestamp = Date()
        
        // 保存原图和缩略图
        guard let imagePath = saveImage(image, withId: id),
              let thumbnailPath = saveThumbnail(image, withId: id) else {
            return nil
        }
        
        // 获取位置信息
        let locationInfo = getCurrentLocationInfo()
        
        // 获取天气信息
        let weatherInfo = getCurrentWeatherInfo()
        
        let record = PhotoRecord(
            id: id,
            timestamp: timestamp,
            location: locationInfo,
            weather: weatherInfo,
            customNote: customNote,
            imagePath: imagePath,
            thumbnailPath: thumbnailPath,
            tags: tags,
            mood: mood
        )
        
        photoRecords.insert(record, at: 0) // 插入到最前面
        savePhotoRecords()
        
        // 保存到系统相册
        saveToPhotoLibrary(image)
        
        return record
    }
    
    func deletePhotoRecord(_ recordId: String) {
        guard let index = photoRecords.firstIndex(where: { $0.id == recordId }) else { return }
        
        let record = photoRecords[index]
        
        // 删除本地文件
        deleteImageFiles(for: record)
        
        // 从数组中移除
        photoRecords.remove(at: index)
        savePhotoRecords()
    }
    
    func getPhotoRecords(filter: PhotoFilter = .all) -> [PhotoRecord] {
        var filteredRecords = photoRecords
        
        // 日期筛选
        if let dateRange = filter.dateRange {
            let interval = dateRange.dateInterval
            filteredRecords = filteredRecords.filter { record in
                interval.contains(record.timestamp)
            }
        }
        
        // 位置筛选
        if let location = filter.location, !location.isEmpty {
            filteredRecords = filteredRecords.filter { record in
                record.location?.displayAddress.contains(location) == true
            }
        }
        
        // 天气筛选
        if let weather = filter.weather {
            filteredRecords = filteredRecords.filter { record in
                record.weather?.condition == weather
            }
        }
        
        // 心情筛选
        if let mood = filter.mood {
            filteredRecords = filteredRecords.filter { record in
                record.mood == mood
            }
        }
        
        // 标签筛选
        if !filter.tags.isEmpty {
            filteredRecords = filteredRecords.filter { record in
                !Set(record.tags).isDisjoint(with: Set(filter.tags))
            }
        }
        
        return filteredRecords
    }
    
    func getPhotoRecord(by id: String) -> PhotoRecord? {
        return photoRecords.first { $0.id == id }
    }
    
    func updatePhotoRecord(_ recordId: String, location: LocationInfo? = nil, weather: WeatherInfo? = nil, customNote: String? = nil, mood: MoodType? = nil, tags: [String]? = nil) {
        guard let index = photoRecords.firstIndex(where: { $0.id == recordId }) else { return }
        
        let record = photoRecords[index]
        
        // 创建新的记录结构（因为PhotoRecord是struct）
        let updatedRecord = PhotoRecord(
            id: record.id,
            timestamp: record.timestamp,
            location: location ?? record.location,
            weather: weather ?? record.weather,
            customNote: customNote ?? record.customNote,
            imagePath: record.imagePath,
            thumbnailPath: record.thumbnailPath,
            tags: tags ?? record.tags,
            mood: mood ?? record.mood
        )
        
        photoRecords[index] = updatedRecord
        savePhotoRecords()
    }
    
    // MARK: - 图片处理
    private func saveImage(_ image: UIImage, withId id: String) -> String? {
        let filename = "\(id).jpg"
        let url = photosDirectory.appendingPathComponent(filename)
        
        guard let data = image.jpegData(compressionQuality: 0.8) else { return nil }
        
        do {
            try data.write(to: url)
            return url.path
        } catch {
            print("保存图片失败: \(error)")
            return nil
        }
    }
    
    private func saveThumbnail(_ image: UIImage, withId id: String) -> String? {
        let filename = "\(id)_thumb.jpg"
        let url = thumbnailsDirectory.appendingPathComponent(filename)
        
        // 生成缩略图
        let thumbnailSize = CGSize(width: 200, height: 200)
        let thumbnail = image.resized(to: thumbnailSize)
        
        guard let data = thumbnail.jpegData(compressionQuality: 0.7) else { return nil }
        
        do {
            try data.write(to: url)
            return url.path
        } catch {
            print("保存缩略图失败: \(error)")
            return nil
        }
    }
    
    private func deleteImageFiles(for record: PhotoRecord) {
        // 删除原图
        try? FileManager.default.removeItem(atPath: record.imagePath)
        
        // 删除缩略图
        try? FileManager.default.removeItem(atPath: record.thumbnailPath)
    }
    
    func loadImage(from path: String) -> UIImage? {
        return UIImage(contentsOfFile: path)
    }
    
    // MARK: - 水印处理
    func addWatermark(to image: UIImage, record: PhotoRecord) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: image.size)
        
        return renderer.image { context in
            // 绘制原图
            image.draw(at: .zero)
            
            // 准备水印文本
            var watermarkLines: [String] = []
            
            if watermarkConfig.showTime {
                watermarkLines.append("📅 \(record.formattedDate) \(record.formattedTime)")
            }
            
            if watermarkConfig.showLocation, let location = record.location {
                watermarkLines.append("📍 \(location.displayAddress)")
            }
            
            if watermarkConfig.showWeather, let weather = record.weather {
                watermarkLines.append("🌤 \(weather.displayDescription)")
            }
            
            if watermarkConfig.showCustomText, let note = record.customNote, !note.isEmpty {
                watermarkLines.append("💭 \(note)")
            }
            
            if let mood = record.mood {
                watermarkLines.append("\(mood.emoji) \(mood.displayName)")
            }
            
            guard !watermarkLines.isEmpty else { return }
            
            // 绘制水印
            drawWatermark(lines: watermarkLines, on: context.cgContext, imageSize: image.size)
        }
    }
    
    private func drawWatermark(lines: [String], on context: CGContext, imageSize: CGSize) {
        let font = UIFont.systemFont(ofSize: watermarkConfig.fontSize, weight: .medium)
        let textColor = watermarkConfig.textColor
        let backgroundColor = watermarkConfig.backgroundColor
        
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: textColor
        ]
        
        // 计算文本尺寸
        let lineHeight = font.lineHeight
        let padding: CGFloat = 12
        let lineSpacing: CGFloat = 4
        
        let maxWidth = lines.map { $0.size(withAttributes: attributes).width }.max() ?? 0
        let totalHeight = CGFloat(lines.count) * lineHeight + CGFloat(lines.count - 1) * lineSpacing
        
        let backgroundRect = CGRect(
            x: 0, y: 0,
            width: maxWidth + padding * 2,
            height: totalHeight + padding * 2
        )
        
        // 计算水印位置
        let watermarkPosition = calculateWatermarkPosition(
            backgroundRect: backgroundRect,
            imageSize: imageSize
        )
        
        // 绘制背景
        context.setFillColor(backgroundColor.cgColor)
        context.fill(watermarkPosition)
        
        // 绘制文本
        for (index, line) in lines.enumerated() {
            let textRect = CGRect(
                x: watermarkPosition.minX + padding,
                y: watermarkPosition.minY + padding + CGFloat(index) * (lineHeight + lineSpacing),
                width: maxWidth,
                height: lineHeight
            )
            
            line.draw(in: textRect, withAttributes: attributes)
        }
    }
    
    private func calculateWatermarkPosition(backgroundRect: CGRect, imageSize: CGSize) -> CGRect {
        let margin: CGFloat = 20
        
        switch watermarkConfig.position {
        case .topLeft:
            return CGRect(
                x: margin,
                y: margin,
                width: backgroundRect.width,
                height: backgroundRect.height
            )
            
        case .topRight:
            return CGRect(
                x: imageSize.width - backgroundRect.width - margin,
                y: margin,
                width: backgroundRect.width,
                height: backgroundRect.height
            )
            
        case .bottomLeft:
            return CGRect(
                x: margin,
                y: imageSize.height - backgroundRect.height - margin,
                width: backgroundRect.width,
                height: backgroundRect.height
            )
            
        case .bottomRight:
            return CGRect(
                x: imageSize.width - backgroundRect.width - margin,
                y: imageSize.height - backgroundRect.height - margin,
                width: backgroundRect.width,
                height: backgroundRect.height
            )
            
        case .center:
            return CGRect(
                x: (imageSize.width - backgroundRect.width) / 2,
                y: (imageSize.height - backgroundRect.height) / 2,
                width: backgroundRect.width,
                height: backgroundRect.height
            )
        }
    }
    
    // MARK: - 系统相册
    private func saveToPhotoLibrary(_ image: UIImage) {
        PHPhotoLibrary.requestAuthorization { status in
            guard status == .authorized else { return }
            
            PHPhotoLibrary.shared().performChanges({
                PHAssetChangeRequest.creationRequestForAsset(from: image)
            }) { success, error in
                if let error = error {
                    print("保存到相册失败: \(error)")
                }
            }
        }
    }
    
    // MARK: - 位置信息
    private func getCurrentLocationInfo() -> LocationInfo? {
        guard let location = currentLocation else { return nil }
        
        let coordinate = LocationCoordinate(location.coordinate)
        
        // 这里可以添加地理编码获取地址信息
        // 为了简化，暂时返回基本信息
        return LocationInfo(
            coordinate: coordinate,
            address: nil,
            city: nil,
            country: nil,
            placeName: nil
        )
    }
    
    // MARK: - 天气信息
    private func getCurrentWeatherInfo() -> WeatherInfo? {
        // 这里应该调用天气API获取实际天气信息
        // 为了演示，返回模拟数据
        let conditions: [WeatherCondition] = [.sunny, .cloudy, .partlyCloudy]
        let randomCondition = conditions.randomElement() ?? .sunny
        let randomTemperature = Double.random(in: 15...30)
        
        return WeatherInfo(
            temperature: randomTemperature,
            condition: randomCondition,
            humidity: Double.random(in: 40...80),
            windSpeed: Double.random(in: 0...15),
            description: randomCondition.displayName
        )
    }
    
    // MARK: - 统计信息
    func getPhotoStats() -> PhotoStats {
        let calendar = Calendar.current
        let now = Date()
        
        // 今天
        let startOfDay = calendar.startOfDay(for: now)
        let todayPhotos = photoRecords.filter { $0.timestamp >= startOfDay }.count
        
        // 本周
        let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
        let thisWeekPhotos = photoRecords.filter { $0.timestamp >= startOfWeek }.count
        
        // 本月
        let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now
        let thisMonthPhotos = photoRecords.filter { $0.timestamp >= startOfMonth }.count
        
        // 常用位置
        let locations = photoRecords.compactMap { $0.location?.displayAddress }
        let locationCounts = Dictionary(grouping: locations, by: { $0 })
            .mapValues { $0.count }
            .sorted { $0.value > $1.value }
        let favoriteLocations = Array(locationCounts.prefix(5).map { $0.key })
        
        // 心情分布
        let moods = photoRecords.compactMap { $0.mood }
        let moodDistribution = Dictionary(grouping: moods, by: { $0 })
            .mapValues { $0.count }
        
        // 天气分布
        let weathers = photoRecords.compactMap { $0.weather?.condition }
        let weatherDistribution = Dictionary(grouping: weathers, by: { $0 })
            .mapValues { $0.count }
        
        // 月度统计
        let monthlyGroups = Dictionary(grouping: photoRecords) { record in
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM"
            return formatter.string(from: record.timestamp)
        }
        let monthlyPhotoCount = monthlyGroups.mapValues { $0.count }
        
        return PhotoStats(
            totalPhotos: photoRecords.count,
            thisMonthPhotos: thisMonthPhotos,
            thisWeekPhotos: thisWeekPhotos,
            todayPhotos: todayPhotos,
            favoriteLocations: favoriteLocations,
            moodDistribution: moodDistribution,
            weatherDistribution: weatherDistribution,
            monthlyPhotoCount: monthlyPhotoCount
        )
    }
    
    // MARK: - 水印配置
    func getWatermarkConfig() -> WatermarkConfig {
        return watermarkConfig
    }
    
    func updateWatermarkConfig(_ config: WatermarkConfig) {
        watermarkConfig = config
        saveWatermarkConfig()
    }
    
    // MARK: - 测试数据
    func addTestData() {
        // 添加一些测试照片记录
        _ = createTestImage()  // 创建测试图片但不使用
        
        for i in 0..<5 {
            let daysAgo = i
            let timestamp = Calendar.current.date(byAdding: .day, value: -daysAgo, to: Date()) ?? Date()
            
            let moods: [MoodType] = [.happy, .excited, .calm, .relaxed]
            let weathers: [WeatherCondition] = [.sunny, .cloudy, .partlyCloudy]
            
            let record = PhotoRecord(
                id: UUID().uuidString,
                timestamp: timestamp,
                location: LocationInfo(
                    coordinate: LocationCoordinate(CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)),
                    address: "北京市朝阳区",
                    city: "北京",
                    country: "中国",
                    placeName: "天安门广场"
                ),
                weather: WeatherInfo(
                    temperature: Double.random(in: 15...25),
                    condition: weathers.randomElement() ?? .sunny,
                    humidity: Double.random(in: 40...80),
                    windSpeed: Double.random(in: 0...10),
                    description: "晴朗"
                ),
                customNote: "测试照片 \(i + 1)",
                imagePath: "",
                thumbnailPath: "",
                tags: ["测试", "打卡"],
                mood: moods.randomElement()
            )
            
            photoRecords.append(record)
        }
        
        savePhotoRecords()
    }
    
    private func createTestImage() -> UIImage {
        let size = CGSize(width: 300, height: 300)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            UIColor.systemBlue.setFill()
            context.fill(CGRect(origin: .zero, size: size))
            
            let text = "测试照片"
            let attributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 24, weight: .bold),
                .foregroundColor: UIColor.white
            ]
            
            let textSize = text.size(withAttributes: attributes)
            let textRect = CGRect(
                x: (size.width - textSize.width) / 2,
                y: (size.height - textSize.height) / 2,
                width: textSize.width,
                height: textSize.height
            )
            
            text.draw(in: textRect, withAttributes: attributes)
        }
    }
}

// MARK: - CLLocationManagerDelegate
extension PhotoAlbumDataManager: CLLocationManagerDelegate {
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        currentLocation = locations.last
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("位置获取失败: \(error)")
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            startLocationUpdates()
        case .denied, .restricted:
            print("位置权限被拒绝")
        case .notDetermined:
            requestLocationPermission()
        @unknown default:
            break
        }
    }
}

// MARK: - UIImage Extension
extension UIImage {
    func resized(to size: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { _ in
            self.draw(in: CGRect(origin: .zero, size: size))
        }
    }
} 