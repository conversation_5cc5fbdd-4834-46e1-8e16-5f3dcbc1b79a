import Foundation
import UIKit
import CoreLocation

// MARK: - 照片记录
struct PhotoRecord: Codable, Identifiable {
    let id: String
    let timestamp: Date
    let location: LocationInfo?
    let weather: WeatherInfo?
    let customNote: String?
    let imagePath: String // 本地存储路径
    let thumbnailPath: String // 缩略图路径
    let tags: [String] // 标签
    let mood: MoodType? // 心情
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        return formatter.string(from: timestamp)
    }
    
    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: timestamp)
    }
    
    var formattedDateTime: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        return formatter.string(from: timestamp)
    }
}

// MARK: - 地点信息
struct LocationInfo: Codable {
    let coordinate: LocationCoordinate
    let address: String?
    let city: String?
    let country: String?
    let placeName: String? // 地标名称
    
    var displayAddress: String {
        if let placeName = placeName, !placeName.isEmpty {
            return placeName
        }
        if let address = address, !address.isEmpty {
            return address
        }
        if let city = city, !city.isEmpty {
            return city
        }
        return "未知位置"
    }
}

// MARK: - 天气信息
struct WeatherInfo: Codable {
    let temperature: Double // 温度（摄氏度）
    let condition: WeatherCondition // 天气状况
    let humidity: Double? // 湿度
    let windSpeed: Double? // 风速
    let description: String // 天气描述
    
    var temperatureString: String {
        return String(format: "%.0f°C", temperature)
    }
    
    var displayDescription: String {
        return "\(condition.displayName) \(temperatureString)"
    }
}

// MARK: - 天气状况
enum WeatherCondition: String, Codable, CaseIterable {
    case sunny = "sunny"
    case cloudy = "cloudy"
    case rainy = "rainy"
    case snowy = "snowy"
    case foggy = "foggy"
    case windy = "windy"
    case stormy = "stormy"
    case partlyCloudy = "partly_cloudy"
    
    var displayName: String {
        switch self {
        case .sunny: return "晴朗"
        case .cloudy: return "多云"
        case .rainy: return "雨天"
        case .snowy: return "雪天"
        case .foggy: return "雾天"
        case .windy: return "大风"
        case .stormy: return "暴雨"
        case .partlyCloudy: return "晴转多云"
        }
    }
    
    var icon: String {
        switch self {
        case .sunny: return "sun.max.fill"
        case .cloudy: return "cloud.fill"
        case .rainy: return "cloud.rain.fill"
        case .snowy: return "cloud.snow.fill"
        case .foggy: return "cloud.fog.fill"
        case .windy: return "wind"
        case .stormy: return "cloud.bolt.rain.fill"
        case .partlyCloudy: return "cloud.sun.fill"
        }
    }
    
    var color: UIColor {
        switch self {
        case .sunny: return .systemYellow
        case .cloudy: return .systemGray
        case .rainy: return .systemBlue
        case .snowy: return .systemCyan
        case .foggy: return .systemGray2
        case .windy: return .systemGreen
        case .stormy: return .systemPurple
        case .partlyCloudy: return .systemOrange
        }
    }
}

// MARK: - 心情类型
enum MoodType: String, Codable, CaseIterable {
    case happy = "happy"
    case excited = "excited"
    case calm = "calm"
    case sad = "sad"
    case angry = "angry"
    case surprised = "surprised"
    case tired = "tired"
    case relaxed = "relaxed"
    
    var displayName: String {
        switch self {
        case .happy: return "开心"
        case .excited: return "兴奋"
        case .calm: return "平静"
        case .sad: return "难过"
        case .angry: return "生气"
        case .surprised: return "惊讶"
        case .tired: return "疲惫"
        case .relaxed: return "放松"
        }
    }
    
    var emoji: String {
        switch self {
        case .happy: return "😊"
        case .excited: return "🤩"
        case .calm: return "😌"
        case .sad: return "😢"
        case .angry: return "😠"
        case .surprised: return "😲"
        case .tired: return "😴"
        case .relaxed: return "😎"
        }
    }
    
    var color: UIColor {
        switch self {
        case .happy: return .systemYellow
        case .excited: return .systemOrange
        case .calm: return .systemBlue
        case .sad: return .systemIndigo
        case .angry: return .systemRed
        case .surprised: return .systemPink
        case .tired: return .systemGray
        case .relaxed: return .systemGreen
        }
    }
}

// MARK: - 水印配置
struct WatermarkConfig: Codable {
    let showLocation: Bool
    let showTime: Bool
    let showWeather: Bool
    let showCustomText: Bool
    let fontSize: CGFloat
    let textColor: UIColor
    let backgroundColor: UIColor
    let opacity: CGFloat
    let position: WatermarkPosition
    
    // 自定义编码/解码，因为UIColor不直接支持Codable
    enum CodingKeys: String, CodingKey {
        case showLocation, showTime, showWeather, showCustomText
        case fontSize, opacity, position
        case textColorData, backgroundColorData
    }
    
    init(showLocation: Bool, showTime: Bool, showWeather: Bool, showCustomText: Bool, fontSize: CGFloat, textColor: UIColor, backgroundColor: UIColor, opacity: CGFloat, position: WatermarkPosition) {
        self.showLocation = showLocation
        self.showTime = showTime
        self.showWeather = showWeather
        self.showCustomText = showCustomText
        self.fontSize = fontSize
        self.textColor = textColor
        self.backgroundColor = backgroundColor
        self.opacity = opacity
        self.position = position
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        showLocation = try container.decode(Bool.self, forKey: .showLocation)
        showTime = try container.decode(Bool.self, forKey: .showTime)
        showWeather = try container.decode(Bool.self, forKey: .showWeather)
        showCustomText = try container.decode(Bool.self, forKey: .showCustomText)
        fontSize = try container.decode(CGFloat.self, forKey: .fontSize)
        opacity = try container.decode(CGFloat.self, forKey: .opacity)
        position = try container.decode(WatermarkPosition.self, forKey: .position)
        
        // 解码UIColor
        if let textColorData = try container.decodeIfPresent(Data.self, forKey: .textColorData),
           let decodedTextColor = try NSKeyedUnarchiver.unarchivedObject(ofClass: UIColor.self, from: textColorData) {
            textColor = decodedTextColor
        } else {
            textColor = .white
        }
        
        if let backgroundColorData = try container.decodeIfPresent(Data.self, forKey: .backgroundColorData),
           let decodedBackgroundColor = try NSKeyedUnarchiver.unarchivedObject(ofClass: UIColor.self, from: backgroundColorData) {
            backgroundColor = decodedBackgroundColor
        } else {
            backgroundColor = .black.withAlphaComponent(0.6)
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(showLocation, forKey: .showLocation)
        try container.encode(showTime, forKey: .showTime)
        try container.encode(showWeather, forKey: .showWeather)
        try container.encode(showCustomText, forKey: .showCustomText)
        try container.encode(fontSize, forKey: .fontSize)
        try container.encode(opacity, forKey: .opacity)
        try container.encode(position, forKey: .position)
        
        // 编码UIColor
        let textColorData = try NSKeyedArchiver.archivedData(withRootObject: textColor, requiringSecureCoding: false)
        try container.encode(textColorData, forKey: .textColorData)
        
        let backgroundColorData = try NSKeyedArchiver.archivedData(withRootObject: backgroundColor, requiringSecureCoding: false)
        try container.encode(backgroundColorData, forKey: .backgroundColorData)
    }
    
    static let `default` = WatermarkConfig(
        showLocation: true,
        showTime: true,
        showWeather: true,
        showCustomText: true,
        fontSize: 16,
        textColor: .white,
        backgroundColor: .black.withAlphaComponent(0.6),
        opacity: 0.8,
        position: .bottomLeft
    )
}

// MARK: - 水印位置
enum WatermarkPosition: String, Codable, CaseIterable {
    case topLeft = "top_left"
    case topRight = "top_right"
    case bottomLeft = "bottom_left"
    case bottomRight = "bottom_right"
    case center = "center"
    
    var displayName: String {
        switch self {
        case .topLeft: return "左上角"
        case .topRight: return "右上角"
        case .bottomLeft: return "左下角"
        case .bottomRight: return "右下角"
        case .center: return "居中"
        }
    }
}

// MARK: - 照片统计
struct PhotoStats {
    let totalPhotos: Int
    let thisMonthPhotos: Int
    let thisWeekPhotos: Int
    let todayPhotos: Int
    let favoriteLocations: [String]
    let moodDistribution: [MoodType: Int]
    let weatherDistribution: [WeatherCondition: Int]
    let monthlyPhotoCount: [String: Int] // 月份 -> 照片数量
}

// MARK: - 照片筛选条件
struct PhotoFilter {
    let dateRange: DateRange?
    let location: String?
    let weather: WeatherCondition?
    let mood: MoodType?
    let tags: [String]
    
    static let all = PhotoFilter(
        dateRange: nil,
        location: nil,
        weather: nil,
        mood: nil,
        tags: []
    )
}

// MARK: - 日期范围
enum DateRange: CaseIterable {
    case today
    case thisWeek
    case thisMonth
    case thisYear
    case custom(start: Date, end: Date)
    
    var displayName: String {
        switch self {
        case .today: return "今天"
        case .thisWeek: return "本周"
        case .thisMonth: return "本月"
        case .thisYear: return "今年"
        case .custom: return "自定义"
        }
    }
    
    var dateInterval: DateInterval {
        let calendar = Calendar.current
        let now = Date()
        
        switch self {
        case .today:
            let startOfDay = calendar.startOfDay(for: now)
            let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) ?? now
            return DateInterval(start: startOfDay, end: endOfDay)
            
        case .thisWeek:
            let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
            let endOfWeek = calendar.date(byAdding: .weekOfYear, value: 1, to: startOfWeek) ?? now
            return DateInterval(start: startOfWeek, end: endOfWeek)
            
        case .thisMonth:
            let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now
            let endOfMonth = calendar.date(byAdding: .month, value: 1, to: startOfMonth) ?? now
            return DateInterval(start: startOfMonth, end: endOfMonth)
            
        case .thisYear:
            let startOfYear = calendar.dateInterval(of: .year, for: now)?.start ?? now
            let endOfYear = calendar.date(byAdding: .year, value: 1, to: startOfYear) ?? now
            return DateInterval(start: startOfYear, end: endOfYear)
            
        case .custom(let start, let end):
            return DateInterval(start: start, end: end)
        }
    }
    
    static var allCases: [DateRange] {
        return [.today, .thisWeek, .thisMonth, .thisYear]
    }
} 