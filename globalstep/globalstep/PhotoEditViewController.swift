import UIKit

protocol PhotoEditViewControllerDelegate: AnyObject {
    func photoEditViewController(_ controller: PhotoEditViewController, didFinishEditing image: UIImage, customNote: String?, mood: MoodType?, tags: [String])
    func photoEditViewControllerDidCancel(_ controller: PhotoEditViewController)
}

class PhotoEditViewController: UIViewController {
    
    // MARK: - Properties
    weak var delegate: PhotoEditViewControllerDelegate?
    var originalImage: UIImage?
    
    private var selectedMood: MoodType?
    private var selectedTags: [String] = []
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let imageView = UIImageView()
    private let noteTextView = UITextView()
    private let moodSelectionView = UIView()
    private let moodStackView = UIStackView()
    private let tagsView = UIView()
    private let tagsStackView = UIStackView()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        displayImage()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "编辑照片"
        view.backgroundColor = .systemBackground
        
        // 导航栏按钮
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .save,
            target: self,
            action: #selector(saveTapped)
        )
        
        setupScrollView()
        setupImageView()
        setupNoteTextView()
        setupMoodSelection()
        setupTagsSelection()
        setupConstraints()
    }
    
    private func setupScrollView() {
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
    }
    
    private func setupImageView() {
        imageView.contentMode = .scaleAspectFit
        imageView.layer.cornerRadius = 12
        imageView.clipsToBounds = true
        imageView.backgroundColor = .secondarySystemBackground
        imageView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(imageView)
    }
    
    private func setupNoteTextView() {
        let noteLabel = UILabel()
        noteLabel.text = "📝 添加备注"
        noteLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        noteLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(noteLabel)
        
        noteTextView.font = UIFont.systemFont(ofSize: 16)
        noteTextView.layer.cornerRadius = 8
        noteTextView.layer.borderColor = UIColor.separator.cgColor
        noteTextView.layer.borderWidth = 1
        noteTextView.backgroundColor = .secondarySystemBackground
        noteTextView.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        noteTextView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(noteTextView)
        
        // 占位符
        let placeholderLabel = UILabel()
        placeholderLabel.text = "记录此刻的心情和想法..."
        placeholderLabel.font = UIFont.systemFont(ofSize: 16)
        placeholderLabel.textColor = .placeholderText
        placeholderLabel.translatesAutoresizingMaskIntoConstraints = false
        noteTextView.addSubview(placeholderLabel)
        
        NSLayoutConstraint.activate([
            noteLabel.topAnchor.constraint(equalTo: imageView.bottomAnchor, constant: 30),
            noteLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            noteLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            
            noteTextView.topAnchor.constraint(equalTo: noteLabel.bottomAnchor, constant: 12),
            noteTextView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            noteTextView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            noteTextView.heightAnchor.constraint(equalToConstant: 100),
            
            placeholderLabel.topAnchor.constraint(equalTo: noteTextView.topAnchor, constant: 12),
            placeholderLabel.leadingAnchor.constraint(equalTo: noteTextView.leadingAnchor, constant: 16),
            placeholderLabel.trailingAnchor.constraint(equalTo: noteTextView.trailingAnchor, constant: -16)
        ])
        
        // 监听文本变化
        noteTextView.delegate = self
    }
    
    private func setupMoodSelection() {
        let moodLabel = UILabel()
        moodLabel.text = "😊 选择心情"
        moodLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        moodLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(moodLabel)
        
        moodSelectionView.backgroundColor = .secondarySystemBackground
        moodSelectionView.layer.cornerRadius = 12
        moodSelectionView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(moodSelectionView)
        
        moodStackView.axis = .horizontal
        moodStackView.distribution = .fillEqually
        moodStackView.spacing = 8
        moodStackView.translatesAutoresizingMaskIntoConstraints = false
        moodSelectionView.addSubview(moodStackView)
        
        // 添加心情按钮
        for mood in MoodType.allCases {
            let button = createMoodButton(for: mood)
            moodStackView.addArrangedSubview(button)
        }
        
        NSLayoutConstraint.activate([
            moodLabel.topAnchor.constraint(equalTo: noteTextView.bottomAnchor, constant: 30),
            moodLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            moodLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            
            moodSelectionView.topAnchor.constraint(equalTo: moodLabel.bottomAnchor, constant: 12),
            moodSelectionView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            moodSelectionView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            moodSelectionView.heightAnchor.constraint(equalToConstant: 80),
            
            moodStackView.topAnchor.constraint(equalTo: moodSelectionView.topAnchor, constant: 12),
            moodStackView.leadingAnchor.constraint(equalTo: moodSelectionView.leadingAnchor, constant: 12),
            moodStackView.trailingAnchor.constraint(equalTo: moodSelectionView.trailingAnchor, constant: -12),
            moodStackView.bottomAnchor.constraint(equalTo: moodSelectionView.bottomAnchor, constant: -12)
        ])
    }
    
    private func createMoodButton(for mood: MoodType) -> UIButton {
        let button = UIButton(type: .system)
        button.backgroundColor = .tertiarySystemBackground
        button.layer.cornerRadius = 8
        button.translatesAutoresizingMaskIntoConstraints = false
        
        let emojiLabel = UILabel()
        emojiLabel.text = mood.emoji
        emojiLabel.font = UIFont.systemFont(ofSize: 24)
        emojiLabel.textAlignment = .center
        emojiLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let nameLabel = UILabel()
        nameLabel.text = mood.displayName
        nameLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        nameLabel.textAlignment = .center
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        
        button.addSubview(emojiLabel)
        button.addSubview(nameLabel)
        
        NSLayoutConstraint.activate([
            emojiLabel.topAnchor.constraint(equalTo: button.topAnchor, constant: 8),
            emojiLabel.centerXAnchor.constraint(equalTo: button.centerXAnchor),
            
            nameLabel.topAnchor.constraint(equalTo: emojiLabel.bottomAnchor, constant: 4),
            nameLabel.centerXAnchor.constraint(equalTo: button.centerXAnchor),
            nameLabel.bottomAnchor.constraint(equalTo: button.bottomAnchor, constant: -8)
        ])
        
        button.addTarget(self, action: #selector(moodButtonTapped(_:)), for: .touchUpInside)
        button.tag = MoodType.allCases.firstIndex(of: mood) ?? 0
        
        return button
    }
    
    private func setupTagsSelection() {
        let tagsLabel = UILabel()
        tagsLabel.text = "🏷 添加标签"
        tagsLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        tagsLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(tagsLabel)
        
        tagsView.backgroundColor = .secondarySystemBackground
        tagsView.layer.cornerRadius = 12
        tagsView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(tagsView)
        
        tagsStackView.axis = .horizontal
        tagsStackView.spacing = 8
        tagsStackView.translatesAutoresizingMaskIntoConstraints = false
        tagsView.addSubview(tagsStackView)
        
        // 预设标签
        let predefinedTags = ["打卡", "美食", "旅行", "工作", "运动", "朋友", "家人", "学习"]
        for tag in predefinedTags {
            let button = createTagButton(for: tag)
            tagsStackView.addArrangedSubview(button)
        }
        
        NSLayoutConstraint.activate([
            tagsLabel.topAnchor.constraint(equalTo: moodSelectionView.bottomAnchor, constant: 30),
            tagsLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            tagsLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            
            tagsView.topAnchor.constraint(equalTo: tagsLabel.bottomAnchor, constant: 12),
            tagsView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            tagsView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            tagsView.heightAnchor.constraint(equalToConstant: 80),
            tagsView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20),
            
            tagsStackView.topAnchor.constraint(equalTo: tagsView.topAnchor, constant: 12),
            tagsStackView.leadingAnchor.constraint(equalTo: tagsView.leadingAnchor, constant: 12),
            tagsStackView.trailingAnchor.constraint(equalTo: tagsView.trailingAnchor, constant: -12),
            tagsStackView.bottomAnchor.constraint(equalTo: tagsView.bottomAnchor, constant: -12)
        ])
    }
    
    private func createTagButton(for tag: String) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(tag, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        button.backgroundColor = .tertiarySystemBackground
        button.setTitleColor(.label, for: .normal)
        button.layer.cornerRadius = 16
        button.contentEdgeInsets = UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16)
        button.translatesAutoresizingMaskIntoConstraints = false
        
        button.addTarget(self, action: #selector(tagButtonTapped(_:)), for: .touchUpInside)
        
        NSLayoutConstraint.activate([
            button.heightAnchor.constraint(equalToConstant: 32)
        ])
        
        return button
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 滚动视图
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            // 图片视图
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            imageView.heightAnchor.constraint(equalToConstant: 300)
        ])
    }
    
    // MARK: - Display Image
    private func displayImage() {
        imageView.image = originalImage
    }
    
    // MARK: - Actions
    @objc private func cancelTapped() {
        delegate?.photoEditViewControllerDidCancel(self)
    }
    
    @objc private func saveTapped() {
        guard let image = originalImage else { return }
        
        let customNote = noteTextView.text.isEmpty ? nil : noteTextView.text
        
        delegate?.photoEditViewController(
            self,
            didFinishEditing: image,
            customNote: customNote,
            mood: selectedMood,
            tags: selectedTags
        )
    }
    
    @objc private func moodButtonTapped(_ sender: UIButton) {
        let mood = MoodType.allCases[sender.tag]
        
        // 更新选中状态
        for (index, subview) in moodStackView.arrangedSubviews.enumerated() {
            if let button = subview as? UIButton {
                let isSelected = index == sender.tag
                button.backgroundColor = isSelected ? mood.color.withAlphaComponent(0.3) : .tertiarySystemBackground
                
                if isSelected {
                    selectedMood = mood
                } else if selectedMood == MoodType.allCases[index] {
                    selectedMood = nil
                }
            }
        }
    }
    
    @objc private func tagButtonTapped(_ sender: UIButton) {
        guard let title = sender.title(for: .normal) else { return }
        
        if selectedTags.contains(title) {
            // 取消选择
            selectedTags.removeAll { $0 == title }
            sender.backgroundColor = .tertiarySystemBackground
            sender.setTitleColor(.label, for: .normal)
        } else {
            // 选择标签
            selectedTags.append(title)
            sender.backgroundColor = .systemBlue
            sender.setTitleColor(.white, for: .normal)
        }
    }
}

// MARK: - UITextViewDelegate
extension PhotoEditViewController: UITextViewDelegate {
    func textViewDidChange(_ textView: UITextView) {
        // 隐藏/显示占位符
        if let placeholderLabel = textView.subviews.first(where: { $0 is UILabel }) as? UILabel {
            placeholderLabel.isHidden = !textView.text.isEmpty
        }
    }
} 