import UIKit

class TripPlanResultViewController: UIViewController {
    
    // MARK: - Properties
    var departureCity: String = ""
    var destinationCity: String = ""
    var days: String = ""
    var travelType: String = ""
    var travelPreferences: TravelPreferences?
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let headerView = UIView()
    private let planTableView = UITableView()
    private let loadingView = UIView()
    private let loadingLabel = UILabel()
    private let activityIndicator = UIActivityIndicatorView(style: .large)
    
    // Sample data for trip plans
    private var tripPlans: [TripPlan] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        updateTableViewHeight() // 初始化表格高度
        generateTripPlans()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 确保导航栏可见
        navigationController?.setNavigationBarHidden(false, animated: false)
        print("🎪 AI规划结果页面即将显示，导航栏状态: \(navigationController?.isNavigationBarHidden == false ? "可见" : "隐藏")")
    }
    
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        
        // Navigation
        navigationItem.title = "🤖 AI智能规划"
        
        // 创建更明显的返回按钮
        let backButton = UIBarButtonItem(
            title: "← 返回",
            style: .plain,
            target: self,
            action: #selector(backTapped)
        )
        backButton.tintColor = .systemBlue
        navigationItem.leftBarButtonItem = backButton
        
        // 确保导航栏可见
        navigationController?.setNavigationBarHidden(false, animated: false)
        navigationController?.navigationBar.isHidden = false
        
        print("🎭 设置返回按钮完成: \(navigationItem.leftBarButtonItem != nil ? "成功" : "失败")")
        print("🎪 导航栏状态: \(navigationController?.isNavigationBarHidden == false ? "可见" : "隐藏")")
        
        // Setup scroll view
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        // Setup header
        setupHeader()
        
        // Setup table view
        planTableView.translatesAutoresizingMaskIntoConstraints = false
        planTableView.delegate = self
        planTableView.dataSource = self
        planTableView.backgroundColor = .clear
        planTableView.separatorStyle = .none
        planTableView.register(TripPlanCell.self, forCellReuseIdentifier: "TripPlanCell")
        contentView.addSubview(planTableView)
        
        // Setup loading view
        setupLoadingView()
    }
    
    private func setupHeader() {
        headerView.translatesAutoresizingMaskIntoConstraints = false
        headerView.backgroundColor = .systemBackground
        headerView.layer.cornerRadius = 16
        headerView.layer.shadowColor = UIColor.black.cgColor
        headerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        headerView.layer.shadowRadius = 8
        headerView.layer.shadowOpacity = 0.1
        contentView.addSubview(headerView)
        
        // Cities and duration
        let routeLabel = UILabel()
        routeLabel.text = "\(departureCity) ⇄ \(destinationCity)"
        routeLabel.font = UIFont.boldSystemFont(ofSize: 20)
        routeLabel.textColor = .label
        routeLabel.translatesAutoresizingMaskIntoConstraints = false
        headerView.addSubview(routeLabel)
        
        let durationIcon = UILabel()
        durationIcon.text = "🕐"
        durationIcon.font = UIFont.systemFont(ofSize: 16)
        durationIcon.translatesAutoresizingMaskIntoConstraints = false
        headerView.addSubview(durationIcon)
        
        let durationLabel = UILabel()
        durationLabel.text = days
        durationLabel.font = UIFont.systemFont(ofSize: 16)
        durationLabel.textColor = .systemBlue
        durationLabel.translatesAutoresizingMaskIntoConstraints = false
        headerView.addSubview(durationLabel)
        
        NSLayoutConstraint.activate([
            routeLabel.leadingAnchor.constraint(equalTo: headerView.leadingAnchor, constant: 20),
            routeLabel.centerYAnchor.constraint(equalTo: headerView.centerYAnchor),
            
            durationIcon.trailingAnchor.constraint(equalTo: durationLabel.leadingAnchor, constant: -4),
            durationIcon.centerYAnchor.constraint(equalTo: headerView.centerYAnchor),
            
            durationLabel.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -20),
            durationLabel.centerYAnchor.constraint(equalTo: headerView.centerYAnchor)
        ])
    }
    
    private func setupLoadingView() {
        // 加载视图背景
        loadingView.translatesAutoresizingMaskIntoConstraints = false
        loadingView.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.95)
        loadingView.isHidden = true
        view.addSubview(loadingView)
        
        // 加载标签
        loadingLabel.translatesAutoresizingMaskIntoConstraints = false
        loadingLabel.text = "🤖 AI智能规划中\n\nDeepSeek正在根据您的偏好\n生成个性化行程..."
        loadingLabel.font = UIFont.boldSystemFont(ofSize: 18)
        loadingLabel.textColor = .label
        loadingLabel.textAlignment = .center
        loadingLabel.numberOfLines = 0
        loadingView.addSubview(loadingLabel)
        
        // 活动指示器
        activityIndicator.translatesAutoresizingMaskIntoConstraints = false
        activityIndicator.color = .systemBlue
        loadingView.addSubview(activityIndicator)
        
        // 设置约束
        NSLayoutConstraint.activate([
            loadingView.topAnchor.constraint(equalTo: view.topAnchor),
            loadingView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            loadingView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            loadingView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            loadingLabel.centerXAnchor.constraint(equalTo: loadingView.centerXAnchor),
            loadingLabel.centerYAnchor.constraint(equalTo: loadingView.centerYAnchor, constant: -30),
            loadingLabel.leadingAnchor.constraint(greaterThanOrEqualTo: loadingView.leadingAnchor, constant: 30),
            loadingLabel.trailingAnchor.constraint(lessThanOrEqualTo: loadingView.trailingAnchor, constant: -30),
            
            activityIndicator.centerXAnchor.constraint(equalTo: loadingView.centerXAnchor),
            activityIndicator.topAnchor.constraint(equalTo: loadingLabel.bottomAnchor, constant: 30)
        ])
    }
    
    private var tableHeightConstraint: NSLayoutConstraint!
    
    private func setupConstraints() {
        // 创建表格高度约束
        tableHeightConstraint = planTableView.heightAnchor.constraint(equalToConstant: 600) // 默认高度
        
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            headerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            headerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            headerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            headerView.heightAnchor.constraint(equalToConstant: 80),
            
            planTableView.topAnchor.constraint(equalTo: headerView.bottomAnchor, constant: 20),
            planTableView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            planTableView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            planTableView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20),
            tableHeightConstraint
        ])
    }
    
    private func updateTableViewHeight() {
        let cellHeight: CGFloat = 200
        let totalHeight = CGFloat(tripPlans.count) * cellHeight
        tableHeightConstraint.constant = totalHeight
        
        print("📏 更新表格高度: \(totalHeight) (行数: \(tripPlans.count), 行高: \(cellHeight))")
        
        // 强制布局更新
        view.setNeedsLayout()
        view.layoutIfNeeded()
    }
    
    private func generateTripPlans() {
        let dayCount = extractDayCount(from: days)
        let finalTravelType = !self.travelType.isEmpty ? self.travelType : extractTravelType()
        
        print("🚀 开始AI增强规划")
        print("📍 路线: \(departureCity) → \(destinationCity)")
        print("📅 天数: \(dayCount)")
        print("👥 旅行类型: \(finalTravelType)")
        print("🎯 用户偏好: \(travelPreferences?.interests ?? [])个兴趣, \(travelPreferences?.foods ?? [])个美食偏好, \(travelPreferences?.accommodations ?? [])个住宿偏好")
        
        // 显示加载界面
        showLoadingView(
            departure: departureCity,
            destination: destinationCity,
            days: dayCount,
            travelType: finalTravelType,
            interestsCount: travelPreferences?.interests.count ?? 0
        )
        
        // 优先使用AI增强的智能规划引擎，传入完整的用户偏好
        TripPlanningEngine.shared.generateAIEnhancedTripPlans(
            departure: departureCity,
            destination: destinationCity,
            days: dayCount,
            travelType: finalTravelType,
            preferences: travelPreferences ?? TravelPreferences(interests: [], foods: [], accommodations: [])
        ) { [weak self] aiPlans in
            DispatchQueue.main.async {
                // 隐藏加载界面
                self?.hideLoadingView()
                
                guard let self = self else { return }
                
                print("🎯 AI规划回调被调用")
                print("📊 AI返回的方案数量: \(aiPlans.count)")
                
                if !aiPlans.isEmpty {
                    print("✅ 使用AI生成的方案")
                    for (index, plan) in aiPlans.enumerated() {
                        print("📋 方案\(index + 1): \(plan.title)")
                    }
                    self.tripPlans = aiPlans
                } else {
                    print("⚠️ AI方案为空，使用本地算法")
                    // 如果AI规划失败，使用本地算法
                    self.tripPlans = TripPlanningEngine.shared.generateTripPlans(
                        departure: self.departureCity,
                        destination: self.destinationCity,
                        days: dayCount,
                        travelType: finalTravelType,
                        preferences: self.travelPreferences ?? TravelPreferences(interests: [], foods: [], accommodations: [])
                    )
                }
                
                // 如果仍然没有生成任何方案，提供默认方案
                if self.tripPlans.isEmpty {
                    print("🔧 提供默认方案")
                    self.tripPlans = [
                        TripPlan(
                            title: "\(self.destinationCity)经典文化游",
                            description: "从\(self.departureCity)出发，探索\(self.destinationCity)的文化魅力",
                            dayCount: dayCount,
                            attractionCount: 8,
                            attractions: ["市中心", "历史文化区", "特色美食街", "著名景点", "公园广场", "购物中心"],
                            dailyPlans: nil
                        )
                    ]
                }
                
                print("📱 最终方案数量: \(self.tripPlans.count)")
                print("🔄 刷新表格视图")
                self.planTableView.reloadData()
                
                // 更新表格视图的高度约束
                self.updateTableViewHeight()
                
                // 显示AI生成成功的提示
                if !aiPlans.isEmpty {
                    self.showSuccessMessage(planCount: aiPlans.count)
                }
                
                print("🎊 UI更新完成")
            }
        }
    }
    
    private func showLoadingView(departure: String, destination: String, days: Int, travelType: String, interestsCount: Int) {
        print("🎬 显示AI加载界面")
        loadingLabel.text = "🤖 AI智能规划中\n\nDeepSeek正在根据您的偏好生成个性化行程\n\n📍 \(departure) → \(destination)\n📅 \(days)天 👥 \(travelType)\n🎯 \(interestsCount)个兴趣偏好"
        
        loadingView.isHidden = false
        activityIndicator.startAnimating()
        
        // 确保加载视图在最上层
        view.bringSubviewToFront(loadingView)
        
        // 添加淡入动画
        loadingView.alpha = 0
        UIView.animate(withDuration: 0.3) {
            self.loadingView.alpha = 1
        }
        
        print("✅ AI加载界面已显示")
    }
    
    private func hideLoadingView() {
        print("🎭 隐藏AI加载界面")
        
        UIView.animate(withDuration: 0.3, animations: {
            self.loadingView.alpha = 0
        }) { _ in
            self.loadingView.isHidden = true
            self.activityIndicator.stopAnimating()
        }
        
        print("✅ AI加载界面已隐藏")
    }
    
    private func showSuccessMessage(planCount: Int) {
        let successAlert = UIAlertController(
            title: "✨ AI规划完成", 
            message: "已为您生成\(planCount)个个性化行程方案", 
            preferredStyle: .alert
        )
        successAlert.addAction(UIAlertAction(title: "查看行程", style: .default))
        present(successAlert, animated: true)
    }
    
    private func extractTravelType() -> String {
        // 这里可以根据实际需求获取旅行类型
        return "情侣" // 默认值
    }
    
    private func extractDayCount(from daysString: String) -> Int {
        return Int(daysString.replacingOccurrences(of: "天", with: "")) ?? 2
    }
    
    @objc private func backTapped() {
        print("🔙 返回按钮被点击")
        DispatchQueue.main.async {
            self.navigationController?.popViewController(animated: true)
        }
    }
}

// MARK: - UITableViewDataSource, UITableViewDelegate
extension TripPlanResultViewController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        print("📊 表格视图询问行数: \(tripPlans.count)")
        return tripPlans.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        print("📝 创建第\(indexPath.row)行单元格: \(tripPlans[indexPath.row].title)")
        let cell = tableView.dequeueReusableCell(withIdentifier: "TripPlanCell", for: indexPath) as! TripPlanCell
        cell.configure(with: tripPlans[indexPath.row], departure: departureCity, destination: destinationCity)
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        print("📏 第\(indexPath.row)行高度: 200")
        return 200
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        // 保存选中的行程到数据管理器
        let selectedPlan = tripPlans[indexPath.row]
        let tripRecord = TripRecord(
            id: UUID().uuidString,
            title: selectedPlan.title,
            destination: destinationCity,
            departure: departureCity,
            startDate: Date(),
            endDate: Calendar.current.date(byAdding: .day, value: selectedPlan.dayCount, to: Date()) ?? Date(),
            dayCount: selectedPlan.dayCount,
            attractions: selectedPlan.attractions,
            currentDay: 1,
            status: .planning, // 刚创建的行程应该是规划中状态
            createdDate: Date(),
            lastUpdated: Date()
        )
        
        // 保存到数据管理器
        TripDataManager.shared.saveTrip(tripRecord)
        print("💾 已保存行程到数据管理器: \(tripRecord.title)")
        
        // 显示保存成功提示
        let saveAlert = UIAlertController(title: "✅ 行程已保存", message: "「\(selectedPlan.title)」已保存到本地，您可以在首页「进行中」列表查看和管理此行程", preferredStyle: .alert)
        saveAlert.addAction(UIAlertAction(title: "查看地图", style: .default) { _ in
            // 跳转到地图页面
            let mapVC = TripMapViewController()
            mapVC.tripPlans = self.tripPlans
            mapVC.currentPlanIndex = indexPath.row
            mapVC.departureCity = self.departureCity
            mapVC.destinationCity = self.destinationCity
            self.navigationController?.pushViewController(mapVC, animated: true)
        })
        saveAlert.addAction(UIAlertAction(title: "返回首页", style: .default) { _ in
            // 直接返回到首页
            self.navigationController?.popToRootViewController(animated: true)
        })
        present(saveAlert, animated: true)
    }
}

// MARK: - Models (现在定义在 TripModels.swift 中)

// MARK: - Custom Cell
class TripPlanCell: UITableViewCell {
    private let containerView = UIView()
    private let titleLabel = UILabel()
    private let departureLabel = UILabel()
    private let destinationLabel = UILabel()
    private let daysLabel = UILabel()
    private let attractionsLabel = UILabel()
    private let attractionsList = UILabel()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear
        
        // Container
        containerView.translatesAutoresizingMaskIntoConstraints = false
        containerView.backgroundColor = .systemBackground
        containerView.layer.cornerRadius = 16
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowRadius = 8
        containerView.layer.shadowOpacity = 0.1
        contentView.addSubview(containerView)
        
        // Title
        titleLabel.font = UIFont.boldSystemFont(ofSize: 16)
        titleLabel.textColor = .label
        titleLabel.numberOfLines = 0
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(titleLabel)
        
        // Departure city
        departureLabel.font = UIFont.systemFont(ofSize: 14)
        departureLabel.textColor = .systemGreen
        departureLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(departureLabel)
        
        // Destination city
        destinationLabel.font = UIFont.systemFont(ofSize: 14)
        destinationLabel.textColor = .systemRed
        destinationLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(destinationLabel)
        
        // Days and attractions
        daysLabel.font = UIFont.systemFont(ofSize: 14)
        daysLabel.textColor = .systemBlue
        daysLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(daysLabel)
        
        attractionsLabel.font = UIFont.systemFont(ofSize: 14)
        attractionsLabel.textColor = .systemBlue
        attractionsLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(attractionsLabel)
        
        // Attractions list
        attractionsList.font = UIFont.systemFont(ofSize: 12)
        attractionsList.textColor = .secondaryLabel
        attractionsList.numberOfLines = 0
        attractionsList.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(attractionsList)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),
            
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            
            departureLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 12),
            departureLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            
            destinationLabel.topAnchor.constraint(equalTo: departureLabel.bottomAnchor, constant: 6),
            destinationLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            
            daysLabel.centerYAnchor.constraint(equalTo: departureLabel.centerYAnchor),
            daysLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            
            attractionsLabel.centerYAnchor.constraint(equalTo: destinationLabel.centerYAnchor),
            attractionsLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            
            attractionsList.topAnchor.constraint(equalTo: destinationLabel.bottomAnchor, constant: 12),
            attractionsList.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            attractionsList.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            attractionsList.bottomAnchor.constraint(lessThanOrEqualTo: containerView.bottomAnchor, constant: -16)
        ])
    }
    
    func configure(with tripPlan: TripPlan, departure: String = "济南市", destination: String = "上海市") {
        titleLabel.text = tripPlan.title
        departureLabel.text = "● \(departure)"
        destinationLabel.text = "● \(destination)  \(tripPlan.dayCount)天 • \(tripPlan.attractionCount)个地点"
        
        // 如果有详细的每日计划，显示每日概览
        if let dailyPlans = tripPlan.dailyPlans, !dailyPlans.isEmpty {
            let dailyOverview = dailyPlans.prefix(3).map { dayPlan in
                let activityCount = dayPlan.activities.count
                return "Day\(dayPlan.day): \(activityCount)个活动"
            }.joined(separator: " • ")
            
            attractionsList.text = "\(tripPlan.description)\n\n📅 行程概览：\(dailyOverview)"
        } else {
            // 显示景点列表
            let attractionsText = tripPlan.attractions.joined(separator: "  •  ")
            attractionsList.text = "🏛️ 主要景点：\(attractionsText)"
        }
    }
} 