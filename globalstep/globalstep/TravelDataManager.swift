import Foundation
import CoreLocation

// MARK: - Travel Data Models
struct TravelCity {
    let id: String
    let name: String
    let province: String
    let coordinate: CLLocationCoordinate2D
    let description: String
    let attractions: [Attraction]
    let restaurants: [Restaurant]
    let hotels: [Hotel]
    let tags: [String] // 城市特色标签
}

struct AttractionDetail {
    let id: String
    let name: String
    let cityId: String
    let coordinate: CLLocationCoordinate2D
    let category: AttractionCategory
    let description: String
    let openTime: String
    let ticketPrice: String
    let visitDuration: Int // 建议游玩时长（分钟）
    let popularity: Int // 热门程度 1-10
    let tags: [String] // 景点标签
    let imageURL: String?
    let rating: Double // 评分 1-5
}

struct Restaurant {
    let id: String
    let name: String
    let cityId: String
    let coordinate: CLLocationCoordinate2D
    let cuisine: CuisineType
    let priceLevel: PriceLevel
    let description: String
    let specialties: [String]
    let rating: Double
    let tags: [String]
}

struct Hotel {
    let id: String
    let name: String
    let cityId: String
    let coordinate: CLLocationCoordinate2D
    let hotelType: HotelType
    let priceLevel: PriceLevel
    let amenities: [String]
    let rating: Double
    let description: String
}

// MARK: - Enums
enum AttractionCategory: String, CaseIterable {
    case historical = "历史文化"
    case natural = "自然风光"
    case modern = "现代景观"
    case religious = "宗教建筑"
    case museum = "博物馆"
    case park = "公园休闲"
    case shopping = "购物娱乐"
    case art = "艺术创意"
    case food = "美食体验"
    case adventure = "户外冒险"
    case family = "亲子乐园"
    case nightlife = "夜生活"
}

enum CuisineType: String, CaseIterable {
    case local = "本地特色"
    case chinese = "中餐"
    case western = "西餐"
    case japanese = "日韩料理"
    case street = "街头小吃"
    case fine = "高端餐厅"
    case hotpot = "火锅"
    case seafood = "海鲜"
    case vegetarian = "素食"
    case dessert = "甜品茶饮"
}

enum HotelType: String, CaseIterable {
    case luxury = "豪华酒店"
    case business = "商务酒店"
    case boutique = "精品酒店"
    case resort = "度假村"
    case hostel = "青年旅社"
    case bnb = "民宿客栈"
    case apartment = "公寓式酒店"
}

enum PriceLevel: String, CaseIterable {
    case budget = "经济实惠"
    case moderate = "中等价位"
    case expensive = "高端消费"
    case luxury = "奢华体验"
}

// MARK: - Travel Data Manager
class TravelDataManager {
    static let shared = TravelDataManager()
    
    private var cities: [TravelCity] = []
    private var attractions: [AttractionDetail] = []
    private var restaurants: [Restaurant] = []
    private var hotels: [Hotel] = []
    
    private init() {
        loadData()
    }
    
    private func loadData() {
        loadCities()
        loadAttractions()
        loadRestaurants()
        loadHotels()
    }
    
    // MARK: - Data Loading
    private func loadCities() {
        cities = [
            // 北京
            TravelCity(
                id: "beijing",
                name: "北京市",
                province: "北京市",
                coordinate: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
                description: "中华人民共和国首都，历史文化名城",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["历史文化", "皇家建筑", "现代都市", "美食天堂"]
            ),
            
            // 天津
            TravelCity(
                id: "tianjin",
                name: "天津市",
                province: "天津市",
                coordinate: CLLocationCoordinate2D(latitude: 39.1304, longitude: 117.1892),
                description: "海河之畔的历史名城，中西文化交融之地",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["津门文化", "海河风光", "意式风情", "传统小吃"]
            ),
            
            // 上海
            TravelCity(
                id: "shanghai",
                name: "上海市",
                province: "上海市",
                coordinate: CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737),
                description: "国际化大都市，东方巴黎",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["国际都市", "摩天大楼", "江南文化", "时尚购物"]
            ),
            
            // 杭州
            TravelCity(
                id: "hangzhou",
                name: "杭州市",
                province: "浙江省",
                coordinate: CLLocationCoordinate2D(latitude: 30.2741, longitude: 120.1551),
                description: "人间天堂，西湖美景甲天下",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["西湖美景", "江南水乡", "丝绸文化", "茶文化"]
            ),
            
            // 西安
            TravelCity(
                id: "xian",
                name: "西安市",
                province: "陕西省",
                coordinate: CLLocationCoordinate2D(latitude: 34.3416, longitude: 108.9398),
                description: "十三朝古都，历史文化底蕴深厚",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["古都文化", "兵马俑", "城墙古迹", "陕西美食"]
            ),
            
            // 成都
            TravelCity(
                id: "chengdu",
                name: "成都市",
                province: "四川省",
                coordinate: CLLocationCoordinate2D(latitude: 30.5728, longitude: 104.0668),
                description: "天府之国，熊猫故乡，美食之都",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["大熊猫", "川菜美食", "休闲生活", "古蜀文化"]
            ),
            
            // 重庆
            TravelCity(
                id: "chongqing",
                name: "重庆市",
                province: "重庆市",
                coordinate: CLLocationCoordinate2D(latitude: 29.5647, longitude: 106.5507),
                description: "山城雾都，火锅之都，长江之滨",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["山城景观", "火锅文化", "长江三峡", "夜景灯火"]
            ),
            
            // 广州
            TravelCity(
                id: "guangzhou",
                name: "广州市",
                province: "广东省",
                coordinate: CLLocationCoordinate2D(latitude: 23.1291, longitude: 113.2644),
                description: "千年商都，岭南文化中心，美食天堂",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["粤菜文化", "商贸中心", "岭南建筑", "花城美景"]
            ),
            
            // 深圳
            TravelCity(
                id: "shenzhen",
                name: "深圳市",
                province: "广东省",
                coordinate: CLLocationCoordinate2D(latitude: 22.5431, longitude: 114.0579),
                description: "改革开放前沿，现代化国际城市",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["科技创新", "现代都市", "主题公园", "海滨风光"]
            ),
            
            // 厦门
            TravelCity(
                id: "xiamen",
                name: "厦门市",
                province: "福建省",
                coordinate: CLLocationCoordinate2D(latitude: 24.4798, longitude: 118.0894),
                description: "海上花园，鼓浪屿风情，闽南文化",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["海岛风情", "鼓浪屿", "闽南文化", "海鲜美食"]
            ),
            
            // 南京
            TravelCity(
                id: "nanjing",
                name: "南京市",
                province: "江苏省",
                coordinate: CLLocationCoordinate2D(latitude: 32.0603, longitude: 118.7969),
                description: "六朝古都，金陵名城，历史文化深厚",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["六朝古都", "明城墙", "秦淮河", "梧桐叶落"]
            ),
            
            // 苏州
            TravelCity(
                id: "suzhou",
                name: "苏州市",
                province: "江苏省",
                coordinate: CLLocationCoordinate2D(latitude: 31.2989, longitude: 120.5853),
                description: "人间天堂，园林之都，丝绸之乡",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["古典园林", "水乡古镇", "丝绸文化", "昆曲艺术"]
            ),
            
            // 武汉
            TravelCity(
                id: "wuhan",
                name: "武汉市",
                province: "湖北省",
                coordinate: CLLocationCoordinate2D(latitude: 30.5928, longitude: 114.3055),
                description: "九省通衢，江城风情，长江中游中心城市",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["长江汉江", "黄鹤楼", "樱花盛开", "热干面"]
            ),
            
            // 长沙
            TravelCity(
                id: "changsha",
                name: "长沙市",
                province: "湖南省",
                coordinate: CLLocationCoordinate2D(latitude: 28.2282, longitude: 112.9388),
                description: "星城长沙，湘江之滨，娱乐之都",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["橘子洲头", "湘菜文化", "娱乐产业", "岳麓山"]
            ),
            
            // 青岛
            TravelCity(
                id: "qingdao",
                name: "青岛市",
                province: "山东省",
                coordinate: CLLocationCoordinate2D(latitude: 36.0671, longitude: 120.3826),
                description: "红瓦绿树，碧海蓝天，啤酒之城",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["海滨城市", "啤酒文化", "德式建筑", "海鲜美食"]
            ),
            
            // 大连
            TravelCity(
                id: "dalian",
                name: "大连市",
                province: "辽宁省",
                coordinate: CLLocationCoordinate2D(latitude: 38.9140, longitude: 121.6147),
                description: "浪漫之都，海滨明珠，东北之窗",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["海滨风光", "广场文化", "俄式建筑", "海鲜大连"]
            ),
            
            // 昆明
            TravelCity(
                id: "kunming",
                name: "昆明市",
                province: "云南省",
                coordinate: CLLocationCoordinate2D(latitude: 25.0389, longitude: 102.7183),
                description: "春城昆明，四季如春，民族风情",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["春城花都", "滇池风光", "民族文化", "鲜花饼"]
            ),
            
            // 丽江
            TravelCity(
                id: "lijiang",
                name: "丽江市",
                province: "云南省",
                coordinate: CLLocationCoordinate2D(latitude: 26.8551, longitude: 100.2309),
                description: "古城丽江，纳西文化，雪山古镇",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["古城文化", "纳西族", "玉龙雪山", "艳遇之都"]
            ),
            
            // 大理
            TravelCity(
                id: "dali",
                name: "大理市",
                province: "云南省",
                coordinate: CLLocationCoordinate2D(latitude: 25.6956, longitude: 100.2417),
                description: "风花雪月，洱海苍山，白族文化",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["洱海风光", "苍山雪景", "白族文化", "古城韵味"]
            ),
            
            // 桂林
            TravelCity(
                id: "guilin",
                name: "桂林市",
                province: "广西壮族自治区",
                coordinate: CLLocationCoordinate2D(latitude: 25.2736, longitude: 110.2900),
                description: "桂林山水甲天下，漓江风光如画",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["山水甲天下", "漓江风光", "溶洞奇观", "米粉文化"]
            ),
            
            // 三亚
            TravelCity(
                id: "sanya",
                name: "三亚市",
                province: "海南省",
                coordinate: CLLocationCoordinate2D(latitude: 18.2528, longitude: 109.5113),
                description: "热带天堂，椰风海韵，度假胜地",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["热带海滨", "椰林沙滩", "度假天堂", "海南美食"]
            ),
            
            // 哈尔滨
            TravelCity(
                id: "harbin",
                name: "哈尔滨市",
                province: "黑龙江省",
                coordinate: CLLocationCoordinate2D(latitude: 45.8038, longitude: 126.5349),
                description: "冰城雪都，东方莫斯科，冰雪文化",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["冰雪文化", "俄式风情", "中央大街", "冰灯艺术"]
            ),
            
            // 乌鲁木齐
            TravelCity(
                id: "urumqi",
                name: "乌鲁木齐市",
                province: "新疆维吾尔自治区",
                coordinate: CLLocationCoordinate2D(latitude: 43.8256, longitude: 87.6168),
                description: "丝路明珠，民族风情，西域文化",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["丝绸之路", "民族风情", "天山雪景", "新疆美食"]
            ),
            
            // 拉萨
            TravelCity(
                id: "lhasa",
                name: "拉萨市",
                province: "西藏自治区",
                coordinate: CLLocationCoordinate2D(latitude: 29.6520, longitude: 91.1721),
                description: "日光城拉萨，雪域高原，藏传佛教圣地",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["雪域高原", "藏传佛教", "布达拉宫", "圣城文化"]
            ),
            
            // 张家界
            TravelCity(
                id: "zhangjiajie",
                name: "张家界市",
                province: "湖南省",
                coordinate: CLLocationCoordinate2D(latitude: 29.1274, longitude: 110.4790),
                description: "奇峰异石，天下奇观，阿凡达取景地",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["石柱奇峰", "天门山", "玻璃栈道", "土家文化"]
            ),
            
            // 黄山
            TravelCity(
                id: "huangshan",
                name: "黄山市",
                province: "安徽省",
                coordinate: CLLocationCoordinate2D(latitude: 29.7155, longitude: 118.3375),
                description: "天下第一奇山，五岳归来不看山",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["奇松怪石", "云海日出", "温泉胜地", "徽州文化"]
            ),
            
            // 曲靖
            TravelCity(
                id: "qujing",
                name: "曲靖市",
                province: "云南省",
                coordinate: CLLocationCoordinate2D(latitude: 25.5016, longitude: 103.7834),
                description: "云南第二大城市，珠江源头，秀丽的田园风光",
                attractions: [],
                restaurants: [],
                hotels: [],
                tags: ["珠江源头", "田园风光", "彝族文化", "油菜花海"]
            )
        ]
    }
    
    private func loadAttractions() {
        attractions = [
            // 天津景点 - 真实GPS坐标
            AttractionDetail(
                id: "tianjin_railway_station",
                name: "天津站",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1467, longitude: 117.2056),
                category: .modern,
                description: "天津市主要火车站，交通枢纽",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 30,
                popularity: 6,
                tags: ["交通枢纽", "出发点", "现代建筑"],
                imageURL: nil,
                rating: 4.0
            ),
            
            AttractionDetail(
                id: "tianjin_eye",
                name: "天津之眼",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1345, longitude: 117.1678),
                category: .modern,
                description: "世界上唯一建在桥上的摩天轮，天津地标性建筑",
                openTime: "09:30-21:30",
                ticketPrice: "70元",
                visitDuration: 60,
                popularity: 9,
                tags: ["地标建筑", "摩天轮", "海河夜景", "浪漫约会"],
                imageURL: nil,
                rating: 4.5
            ),
            
            AttractionDetail(
                id: "tianjin_ancient_culture_street",
                name: "古文化街（津门故里）",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1234, longitude: 117.1890),
                category: .historical,
                description: "津门故里，传统文化商业街，天津民俗文化的缩影",
                openTime: "09:00-21:00",
                ticketPrice: "免费",
                visitDuration: 120,
                popularity: 8,
                tags: ["津门文化", "传统建筑", "民俗体验", "特色购物"],
                imageURL: nil,
                rating: 4.3
            ),
            
            AttractionDetail(
                id: "goubuli_baozi_gulou",
                name: "狗不理包子（鼓楼店）",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1200, longitude: 117.1950),
                category: .food,
                description: "天津三绝之一，百年老字号包子店",
                openTime: "06:00-21:00",
                ticketPrice: "人均30-50元",
                visitDuration: 45,
                popularity: 7,
                tags: ["天津三绝", "传统美食", "包子", "老字号"],
                imageURL: nil,
                rating: 4.2
            ),
            
            AttractionDetail(
                id: "tianjin_italian_style_town",
                name: "意大利风情区",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1423, longitude: 117.1789),
                category: .historical,
                description: "亚洲唯一的意大利风情建筑群，欧式浪漫风情",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 90,
                popularity: 8,
                tags: ["欧式建筑", "意大利风情", "拍照圣地", "咖啡文化"],
                imageURL: nil,
                rating: 4.4
            ),
            
            AttractionDetail(
                id: "jinwan_plaza",
                name: "津湾广场",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1234, longitude: 117.1567),
                category: .shopping,
                description: "海河边的现代商业广场，购物娱乐中心",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 60,
                popularity: 7,
                tags: ["购物中心", "海河景观", "现代建筑", "休闲娱乐"],
                imageURL: nil,
                rating: 4.1
            ),
            
            AttractionDetail(
                id: "tianjin_five_avenue",
                name: "五大道历史文化区",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1145, longitude: 117.1789),
                category: .historical,
                description: "万国建筑博览区，近代历史建筑群",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 150,
                popularity: 9,
                tags: ["万国建筑", "历史文化", "骑行游览", "民国风情"],
                imageURL: nil,
                rating: 4.6
            ),
            
            AttractionDetail(
                id: "xikai_cathedral",
                name: "西开教堂",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1178, longitude: 117.1834),
                category: .religious,
                description: "天津最大的天主教堂，哥特式建筑风格",
                openTime: "08:00-11:30, 14:00-16:00",
                ticketPrice: "免费",
                visitDuration: 40,
                popularity: 7,
                tags: ["宗教建筑", "哥特式", "历史建筑", "文化遗产"],
                imageURL: nil,
                rating: 4.3
            ),
            
            AttractionDetail(
                id: "tianjin_porcelain_house",
                name: "瓷房子",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1123, longitude: 117.1945),
                category: .art,
                description: "用古瓷片装饰的法式洋楼，独特的艺术建筑",
                openTime: "09:00-18:00",
                ticketPrice: "50元",
                visitDuration: 45,
                popularity: 7,
                tags: ["艺术建筑", "瓷器文化", "创意设计", "拍照景点"],
                imageURL: nil,
                rating: 4.2
            ),
            
            AttractionDetail(
                id: "tianjin_haihe_river",
                name: "海河",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1267, longitude: 117.1889),
                category: .natural,
                description: "天津母亲河，两岸风光秀丽，夜景迷人",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 120,
                popularity: 8,
                tags: ["海河夜景", "游船观光", "滨水漫步", "摄影天堂"],
                imageURL: nil,
                rating: 4.5
            ),
            
            AttractionDetail(
                id: "erduo_yan_zhagao_store",
                name: "耳朵眼炸糕总店",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1180, longitude: 117.1920),
                category: .food,
                description: "天津三绝之一，传统炸糕名店",
                openTime: "07:00-20:00",
                ticketPrice: "人均10-20元",
                visitDuration: 30,
                popularity: 6,
                tags: ["天津三绝", "传统糕点", "炸糕", "老字号"],
                imageURL: nil,
                rating: 4.1
            ),
            
            // 上海景点 - 真实GPS坐标
            AttractionDetail(
                id: "shanghai_railway_station",
                name: "上海站",
                cityId: "shanghai",
                coordinate: CLLocationCoordinate2D(latitude: 31.2496, longitude: 121.4564),
                category: .modern,
                description: "上海市主要火车站之一，交通枢纽",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 30,
                popularity: 6,
                tags: ["交通枢纽", "出发点", "现代建筑"],
                imageURL: nil,
                rating: 4.0
            ),
            
            AttractionDetail(
                id: "shanghai_bund",
                name: "外滩",
                cityId: "shanghai",
                coordinate: CLLocationCoordinate2D(latitude: 31.2395, longitude: 121.4906),
                category: .historical,
                description: "上海标志性景点，黄浦江滨江大道，万国建筑博览群",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 120,
                popularity: 10,
                tags: ["万国建筑", "黄浦江", "夜景", "历史文化"],
                imageURL: nil,
                rating: 4.8
            ),
            
            AttractionDetail(
                id: "shanghai_oriental_pearl",
                name: "东方明珠塔",
                cityId: "shanghai",
                coordinate: CLLocationCoordinate2D(latitude: 31.2397, longitude: 121.4999),
                category: .modern,
                description: "上海地标性建筑，468米高的电视塔",
                openTime: "08:00-21:30",
                ticketPrice: "160元",
                visitDuration: 90,
                popularity: 9,
                tags: ["地标建筑", "观景台", "夜景", "电视塔"],
                imageURL: nil,
                rating: 4.5
            ),
            
            AttractionDetail(
                id: "shanghai_nanjing_road",
                name: "南京路步行街",
                cityId: "shanghai",
                coordinate: CLLocationCoordinate2D(latitude: 31.2352, longitude: 121.4752),
                category: .shopping,
                description: "中华商业第一街，购物天堂",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 150,
                popularity: 8,
                tags: ["购物天堂", "步行街", "商业中心", "老字号"],
                imageURL: nil,
                rating: 4.4
            ),
            
            AttractionDetail(
                id: "shanghai_yu_garden",
                name: "豫园",
                cityId: "shanghai",
                coordinate: CLLocationCoordinate2D(latitude: 31.2270, longitude: 121.4917),
                category: .historical,
                description: "明代私人花园，江南古典园林代表",
                openTime: "08:30-17:00",
                ticketPrice: "40元",
                visitDuration: 120,
                popularity: 8,
                tags: ["古典园林", "明代建筑", "传统文化", "小笼包"],
                imageURL: nil,
                rating: 4.3
            ),
            
            AttractionDetail(
                id: "shanghai_french_concession",
                name: "法租界",
                cityId: "shanghai",
                coordinate: CLLocationCoordinate2D(latitude: 31.2132, longitude: 121.4652),
                category: .historical,
                description: "上海历史文化风貌区，梧桐叶影法式风情",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 180,
                popularity: 7,
                tags: ["法式建筑", "历史文化", "梧桐大道", "咖啡文化"],
                imageURL: nil,
                rating: 4.2
            ),
            
            // 杭州景点 - 真实GPS坐标
            AttractionDetail(
                id: "hangzhou_railway_station",
                name: "杭州站",
                cityId: "hangzhou",
                coordinate: CLLocationCoordinate2D(latitude: 30.2517, longitude: 120.1812),
                category: .modern,
                description: "杭州市主要火车站，交通枢纽",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 30,
                popularity: 6,
                tags: ["交通枢纽", "出发点", "现代建筑"],
                imageURL: nil,
                rating: 4.0
            ),
            
            AttractionDetail(
                id: "hangzhou_west_lake",
                name: "西湖",
                cityId: "hangzhou",
                coordinate: CLLocationCoordinate2D(latitude: 30.2551, longitude: 120.1445),
                category: .natural,
                description: "人间天堂，世界文化遗产，西湖十景",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 240,
                popularity: 10,
                tags: ["世界遗产", "西湖十景", "断桥残雪", "雷峰塔"],
                imageURL: nil,
                rating: 4.9
            ),
            
            AttractionDetail(
                id: "hangzhou_lingyin_temple",
                name: "灵隐寺",
                cityId: "hangzhou",
                coordinate: CLLocationCoordinate2D(latitude: 30.2418, longitude: 120.1014),
                category: .religious,
                description: "江南佛教名刹，中国佛教十大古寺之一",
                openTime: "07:30-17:30",
                ticketPrice: "45元",
                visitDuration: 120,
                popularity: 8,
                tags: ["佛教名刹", "古寺", "石刻造像", "禅文化"],
                imageURL: nil,
                rating: 4.6
            ),
            
            AttractionDetail(
                id: "hangzhou_qinghefang",
                name: "清河坊街",
                cityId: "hangzhou",
                coordinate: CLLocationCoordinate2D(latitude: 30.2459, longitude: 120.1673),
                category: .historical,
                description: "杭州历史文化街区，传统商业街",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 120,
                popularity: 7,
                tags: ["历史街区", "传统商业", "老字号", "杭州小吃"],
                imageURL: nil,
                rating: 4.2
            ),
            
            // 西安景点 - 真实GPS坐标
            AttractionDetail(
                id: "xian_railway_station",
                name: "西安站",
                cityId: "xian",
                coordinate: CLLocationCoordinate2D(latitude: 34.2676, longitude: 108.9542),
                category: .modern,
                description: "西安市主要火车站，交通枢纽",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 30,
                popularity: 6,
                tags: ["交通枢纽", "出发点", "现代建筑"],
                imageURL: nil,
                rating: 4.0
            ),
            
            AttractionDetail(
                id: "xian_terracotta_warriors",
                name: "兵马俑",
                cityId: "xian",
                coordinate: CLLocationCoordinate2D(latitude: 34.3845, longitude: 109.2734),
                category: .historical,
                description: "世界第八大奇迹，秦始皇陵兵马俑",
                openTime: "08:30-17:00",
                ticketPrice: "120元",
                visitDuration: 180,
                popularity: 10,
                tags: ["世界奇迹", "秦始皇陵", "兵马俑", "考古发现"],
                imageURL: nil,
                rating: 4.8
            ),
            
            AttractionDetail(
                id: "xian_city_wall",
                name: "西安城墙",
                cityId: "xian",
                coordinate: CLLocationCoordinate2D(latitude: 34.2583, longitude: 108.9465),
                category: .historical,
                description: "中国现存最完整的古代城垣建筑",
                openTime: "08:00-22:00",
                ticketPrice: "54元",
                visitDuration: 120,
                popularity: 9,
                tags: ["古城墙", "城垣建筑", "骑行", "夜景"],
                imageURL: nil,
                rating: 4.6
            ),
            
            AttractionDetail(
                id: "xian_big_wild_goose_pagoda",
                name: "大雁塔",
                cityId: "xian",
                coordinate: CLLocationCoordinate2D(latitude: 34.2186, longitude: 108.9647),
                category: .religious,
                description: "唐代佛教建筑艺术杰作，玄奘译经之地",
                openTime: "08:00-17:30",
                ticketPrice: "40元",
                visitDuration: 90,
                popularity: 8,
                tags: ["佛教建筑", "唐代古塔", "玄奘", "音乐喷泉"],
                imageURL: nil,
                rating: 4.5
            ),
            
            AttractionDetail(
                id: "xian_muslim_quarter",
                name: "回民街",
                cityId: "xian",
                coordinate: CLLocationCoordinate2D(latitude: 34.2663, longitude: 108.9414),
                category: .food,
                description: "西安著名美食街，回族风味小吃聚集地",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 120,
                popularity: 8,
                tags: ["美食街", "回族风味", "小吃", "夜市"],
                imageURL: nil,
                rating: 4.3
            ),
            
            // 成都景点 - 真实GPS坐标
            AttractionDetail(
                id: "chengdu_railway_station",
                name: "成都站",
                cityId: "chengdu",
                coordinate: CLLocationCoordinate2D(latitude: 30.6958, longitude: 104.0817),
                category: .modern,
                description: "成都市主要火车站，交通枢纽",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 30,
                popularity: 6,
                tags: ["交通枢纽", "出发点", "现代建筑"],
                imageURL: nil,
                rating: 4.0
            ),
            
            AttractionDetail(
                id: "chengdu_panda_base",
                name: "大熊猫繁育研究基地",
                cityId: "chengdu",
                coordinate: CLLocationCoordinate2D(latitude: 30.7345, longitude: 104.1502),
                category: .family,
                description: "世界著名的大熊猫科研繁育、保护教育和教育旅游基地",
                openTime: "07:30-18:00",
                ticketPrice: "58元",
                visitDuration: 180,
                popularity: 10,
                tags: ["大熊猫", "科研基地", "保护教育", "萌宠"],
                imageURL: nil,
                rating: 4.8
            ),
            
            AttractionDetail(
                id: "chengdu_wuhou_temple",
                name: "武侯祠",
                cityId: "chengdu",
                coordinate: CLLocationCoordinate2D(latitude: 30.6463, longitude: 104.0438),
                category: .historical,
                description: "三国文化圣地，诸葛亮祠堂",
                openTime: "08:00-18:00",
                ticketPrice: "50元",
                visitDuration: 120,
                popularity: 8,
                tags: ["三国文化", "诸葛亮", "历史古迹", "锦里"],
                imageURL: nil,
                rating: 4.5
            ),
            
            AttractionDetail(
                id: "chengdu_jinli",
                name: "锦里古街",
                cityId: "chengdu",
                coordinate: CLLocationCoordinate2D(latitude: 30.6456, longitude: 104.0431),
                category: .historical,
                description: "成都版清明上河图，川西民俗文化街",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 120,
                popularity: 8,
                tags: ["民俗文化", "川西风情", "小吃", "手工艺品"],
                imageURL: nil,
                rating: 4.4
            ),
            
            AttractionDetail(
                id: "chengdu_chunxi_road",
                name: "春熙路",
                cityId: "chengdu",
                coordinate: CLLocationCoordinate2D(latitude: 30.6598, longitude: 104.0805),
                category: .shopping,
                description: "成都最繁华的商业街，时尚购物中心",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 150,
                popularity: 7,
                tags: ["购物中心", "时尚街区", "美食", "夜生活"],
                imageURL: nil,
                rating: 4.2
            ),
            
            // 北京景点 - 真实GPS坐标
            AttractionDetail(
                id: "beijing_railway_station",
                name: "北京站",
                cityId: "beijing",
                coordinate: CLLocationCoordinate2D(latitude: 39.9031, longitude: 116.4274),
                category: .modern,
                description: "北京市主要火车站之一，交通枢纽",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 30,
                popularity: 6,
                tags: ["交通枢纽", "出发点", "现代建筑"],
                imageURL: nil,
                rating: 4.0
            ),
            
            AttractionDetail(
                id: "beijing_forbidden_city",
                name: "故宫博物院",
                cityId: "beijing",
                coordinate: CLLocationCoordinate2D(latitude: 39.9163, longitude: 116.3972),
                category: .historical,
                description: "明清两代皇宫，世界现存最大的古代宫殿建筑群",
                openTime: "08:30-17:00",
                ticketPrice: "60元",
                visitDuration: 240,
                popularity: 10,
                tags: ["皇家宫殿", "明清文化", "古代建筑", "世界遗产"],
                imageURL: nil,
                rating: 4.8
            ),
            
            AttractionDetail(
                id: "beijing_tiananmen_square",
                name: "天安门广场",
                cityId: "beijing",
                coordinate: CLLocationCoordinate2D(latitude: 39.9055, longitude: 116.3976),
                category: .historical,
                description: "世界最大的城市广场，见证中华民族历史",
                openTime: "05:00-22:00",
                ticketPrice: "免费",
                visitDuration: 60,
                popularity: 10,
                tags: ["国家象征", "历史广场", "爱国主义", "必游景点"],
                imageURL: nil,
                rating: 4.7
            ),
            
            AttractionDetail(
                id: "beijing_great_wall_badaling",
                name: "八达岭长城",
                cityId: "beijing",
                coordinate: CLLocationCoordinate2D(latitude: 40.3592, longitude: 116.0138),
                category: .historical,
                description: "中华民族的象征，世界七大奇迹之一",
                openTime: "06:30-19:00",
                ticketPrice: "40元",
                visitDuration: 300,
                popularity: 10,
                tags: ["万里长城", "民族象征", "世界奇迹", "登高望远"],
                imageURL: nil,
                rating: 4.9
            ),
            
            AttractionDetail(
                id: "beijing_temple_of_heaven",
                name: "天坛公园",
                cityId: "beijing",
                coordinate: CLLocationCoordinate2D(latitude: 39.8822, longitude: 116.4066),
                category: .historical,
                description: "明清皇帝祭天的场所，中国古代建筑的杰作",
                openTime: "06:00-21:00",
                ticketPrice: "15元",
                visitDuration: 120,
                popularity: 8,
                tags: ["祭天建筑", "园林艺术", "古代文化", "晨练圣地"],
                imageURL: nil,
                rating: 4.6
            ),
            
            AttractionDetail(
                id: "beijing_summer_palace",
                name: "颐和园",
                cityId: "beijing",
                coordinate: CLLocationCoordinate2D(latitude: 39.9999, longitude: 116.2752),
                category: .historical,
                description: "中国古典园林的典范，慈禧太后的行宫",
                openTime: "06:30-18:00",
                ticketPrice: "30元",
                visitDuration: 180,
                popularity: 9,
                tags: ["皇家园林", "古典建筑", "湖光山色", "世界遗产"],
                imageURL: nil,
                rating: 4.7
            ),
            
            AttractionDetail(
                id: "beijing_beihai_park",
                name: "北海公园",
                cityId: "beijing",
                coordinate: CLLocationCoordinate2D(latitude: 39.9266, longitude: 116.3883),
                category: .park,
                description: "北京最古老的皇家园林，白塔是标志性建筑",
                openTime: "06:00-21:00",
                ticketPrice: "10元",
                visitDuration: 120,
                popularity: 7,
                tags: ["皇家园林", "白塔", "湖心岛", "休闲漫步"],
                imageURL: nil,
                rating: 4.4
            ),
            
            AttractionDetail(
                id: "beijing_hutong",
                name: "南锣鼓巷",
                cityId: "beijing",
                coordinate: CLLocationCoordinate2D(latitude: 39.9368, longitude: 116.4035),
                category: .historical,
                description: "北京最古老的街区之一，胡同文化体验",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 90,
                popularity: 8,
                tags: ["胡同文化", "老北京", "文艺街区", "特色小店"],
                imageURL: nil,
                rating: 4.3
            ),
            
            // 上海景点
            AttractionDetail(
                id: "shanghai_bund",
                name: "外滩",
                cityId: "shanghai",
                coordinate: CLLocationCoordinate2D(latitude: 31.2396, longitude: 121.4900),
                category: .historical,
                description: "上海的标志性景观，万国建筑博览群",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 90,
                popularity: 10,
                tags: ["万国建筑", "黄浦江", "上海地标", "夜景观赏"],
                imageURL: nil,
                rating: 4.7
            ),
            
            AttractionDetail(
                id: "shanghai_oriental_pearl",
                name: "东方明珠塔",
                cityId: "shanghai",
                coordinate: CLLocationCoordinate2D(latitude: 31.2397, longitude: 121.4997),
                category: .modern,
                description: "上海地标性建筑，观光电视塔",
                openTime: "08:00-21:30",
                ticketPrice: "160元",
                visitDuration: 120,
                popularity: 9,
                tags: ["地标建筑", "观光塔", "全景视野", "现代建筑"],
                imageURL: nil,
                rating: 4.4
            ),
            
            // 西安景点
            AttractionDetail(
                id: "xian_terracotta_warriors",
                name: "兵马俑",
                cityId: "xian",
                coordinate: CLLocationCoordinate2D(latitude: 34.3848, longitude: 109.2734),
                category: .historical,
                description: "秦始皇陵兵马俑，世界第八大奇迹",
                openTime: "08:30-17:30",
                ticketPrice: "120元",
                visitDuration: 180,
                popularity: 10,
                tags: ["兵马俑", "秦朝文化", "世界奇迹", "考古发现"],
                imageURL: nil,
                rating: 4.8
            ),
            
            AttractionDetail(
                id: "xian_city_wall",
                name: "西安城墙",
                cityId: "xian",
                coordinate: CLLocationCoordinate2D(latitude: 34.2661, longitude: 108.9537),
                category: .historical,
                description: "中国现存最完整的古代城垣建筑",
                openTime: "08:00-22:00",
                ticketPrice: "54元",
                visitDuration: 120,
                popularity: 8,
                tags: ["古代城墙", "骑行体验", "古都风貌", "防御建筑"],
                imageURL: nil,
                rating: 4.5
            ),
            
            AttractionDetail(
                id: "xian_big_wild_goose_pagoda",
                name: "大雁塔",
                cityId: "xian",
                coordinate: CLLocationCoordinate2D(latitude: 34.2182, longitude: 108.9647),
                category: .religious,
                description: "唐代佛教建筑，玄奘法师译经之地",
                openTime: "08:00-17:30",
                ticketPrice: "40元",
                visitDuration: 90,
                popularity: 9,
                tags: ["唐代建筑", "佛教文化", "古塔", "文化遗产"],
                imageURL: nil,
                rating: 4.6
            ),
            
            // 杭州景点
            AttractionDetail(
                id: "hangzhou_west_lake",
                name: "西湖",
                cityId: "hangzhou",
                coordinate: CLLocationCoordinate2D(latitude: 30.2741, longitude: 120.1551),
                category: .natural,
                description: "人间天堂，世界文化遗产",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 180,
                popularity: 10,
                tags: ["西湖美景", "世界遗产", "江南水乡", "诗画江南"],
                imageURL: nil,
                rating: 4.9
            ),
            
            AttractionDetail(
                id: "hangzhou_lingyin_temple",
                name: "灵隐寺",
                cityId: "hangzhou",
                coordinate: CLLocationCoordinate2D(latitude: 30.2414, longitude: 120.1005),
                category: .religious,
                description: "江南禅宗著名古刹，中国十大古寺之一",
                openTime: "06:00-18:00",
                ticketPrice: "45元",
                visitDuration: 120,
                popularity: 8,
                tags: ["古刹", "禅宗文化", "佛教圣地", "江南名寺"],
                imageURL: nil,
                rating: 4.5
            ),
            
            // 成都景点
            AttractionDetail(
                id: "chengdu_giant_panda_base",
                name: "成都大熊猫繁育研究基地",
                cityId: "chengdu",
                coordinate: CLLocationCoordinate2D(latitude: 30.7326, longitude: 104.1477),
                category: .family,
                description: "世界著名的大熊猫繁育科研机构",
                openTime: "07:30-18:00",
                ticketPrice: "58元",
                visitDuration: 180,
                popularity: 10,
                tags: ["大熊猫", "野生动物", "科普教育", "亲子游"],
                imageURL: nil,
                rating: 4.8
            ),
            
            AttractionDetail(
                id: "chengdu_wuhou_shrine",
                name: "武侯祠",
                cityId: "chengdu",
                coordinate: CLLocationCoordinate2D(latitude: 30.6473, longitude: 104.0439),
                category: .historical,
                description: "中国唯一君臣合祀祠庙，三国文化圣地",
                openTime: "08:00-18:00",
                ticketPrice: "60元",
                visitDuration: 120,
                popularity: 8,
                tags: ["三国文化", "历史名胜", "古代建筑", "文化遗产"],
                imageURL: nil,
                rating: 4.4
            ),
            
            AttractionDetail(
                id: "chengdu_jinli_street",
                name: "锦里古街",
                cityId: "chengdu",
                coordinate: CLLocationCoordinate2D(latitude: 30.6465, longitude: 104.0423),
                category: .historical,
                description: "成都版清明上河图，川西民俗文化街",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 150,
                popularity: 9,
                tags: ["民俗文化", "古街", "川菜美食", "传统工艺"],
                imageURL: nil,
                rating: 4.3
            ),
            
            // 厦门景点
            AttractionDetail(
                id: "xiamen_gulangyu",
                name: "鼓浪屿",
                cityId: "xiamen",
                coordinate: CLLocationCoordinate2D(latitude: 24.4448, longitude: 118.0635),
                category: .natural,
                description: "海上花园，音乐之岛，世界文化遗产",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 240,
                popularity: 10,
                tags: ["海岛风情", "音乐文化", "万国建筑", "世界遗产"],
                imageURL: nil,
                rating: 4.7
            ),
            
            AttractionDetail(
                id: "xiamen_nanputuo_temple",
                name: "南普陀寺",
                cityId: "xiamen",
                coordinate: CLLocationCoordinate2D(latitude: 24.4410, longitude: 118.1016),
                category: .religious,
                description: "闽南佛教胜地，千年古刹",
                openTime: "03:00-20:00",
                ticketPrice: "免费",
                visitDuration: 90,
                popularity: 7,
                tags: ["佛教寺庙", "闽南文化", "古建筑", "朝圣圣地"],
                imageURL: nil,
                rating: 4.2
            ),
            
            // 曲靖景点 - 真实GPS坐标
            AttractionDetail(
                id: "qujing_railway_station",
                name: "曲靖火车站",
                cityId: "qujing",
                coordinate: CLLocationCoordinate2D(latitude: 25.5089, longitude: 103.7972),
                category: .modern,
                description: "曲靖市主要交通枢纽，沪昆高铁重要站点",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 30,
                popularity: 6,
                tags: ["交通枢纽", "出发点", "现代建筑"],
                imageURL: nil,
                rating: 4.0
            ),
            
            AttractionDetail(
                id: "qujing_zhujiang_source",
                name: "珠江源风景区",
                cityId: "qujing",
                coordinate: CLLocationCoordinate2D(latitude: 25.6419, longitude: 104.1647),
                category: .natural,
                description: "珠江发源地，中国南方第一大河的源头",
                openTime: "08:00-18:00",
                ticketPrice: "30元",
                visitDuration: 120,
                popularity: 9,
                tags: ["珠江源头", "自然风光", "生态旅游", "地理标志"],
                imageURL: nil,
                rating: 4.5
            ),
            
            AttractionDetail(
                id: "qujing_colorful_sand_forest",
                name: "陆良彩色沙林",
                cityId: "qujing",
                coordinate: CLLocationCoordinate2D(latitude: 25.0297, longitude: 103.6558),
                category: .natural,
                description: "世界罕见的彩色沙林地貌，色彩斑斓的天然奇观",
                openTime: "08:00-18:00",
                ticketPrice: "25元",
                visitDuration: 150,
                popularity: 8,
                tags: ["彩色沙林", "地质奇观", "摄影胜地", "自然造化"],
                imageURL: nil,
                rating: 4.4
            ),
            
            AttractionDetail(
                id: "qujing_jiulong_waterfall",
                name: "九龙瀑布群",
                cityId: "qujing",
                coordinate: CLLocationCoordinate2D(latitude: 24.8775, longitude: 104.6975),
                category: .natural,
                description: "中国最大的瀑布群景观，层层叠叠十分壮观",
                openTime: "08:00-18:00",
                ticketPrice: "95元",
                visitDuration: 200,
                popularity: 9,
                tags: ["瀑布群", "自然奇观", "摄影胜地", "水景观光"],
                imageURL: nil,
                rating: 4.7
            ),
            
            AttractionDetail(
                id: "qujing_luoping_canola_flowers",
                name: "罗平金鸡峰丛",
                cityId: "qujing",
                coordinate: CLLocationCoordinate2D(latitude: 24.8908, longitude: 104.3089),
                category: .natural,
                description: "中国最美的油菜花海，春季金黄遍野的壮丽景观",
                openTime: "全天开放",
                ticketPrice: "10元",
                visitDuration: 180,
                popularity: 10,
                tags: ["油菜花海", "春季赏花", "田园风光", "摄影天堂"],
                imageURL: nil,
                rating: 4.8
            ),
            
            AttractionDetail(
                id: "qujing_wumeng_mountain",
                name: "会泽大山包",
                cityId: "qujing",
                coordinate: CLLocationCoordinate2D(latitude: 26.5433, longitude: 103.3011),
                category: .natural,
                description: "黑颈鹤的故乡，高原湿地生态保护区",
                openTime: "08:00-18:00",
                ticketPrice: "30元",
                visitDuration: 240,
                popularity: 7,
                tags: ["黑颈鹤", "高原湿地", "生态旅游", "自然保护区"],
                imageURL: nil,
                rating: 4.3
            ),
            
            AttractionDetail(
                id: "qujing_shilin_cave",
                name: "师宗菌子山",
                cityId: "qujing",
                coordinate: CLLocationCoordinate2D(latitude: 24.8133, longitude: 103.9858),
                category: .natural,
                description: "滇东南第一高峰，山林植被丰富，空气清新",
                openTime: "08:00-17:00",
                ticketPrice: "40元",
                visitDuration: 300,
                popularity: 6,
                tags: ["高峰登山", "森林植被", "空气清新", "户外徒步"],
                imageURL: nil,
                rating: 4.2
            ),
            
            AttractionDetail(
                id: "qujing_old_town",
                name: "曲靖古城",
                cityId: "qujing",
                coordinate: CLLocationCoordinate2D(latitude: 25.4976, longitude: 103.7967),
                category: .historical,
                description: "古老的城池遗址，见证曲靖悠久的历史文化",
                openTime: "全天开放",
                ticketPrice: "免费",
                visitDuration: 120,
                popularity: 7,
                tags: ["古城墙", "历史文化", "城池遗址", "文化探索"],
                imageURL: nil,
                rating: 4.1
            )
        ]
    }
    
    private func loadRestaurants() {
        restaurants = [
            // 天津餐厅
            Restaurant(
                id: "goubuli_baozi",
                name: "狗不理包子",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1200, longitude: 117.1950),
                cuisine: .local,
                priceLevel: .moderate,
                description: "天津三绝之一，百年老字号包子店",
                specialties: ["狗不理包子", "素包", "肉包"],
                rating: 4.2,
                tags: ["天津三绝", "百年老店", "传统小吃"]
            ),
            
            Restaurant(
                id: "erduo_yan_zhagao",
                name: "耳朵眼炸糕",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1180, longitude: 117.1920),
                cuisine: .local,
                priceLevel: .budget,
                description: "天津三绝之一，传统炸糕名店",
                specialties: ["耳朵眼炸糕", "豆沙炸糕"],
                rating: 4.1,
                tags: ["天津三绝", "传统糕点", "甜品小吃"]
            ),
            
            Restaurant(
                id: "jianbing_guozi",
                name: "煎饼果子店",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1250, longitude: 117.1880),
                cuisine: .street,
                priceLevel: .budget,
                description: "天津特色街头小吃，早餐首选",
                specialties: ["煎饼果子", "果子", "薄脆"],
                rating: 4.3,
                tags: ["街头小吃", "早餐", "天津特色"]
            ),
            
            // 北京餐厅
            Restaurant(
                id: "quanjude_roast_duck",
                name: "全聚德烤鸭店",
                cityId: "beijing",
                coordinate: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
                cuisine: .local,
                priceLevel: .expensive,
                description: "北京烤鸭老字号，享誉中外",
                specialties: ["北京烤鸭", "烤鸭卷饼", "鸭汤"],
                rating: 4.4,
                tags: ["北京烤鸭", "老字号", "招牌美食"]
            ),
            
            Restaurant(
                id: "donglaishun_hotpot",
                name: "东来顺涮羊肉",
                cityId: "beijing",
                coordinate: CLLocationCoordinate2D(latitude: 39.9100, longitude: 116.4100),
                cuisine: .hotpot,
                priceLevel: .moderate,
                description: "百年涮羊肉老店，铜锅涮肉正宗",
                specialties: ["铜锅涮羊肉", "手切羊肉", "芝麻酱"],
                rating: 4.3,
                tags: ["涮羊肉", "铜锅", "老北京"]
            ),
            
            // 上海餐厅
            Restaurant(
                id: "shanghai_xiaolongbao",
                name: "南翔馒头店",
                cityId: "shanghai",
                coordinate: CLLocationCoordinate2D(latitude: 31.2380, longitude: 121.4890),
                cuisine: .local,
                priceLevel: .moderate,
                description: "百年小笼包老店，上海传统美食",
                specialties: ["小笼包", "蟹黄包", "鲜肉包"],
                rating: 4.4,
                tags: ["小笼包", "上海特色", "传统点心"]
            ),
            
            // 杭州餐厅
            Restaurant(
                id: "hangzhou_west_lake_vinegar_fish",
                name: "楼外楼",
                cityId: "hangzhou",
                coordinate: CLLocationCoordinate2D(latitude: 30.2500, longitude: 120.1400),
                cuisine: .local,
                priceLevel: .expensive,
                description: "西湖边百年老店，杭帮菜代表",
                specialties: ["西湖醋鱼", "东坡肉", "龙井虾仁"],
                rating: 4.5,
                tags: ["杭帮菜", "西湖美食", "百年老店"]
            ),
            
            // 成都餐厅
            Restaurant(
                id: "chengdu_hotpot",
                name: "海底捞火锅",
                cityId: "chengdu",
                coordinate: CLLocationCoordinate2D(latitude: 30.5700, longitude: 104.0700),
                cuisine: .hotpot,
                priceLevel: .moderate,
                description: "知名火锅连锁，服务一流",
                specialties: ["麻辣火锅", "清汤锅底", "优质服务"],
                rating: 4.6,
                tags: ["四川火锅", "优质服务", "连锁品牌"]
            ),
            
            Restaurant(
                id: "chengdu_mapo_tofu",
                name: "陈建民麻婆豆腐店",
                cityId: "chengdu",
                coordinate: CLLocationCoordinate2D(latitude: 30.6650, longitude: 104.0600),
                cuisine: .local,
                priceLevel: .budget,
                description: "正宗川菜老店，麻婆豆腐发源地",
                specialties: ["麻婆豆腐", "回锅肉", "宫保鸡丁"],
                rating: 4.3,
                tags: ["川菜", "传统老店", "地道口味"]
            ),
            
            // 厦门餐厅
            Restaurant(
                id: "xiamen_seafood",
                name: "小眼镜大排档",
                cityId: "xiamen",
                coordinate: CLLocationCoordinate2D(latitude: 24.4500, longitude: 118.0800),
                cuisine: .seafood,
                priceLevel: .moderate,
                description: "厦门知名海鲜大排档，新鲜美味",
                specialties: ["白灼虾", "清蒸螃蟹", "海蛎煎"],
                rating: 4.4,
                tags: ["海鲜", "大排档", "厦门特色"]
            ),
            
            // 西安餐厅
            Restaurant(
                id: "xian_biangbiang_noodles",
                name: "魏家凉皮",
                cityId: "xian",
                coordinate: CLLocationCoordinate2D(latitude: 34.2600, longitude: 108.9400),
                cuisine: .local,
                priceLevel: .budget,
                description: "西安特色面食小吃，地道口味",
                specialties: ["biángbiáng面", "凉皮", "肉夹馍"],
                rating: 4.2,
                tags: ["陕西面食", "传统小吃", "平价美食"]
            ),
            
            // 曲靖餐厅
            Restaurant(
                id: "qujing_crossing_bridge_noodles",
                name: "云南过桥米线",
                cityId: "qujing",
                coordinate: CLLocationCoordinate2D(latitude: 25.5000, longitude: 103.7900),
                cuisine: .local,
                priceLevel: .moderate,
                description: "正宗云南过桥米线，鲜美汤底配新鲜食材",
                specialties: ["过桥米线", "鸡汤米线", "菌菇米线"],
                rating: 4.3,
                tags: ["云南特色", "过桥米线", "地方美食"]
            ),
            
            Restaurant(
                id: "qujing_steamed_chicken",
                name: "曲靖汽锅鸡",
                cityId: "qujing",
                coordinate: CLLocationCoordinate2D(latitude: 25.4980, longitude: 103.7850),
                cuisine: .local,
                priceLevel: .moderate,
                description: "云南传统名菜，汽锅蒸制的鸡肉鲜嫩清香",
                specialties: ["汽锅鸡", "虫草汽锅鸡", "天麻汽锅鸡"],
                rating: 4.4,
                tags: ["云南名菜", "传统做法", "营养滋补"]
            ),
            
            Restaurant(
                id: "qujing_grilled_tofu",
                name: "曲靖烧豆腐",
                cityId: "qujing",
                coordinate: CLLocationCoordinate2D(latitude: 25.5020, longitude: 103.7880),
                cuisine: .street,
                priceLevel: .budget,
                description: "曲靖特色街头小吃，外焦内嫩的烧豆腐",
                specialties: ["烧豆腐", "蘸水豆腐", "韭菜花蘸料"],
                rating: 4.1,
                tags: ["街头小吃", "曲靖特色", "平价美食"]
            )
        ]
    }
    
    private func loadHotels() {
        hotels = [
            // 天津酒店
            Hotel(
                id: "tianjin_sheraton",
                name: "天津喜来登大酒店",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1200, longitude: 117.1700),
                hotelType: .luxury,
                priceLevel: .expensive,
                amenities: ["健身房", "游泳池", "商务中心", "餐厅"],
                rating: 4.5,
                description: "五星级豪华酒店，位置便利，服务优质"
            ),
            
            Hotel(
                id: "tianjin_jinjiang",
                name: "锦江之星",
                cityId: "tianjin",
                coordinate: CLLocationCoordinate2D(latitude: 39.1150, longitude: 117.1800),
                hotelType: .business,
                priceLevel: .moderate,
                amenities: ["免费WiFi", "24小时前台", "商务中心"],
                rating: 4.0,
                description: "经济型连锁酒店，性价比高，位置便利"
            )
        ]
    }
    
    // MARK: - Public Methods
    func getAllCities() -> [TravelCity] {
        return cities
    }
    
    func getCity(by id: String) -> TravelCity? {
        return cities.first { $0.id == id }
    }
    
    func getCityByName(_ name: String) -> TravelCity? {
        return cities.first { $0.name.contains(name) || name.contains($0.name.replacingOccurrences(of: "市", with: "")) }
    }
    
    func getAttractions(for cityId: String) -> [AttractionDetail] {
        return attractions.filter { $0.cityId == cityId }
    }
    
    func getRestaurants(for cityId: String) -> [Restaurant] {
        return restaurants.filter { $0.cityId == cityId }
    }
    
    func getHotels(for cityId: String) -> [Hotel] {
        return hotels.filter { $0.cityId == cityId }
    }
    
    func searchAttractions(by category: AttractionCategory, in cityId: String) -> [AttractionDetail] {
        return attractions.filter { $0.cityId == cityId && $0.category == category }
    }
    
    func getAttractionsByTags(_ tags: [String], in cityId: String) -> [AttractionDetail] {
        return attractions.filter { attraction in
            attraction.cityId == cityId &&
            !Set(attraction.tags).isDisjoint(with: Set(tags))
        }
    }
    
    func getTopAttractions(for cityId: String, limit: Int = 10) -> [AttractionDetail] {
        return attractions
            .filter { $0.cityId == cityId }
            .sorted { $0.popularity > $1.popularity }
            .prefix(limit)
            .map { $0 }
    }
} 