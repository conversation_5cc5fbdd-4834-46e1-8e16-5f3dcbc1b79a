import UIKit

protocol TravelPreferencesDelegate: AnyObject {
    func didCompletePreferences(_ preferences: TravelPreferences)
}

class TravelPreferencesViewController: UIViewController {
    
    weak var delegate: TravelPreferencesDelegate?
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    // Preference sections
    private var interestButtons: [UIButton] = []
    private var foodButtons: [UIButton] = []
    private var accommodationButtons: [UIButton] = []
    
    // Selected preferences
    private var selectedInterests: Set<String> = []
    private var selectedFoods: Set<String> = []
    private var selectedAccommodations: Set<String> = []
    
    // Data
    private let interests = [
        "经典打卡", "自然风光", "人文历史",
        "城市搜索", "户外冒险", "休闲娱乐",
        "艺术创意", "特色体验", "度假"
    ]
    
    private let foods = [
        "必吃餐厅", "本地特色", "国际风味",
        "市井小吃", "网红餐厅", "高端餐厅",
        "美食市集", "西式料理", "米其林"
    ]
    
    private let accommodations = [
        "经济型", "舒适型", "高端型",
        "连锁酒店", "精品酒店", "特色住宿",
        "度假村", "别墅", "民宿", "青旅"
    ]
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        // Navigation
        navigationItem.title = "旅行个性化偏好（可选）"
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "xmark"),
            style: .plain,
            target: self,
            action: #selector(closeTapped)
        )
        
        // Setup scroll view
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        // Create sections
        createSection(title: "兴趣爱好", items: interests, buttons: &interestButtons, selector: #selector(interestTapped(_:)))
        createSection(title: "饮食偏好", items: foods, buttons: &foodButtons, selector: #selector(foodTapped(_:)))
        createSection(title: "住宿需求", items: accommodations, buttons: &accommodationButtons, selector: #selector(accommodationTapped(_:)))
        
        // Bottom buttons
        setupBottomButtons()
    }
    
    private func createSection(title: String, items: [String], buttons: inout [UIButton], selector: Selector) {
        let sectionLabel = UILabel()
        sectionLabel.text = title
        sectionLabel.font = UIFont.boldSystemFont(ofSize: 18)
        sectionLabel.textColor = .label
        sectionLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(sectionLabel)
        
        let containerView = UIView()
        containerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(containerView)
        
        let buttonsPerRow = 3
        
        for (index, item) in items.enumerated() {
            let button = UIButton(type: .system)
            button.setTitle(item, for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            button.setTitleColor(.label, for: .normal)
            button.backgroundColor = .systemGray6
            button.layer.cornerRadius = 20
            button.layer.borderWidth = 1
            button.layer.borderColor = UIColor.systemGray4.cgColor
            button.translatesAutoresizingMaskIntoConstraints = false
            button.addTarget(self, action: selector, for: .touchUpInside)
            button.tag = index
            
            buttons.append(button)
            containerView.addSubview(button)
            
            let columnIndex = index % buttonsPerRow
            let rowIndex = index / buttonsPerRow
            
            // Layout constraints
            if columnIndex == 0 {
                // First button in row
                button.leadingAnchor.constraint(equalTo: containerView.leadingAnchor).isActive = true
            } else {
                // Other buttons in row
                button.leadingAnchor.constraint(equalTo: buttons[index - 1].trailingAnchor, constant: 12).isActive = true
            }
            
            if columnIndex == buttonsPerRow - 1 || index == items.count - 1 {
                // Last button in row or last button overall
                button.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor).isActive = true
            }
            
            if rowIndex == 0 {
                // First row
                button.topAnchor.constraint(equalTo: containerView.topAnchor).isActive = true
            } else if columnIndex == 0 {
                // First button of new row
                let previousRowIndex = index - buttonsPerRow
                if previousRowIndex >= 0 && previousRowIndex < buttons.count {
                    button.topAnchor.constraint(equalTo: buttons[previousRowIndex].bottomAnchor, constant: 12).isActive = true
                }
            } else {
                // Other buttons in same row
                let firstButtonInRowIndex = index - columnIndex
                if firstButtonInRowIndex >= 0 && firstButtonInRowIndex < buttons.count {
                    button.topAnchor.constraint(equalTo: buttons[firstButtonInRowIndex].topAnchor).isActive = true
                }
            }
            
            button.heightAnchor.constraint(equalToConstant: 40).isActive = true
            button.widthAnchor.constraint(greaterThanOrEqualToConstant: 80).isActive = true
            
            // Last button sets bottom constraint
            if index == items.count - 1 {
                button.bottomAnchor.constraint(equalTo: containerView.bottomAnchor).isActive = true
            }
        }
        
        // Set section constraints - 简化逻辑避免约束冲突
        let allContainers = contentView.subviews.filter { !($0 is UILabel) }
        if allContainers.count <= 1 { // First section
            sectionLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 30).isActive = true
        } else { // Subsequent sections
            if let lastContainer = allContainers.dropLast().last {
                sectionLabel.topAnchor.constraint(equalTo: lastContainer.bottomAnchor, constant: 40).isActive = true
            } else {
                sectionLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 30).isActive = true
            }
        }
        
        sectionLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 30).isActive = true
        
        containerView.topAnchor.constraint(equalTo: sectionLabel.bottomAnchor, constant: 20).isActive = true
        containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 30).isActive = true
        containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -30).isActive = true
    }
    
    private func setupBottomButtons() {
        let buttonContainer = UIView()
        buttonContainer.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(buttonContainer)
        
        let skipButton = UIButton(type: .system)
        skipButton.setTitle("跳过", for: .normal)
        skipButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        skipButton.setTitleColor(.label, for: .normal)
        skipButton.backgroundColor = .systemGray6
        skipButton.layer.cornerRadius = 25
        skipButton.translatesAutoresizingMaskIntoConstraints = false
        skipButton.addTarget(self, action: #selector(skipTapped), for: .touchUpInside)
        
        let confirmButton = UIButton(type: .system)
        confirmButton.setTitle("确定", for: .normal)
        confirmButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        confirmButton.setTitleColor(.white, for: .normal)
        confirmButton.backgroundColor = .systemBlue
        confirmButton.layer.cornerRadius = 25
        confirmButton.translatesAutoresizingMaskIntoConstraints = false
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)
        
        buttonContainer.addSubview(skipButton)
        buttonContainer.addSubview(confirmButton)
        
        NSLayoutConstraint.activate([
            buttonContainer.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 30),
            buttonContainer.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -30),
            buttonContainer.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -20),
            buttonContainer.heightAnchor.constraint(equalToConstant: 50),
            
            skipButton.leadingAnchor.constraint(equalTo: buttonContainer.leadingAnchor),
            skipButton.centerYAnchor.constraint(equalTo: buttonContainer.centerYAnchor),
            skipButton.widthAnchor.constraint(equalToConstant: 120),
            skipButton.heightAnchor.constraint(equalToConstant: 50),
            
            confirmButton.trailingAnchor.constraint(equalTo: buttonContainer.trailingAnchor),
            confirmButton.leadingAnchor.constraint(equalTo: skipButton.trailingAnchor, constant: 20),
            confirmButton.centerYAnchor.constraint(equalTo: buttonContainer.centerYAnchor),
            confirmButton.heightAnchor.constraint(equalToConstant: 50)
        ])
        
        // Adjust scroll view to account for bottom buttons
        scrollView.contentInset.bottom = 100
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor)
        ])
        
        // Set bottom constraint for last section
        if let lastContainer = contentView.subviews.last {
            let bottomConstraint = lastContainer.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -40)
            bottomConstraint.priority = UILayoutPriority(999) // 避免约束冲突
            bottomConstraint.isActive = true
        }
    }
    
    // MARK: - Actions
    @objc private func interestTapped(_ sender: UIButton) {
        let interest = interests[sender.tag]
        toggleSelection(item: interest, in: &selectedInterests, button: sender)
    }
    
    @objc private func foodTapped(_ sender: UIButton) {
        let food = foods[sender.tag]
        toggleSelection(item: food, in: &selectedFoods, button: sender)
    }
    
    @objc private func accommodationTapped(_ sender: UIButton) {
        let accommodation = accommodations[sender.tag]
        toggleSelection(item: accommodation, in: &selectedAccommodations, button: sender)
    }
    
    private func toggleSelection(item: String, in set: inout Set<String>, button: UIButton) {
        if set.contains(item) {
            set.remove(item)
            button.backgroundColor = .systemGray6
            button.setTitleColor(.label, for: .normal)
            button.layer.borderColor = UIColor.systemGray4.cgColor
        } else {
            set.insert(item)
            button.backgroundColor = .systemBlue
            button.setTitleColor(.white, for: .normal)
            button.layer.borderColor = UIColor.systemBlue.cgColor
        }
    }
    
    @objc private func closeTapped() {
        dismiss(animated: true)
    }
    
    @objc private func skipTapped() {
        let preferences = TravelPreferences(
            interests: [],
            foods: [],
            accommodations: []
        )
        delegate?.didCompletePreferences(preferences)
        dismiss(animated: true)
    }
    
    @objc private func confirmTapped() {
        let preferences = TravelPreferences(
            interests: Array(selectedInterests),
            foods: Array(selectedFoods),
            accommodations: Array(selectedAccommodations)
        )
        delegate?.didCompletePreferences(preferences)
        dismiss(animated: true)
    }
}

// MARK: - Models
struct TravelPreferences {
    let interests: [String]
    let foods: [String]
    let accommodations: [String]
} 