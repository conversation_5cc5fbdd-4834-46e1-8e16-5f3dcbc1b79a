import UIKit
import MapKit
import CoreLocation

class GlobalLifeViewController: UIViewController {
    
    // MARK: - UI Components
    private let mapView = MKMapView()
    private let statsContainerView = UIView()
    private let statsLabel = UILabel()
    private let visitedCitiesContainerView = UIView()
    private let visitedCitiesScrollView = UIScrollView()
    private let visitedCitiesStackView = UIStackView()
    private let actionButton = UIButton(type: .system)
    
    // MARK: - Data
    private let dataManager = GlobalLifeDataManager.shared
    private var currentStats: GlobalLifeStats?
    private var visitedProvinces: [Province] = []
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupMapView()
        loadData()
        
        // 添加一些测试数据
        addTestData()
        
        // 检查位置权限并设置模拟器位置
        checkLocationPermission()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        refreshStats()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "环球人生"
        view.backgroundColor = .systemBackground
        
        // 设置导航栏
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            title: "选择城市",
            style: .plain,
            target: self,
            action: #selector(selectCityTapped)
        )
        
        setupMapView()
        setupStatsView()
        setupVisitedCitiesView()
        setupActionButton()
        setupConstraints()
    }
    
    private func setupMapView() {
        mapView.delegate = self
        mapView.mapType = .standard
        mapView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(mapView)
        
        // 设置中国地图初始区域 - 调整缩放级别让地图显示更合适
        let chinaRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 35.0, longitude: 105.0),
            latitudinalMeters: 6000000,  // 从 5000000 调整到 6000000，显示更大范围
            longitudinalMeters: 6000000  // 从 5000000 调整到 6000000，显示更大范围
        )
        mapView.setRegion(chinaRegion, animated: false)
    }
    
    private func setupStatsView() {
        statsContainerView.backgroundColor = .clear
        statsContainerView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(statsContainerView)
        
        // 统计信息标签
        statsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        statsLabel.textColor = .label
        statsLabel.textAlignment = .center
        statsLabel.numberOfLines = 0
        statsLabel.translatesAutoresizingMaskIntoConstraints = false
        statsContainerView.addSubview(statsLabel)
    }
    
    private func setupVisitedCitiesView() {
        visitedCitiesContainerView.backgroundColor = .systemBackground
        visitedCitiesContainerView.layer.cornerRadius = 12
        visitedCitiesContainerView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(visitedCitiesContainerView)
        
        visitedCitiesScrollView.showsVerticalScrollIndicator = false
        visitedCitiesScrollView.translatesAutoresizingMaskIntoConstraints = false
        visitedCitiesContainerView.addSubview(visitedCitiesScrollView)
        
        visitedCitiesStackView.axis = .vertical
        visitedCitiesStackView.spacing = 16
        visitedCitiesStackView.translatesAutoresizingMaskIntoConstraints = false
        visitedCitiesScrollView.addSubview(visitedCitiesStackView)
    }
    
    private func setupActionButton() {
        actionButton.setTitle("点亮足迹地图", for: .normal)
        actionButton.backgroundColor = UIColor.systemBlue
        actionButton.setTitleColor(.white, for: .normal)
        actionButton.layer.cornerRadius = 25
        actionButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        actionButton.translatesAutoresizingMaskIntoConstraints = false
        actionButton.addTarget(self, action: #selector(actionButtonTapped), for: .touchUpInside)
        
        // 添加点击图标
        let iconImageView = UIImageView(image: UIImage(systemName: "hand.point.up.left.fill"))
        iconImageView.tintColor = .white
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        actionButton.addSubview(iconImageView)
        
        view.addSubview(actionButton)
        
        NSLayoutConstraint.activate([
            iconImageView.leadingAnchor.constraint(equalTo: actionButton.leadingAnchor, constant: 20),
            iconImageView.centerYAnchor.constraint(equalTo: actionButton.centerYAnchor),
            iconImageView.widthAnchor.constraint(equalToConstant: 20),
            iconImageView.heightAnchor.constraint(equalToConstant: 20)
        ])
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 地图视图 - 占据上半部分
            mapView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            mapView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            mapView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            mapView.heightAnchor.constraint(equalTo: view.heightAnchor, multiplier: 0.55),
            
            // 统计信息容器
            statsContainerView.topAnchor.constraint(equalTo: mapView.bottomAnchor, constant: 8),
            statsContainerView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            statsContainerView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            statsContainerView.heightAnchor.constraint(equalToConstant: 30),
            
            statsLabel.topAnchor.constraint(equalTo: statsContainerView.topAnchor),
            statsLabel.leadingAnchor.constraint(equalTo: statsContainerView.leadingAnchor),
            statsLabel.trailingAnchor.constraint(equalTo: statsContainerView.trailingAnchor),
            statsLabel.bottomAnchor.constraint(equalTo: statsContainerView.bottomAnchor),
            
            // 已访问城市容器
            visitedCitiesContainerView.topAnchor.constraint(equalTo: statsContainerView.bottomAnchor, constant: 12),
            visitedCitiesContainerView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            visitedCitiesContainerView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            visitedCitiesContainerView.bottomAnchor.constraint(equalTo: actionButton.topAnchor, constant: -20),
            
            visitedCitiesScrollView.topAnchor.constraint(equalTo: visitedCitiesContainerView.topAnchor, constant: 16),
            visitedCitiesScrollView.leadingAnchor.constraint(equalTo: visitedCitiesContainerView.leadingAnchor, constant: 16),
            visitedCitiesScrollView.trailingAnchor.constraint(equalTo: visitedCitiesContainerView.trailingAnchor, constant: -16),
            visitedCitiesScrollView.bottomAnchor.constraint(equalTo: visitedCitiesContainerView.bottomAnchor, constant: -16),
            
            visitedCitiesStackView.topAnchor.constraint(equalTo: visitedCitiesScrollView.topAnchor),
            visitedCitiesStackView.leadingAnchor.constraint(equalTo: visitedCitiesScrollView.leadingAnchor),
            visitedCitiesStackView.trailingAnchor.constraint(equalTo: visitedCitiesScrollView.trailingAnchor),
            visitedCitiesStackView.bottomAnchor.constraint(equalTo: visitedCitiesScrollView.bottomAnchor),
            visitedCitiesStackView.widthAnchor.constraint(equalTo: visitedCitiesScrollView.widthAnchor),
            
            // 操作按钮
            actionButton.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -20),
            actionButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            actionButton.widthAnchor.constraint(equalToConstant: 200),
            actionButton.heightAnchor.constraint(equalToConstant: 50)
        ])
    }
    
    // MARK: - Data Loading
    private func loadData() {
        visitedProvinces = dataManager.getProvinces().filter { $0.isVisited }
        refreshStats()
        updateMapOverlays()
        updateVisitedCitiesDisplay()
    }
    
    private func refreshStats() {
        currentStats = dataManager.calculateGlobalLifeStats()
        updateStatsDisplay()
    }
    
    private func updateStatsDisplay() {
        guard let stats = currentStats else { return }
        
        // 匹配设计图的统计信息格式
        let statsText = String(format: "战胜 %.2f%% 的用户    点亮中国 %.2f%%，点亮世界 %.2f%%",
                              stats.userRanking,
                              stats.countryCoveragePercentage,
                              stats.worldCoveragePercentage)
        statsLabel.text = statsText
    }
    
    private func updateVisitedCitiesDisplay() {
        // 清除现有内容
        visitedCitiesStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        // 按国家分组显示已访问的城市
        let visitedCities = dataManager.getProvinces().flatMap { $0.cities }.filter { $0.isVisited }
        let groupedCities = Dictionary(grouping: visitedCities) { city in
            if city.countryId == "china" {
                return "中国"
            } else {
                return dataManager.getCountries().first { $0.id == city.countryId }?.name ?? "其他"
            }
        }
        
        for (countryName, cities) in groupedCities.sorted(by: { $0.key < $1.key }) {
            let countryView = createCountrySection(countryName: countryName, cities: cities)
            visitedCitiesStackView.addArrangedSubview(countryView)
        }
    }
    
    private func createCountrySection(countryName: String, cities: [City]) -> UIView {
        let containerView = UIView()
        
        // 国家标题
        let titleLabel = UILabel()
        titleLabel.text = countryName
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = .systemBlue
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(titleLabel)
        
        // 城市标签容器
        let citiesContainer = UIView()
        citiesContainer.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(citiesContainer)
        
        // 创建城市标签
        var currentX: CGFloat = 0
        var currentY: CGFloat = 0
        let labelHeight: CGFloat = 28
        let spacing: CGFloat = 8
        let maxWidth: CGFloat = UIScreen.main.bounds.width - 72 // 考虑边距
        
        for city in cities.prefix(10) { // 最多显示10个城市
            let cityLabel = UILabel()
            cityLabel.text = city.name
            cityLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
            cityLabel.textColor = .label
            cityLabel.backgroundColor = .systemGray6
            cityLabel.layer.cornerRadius = 14
            cityLabel.clipsToBounds = true
            cityLabel.textAlignment = .center
            cityLabel.translatesAutoresizingMaskIntoConstraints = false
            
            // 计算标签宽度
            let labelWidth = city.name.size(withAttributes: [.font: cityLabel.font!]).width + 16
            
            // 检查是否需要换行
            if currentX + labelWidth > maxWidth && currentX > 0 {
                currentX = 0
                currentY += labelHeight + spacing
            }
            
            citiesContainer.addSubview(cityLabel)
            
            NSLayoutConstraint.activate([
                cityLabel.leadingAnchor.constraint(equalTo: citiesContainer.leadingAnchor, constant: currentX),
                cityLabel.topAnchor.constraint(equalTo: citiesContainer.topAnchor, constant: currentY),
                cityLabel.widthAnchor.constraint(equalToConstant: labelWidth),
                cityLabel.heightAnchor.constraint(equalToConstant: labelHeight)
            ])
            
            currentX += labelWidth + spacing
        }
        
        // 设置容器约束
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            
            citiesContainer.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 8),
            citiesContainer.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            citiesContainer.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            citiesContainer.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
            citiesContainer.heightAnchor.constraint(equalToConstant: currentY + labelHeight)
        ])
        
        return containerView
    }
    
    private func updateMapOverlays() {
        // 清除现有覆盖层
        mapView.removeOverlays(mapView.overlays)
        mapView.removeAnnotations(mapView.annotations)
        
        // 获取所有省份数据
        let allProvinces = dataManager.getProvinces()
        
        // 为每个省份添加覆盖层（包括已访问和未访问的）
        for province in allProvinces {
            // 创建省份覆盖层（使用真实地理边界）
            addProvinceOverlays(for: province)
        }
        
        // 添加城市标记
        let visitedCities = allProvinces.flatMap { $0.cities }.filter { $0.isVisited }
        for city in visitedCities {
            let annotation = CityAnnotation()
            annotation.coordinate = city.coordinate
            annotation.title = city.name
            annotation.city = city
            mapView.addAnnotation(annotation)
        }
    }
    
    // 添加省份覆盖层（支持多个多边形）
    private func addProvinceOverlays(for province: Province) {
        // 优先使用新的 getProvinceOverlays 方法
        let overlays = ProvinceGeometry.shared.getProvinceOverlays(for: province.id, provinceName: province.name)
        
        print("为省份 \(province.name) 创建覆盖层: \(overlays.count) 个多边形")
        
        if !overlays.isEmpty {
            // 设置访问状态
            for overlay in overlays {
                overlay.subtitle = province.isVisited ? "visited" : "unvisited"
            }
            mapView.addOverlays(overlays)
        } else {
            // 如果没有找到地理数据，尝试使用旧方法
            let fallbackOverlay = createFallbackProvinceOverlay(for: province)
            mapView.addOverlay(fallbackOverlay)
        }
    }
    
    // 备用的省份覆盖层创建方法
    private func createFallbackProvinceOverlay(for province: Province) -> MKOverlay {
        // 优先使用Province对象中的坐标
        let coordinates = province.coordinates
        
        print("创建备用省份覆盖层: \(province.name), 坐标数量: \(coordinates.count)")
        
        if !coordinates.isEmpty {
            let polygon = MKPolygon(coordinates: coordinates, count: coordinates.count)
            polygon.title = province.name
            polygon.subtitle = province.isVisited ? "visited" : "unvisited"
            return polygon
        }
        
        // 如果Province对象中没有坐标，尝试从ProvinceGeometry获取
        let geometryCoordinates = ProvinceGeometry.shared.getProvinceCoordinates(for: province.id)
        print("从ProvinceGeometry获取坐标: \(province.name), 数量: \(geometryCoordinates.count)")
        if !geometryCoordinates.isEmpty {
            let polygon = MKPolygon(coordinates: geometryCoordinates, count: geometryCoordinates.count)
            polygon.title = province.name
            polygon.subtitle = province.isVisited ? "visited" : "unvisited"
            return polygon
        }
        
        // 如果还是没有找到边界数据，使用默认矩形
        print("使用默认矩形覆盖层: \(province.name)")
        return createDefaultProvinceOverlay(for: province)
    }
    
    // 创建默认省份覆盖层（备用方案）
    private func createDefaultProvinceOverlay(for province: Province) -> MKOverlay {
        let center = province.center
        
        // 根据省份大小创建不同尺寸的矩形
        let latitudeDelta: CLLocationDegrees
        let longitudeDelta: CLLocationDegrees
        
        // 根据省份名称设置不同的大小
        switch province.name {
        case "新疆", "西藏", "内蒙古":
            latitudeDelta = 8.0
            longitudeDelta = 10.0
        case "青海", "甘肃", "四川", "云南":
            latitudeDelta = 6.0
            longitudeDelta = 8.0
        case "黑龙江", "吉林", "辽宁":
            latitudeDelta = 4.0
            longitudeDelta = 6.0
        case "北京", "天津", "上海", "香港", "澳门":
            latitudeDelta = 0.5
            longitudeDelta = 0.5
        default:
            latitudeDelta = 3.0
            longitudeDelta = 4.0
        }
        
        // 创建矩形坐标
        let coordinates = [
            CLLocationCoordinate2D(latitude: center.latitude - latitudeDelta/2, longitude: center.longitude - longitudeDelta/2),
            CLLocationCoordinate2D(latitude: center.latitude - latitudeDelta/2, longitude: center.longitude + longitudeDelta/2),
            CLLocationCoordinate2D(latitude: center.latitude + latitudeDelta/2, longitude: center.longitude + longitudeDelta/2),
            CLLocationCoordinate2D(latitude: center.latitude + latitudeDelta/2, longitude: center.longitude - longitudeDelta/2)
        ]
        
        let polygon = MKPolygon(coordinates: coordinates, count: coordinates.count)
        polygon.title = province.name
        polygon.subtitle = province.isVisited ? "visited" : "unvisited"
        
        return polygon
    }
    
    // MARK: - Test Data
    private func addTestData() {
        // 添加一些测试数据来演示功能
        let testRegions = ["beijing", "shanghai", "guangdong", "jiangsu", "zhejiang"]
        
        for regionId in testRegions {
            dataManager.markRegionAsVisited(regionId, source: .manual)
        }
        
        // 添加一些测试城市
        let testCities = ["beijing_city_0", "shanghai_city_0", "guangdong_city_0", "guangdong_city_2"]
        for cityId in testCities {
            dataManager.markRegionAsVisited(cityId, source: .photoGPS)
        }
        
        // 刷新数据
        loadData()
    }
    
    // MARK: - Actions
    @objc private func selectCityTapped() {
        let citySelectionVC = CitySelectionViewController()
        citySelectionVC.delegate = self
        let navController = UINavigationController(rootViewController: citySelectionVC)
        present(navController, animated: true)
    }
    
    @objc private func actionButtonTapped() {
        let actionSheet = UIAlertController(title: "点亮足迹", message: "选择点亮方式", preferredStyle: .actionSheet)
        
        actionSheet.addAction(UIAlertAction(title: "从照片GPS自动点亮", style: .default) { [weak self] _ in
            self?.lightUpFromPhotos()
        })
        
        actionSheet.addAction(UIAlertAction(title: "手动选择城市", style: .default) { [weak self] _ in
            self?.selectCityTapped()
        })
        
        actionSheet.addAction(UIAlertAction(title: "查看年度报告", style: .default) { [weak self] _ in
            self?.showAnnualReport()
        })
        
        actionSheet.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(actionSheet, animated: true)
    }
    
    private func lightUpFromPhotos() {
        let loadingAlert = UIAlertController(title: "分析照片", message: "正在分析照片GPS信息...", preferredStyle: .alert)
        present(loadingAlert, animated: true)
        
        dataManager.analyzePhotosGPS { [weak self] cities in
            DispatchQueue.main.async {
                loadingAlert.dismiss(animated: true) {
                    self?.handlePhotoCities(cities)
                }
            }
        }
    }
    
    private func handlePhotoCities(_ cities: [City]) {
        if cities.isEmpty {
            showAlert(title: "未找到照片", message: "没有找到包含GPS信息的照片")
            return
        }
        
        // 自动点亮这些城市
        for city in cities {
            dataManager.markRegionAsVisited(city.id, source: .photoGPS)
        }
        
        // 刷新显示
        loadData()
        
        showAlert(title: "点亮成功", message: "已从 \(cities.count) 张照片中点亮了 \(Set(cities.map { $0.name }).count) 个城市")
    }
    
    private func showAnnualReport() {
        let reportVC = AnnualReportViewController()
        reportVC.stats = currentStats
        let navController = UINavigationController(rootViewController: reportVC)
        present(navController, animated: true)
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    // MARK: - Location Permission
    private func checkLocationPermission() {
        #if targetEnvironment(simulator)
        // 模拟器环境：设置默认位置为北京
        print("运行在模拟器中，使用默认位置：北京")
        let beijingRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
            latitudinalMeters: 2000000,  // 调整北京区域的缩放级别
            longitudinalMeters: 2000000
        )
        mapView.setRegion(beijingRegion, animated: true)
        #else
        // 真机环境：请求位置权限
        dataManager.getCurrentLocation { [weak self] location in
            DispatchQueue.main.async {
                if let location = location {
                    let region = MKCoordinateRegion(
                        center: location.coordinate,
                        latitudinalMeters: 2000000,  // 调整用户位置的缩放级别
                        longitudinalMeters: 2000000
                    )
                    self?.mapView.setRegion(region, animated: true)
                } else {
                    // 位置获取失败，使用默认位置
                    let defaultRegion = MKCoordinateRegion(
                        center: CLLocationCoordinate2D(latitude: 35.0, longitude: 105.0),
                        latitudinalMeters: 6000000,  // 从 5000000 调整到 6000000，显示更大范围
                        longitudinalMeters: 6000000  // 从 5000000 调整到 6000000，显示更大范围
                    )
                    self?.mapView.setRegion(defaultRegion, animated: true)
                }
            }
        }
        #endif
    }
}

// MARK: - MKMapViewDelegate
extension GlobalLifeViewController: MKMapViewDelegate {
    
    func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
        if let polygon = overlay as? MKPolygon {
            let renderer = MKPolygonRenderer(polygon: polygon)
            
            // 根据省份访问状态设置不同颜色
            if polygon.subtitle == "visited" {
                // 已访问的省份 - 蓝色，匹配设计图
                renderer.fillColor = UIColor.systemBlue.withAlphaComponent(0.6)
                renderer.strokeColor = UIColor.systemBlue
                renderer.lineWidth = 1.5
            } else {
                // 未访问的省份 - 浅灰色边框，无填充
                renderer.fillColor = UIColor.clear
                renderer.strokeColor = UIColor.systemGray5
                renderer.lineWidth = 0.5
            }
            
            return renderer
        }
        return MKOverlayRenderer(overlay: overlay)
    }
    
    func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
        if let cityAnnotation = annotation as? CityAnnotation {
            let identifier = "CityAnnotation"
            var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)
            
            if annotationView == nil {
                annotationView = MKAnnotationView(annotation: annotation, reuseIdentifier: identifier)
                annotationView?.canShowCallout = true
                
                // 根据访问来源设置不同颜色
                let circleView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 16))
                circleView.layer.cornerRadius = 8
                circleView.layer.borderWidth = 2
                circleView.layer.borderColor = UIColor.white.cgColor
                
                if cityAnnotation.city?.visitSource == .photoGPS {
                    circleView.backgroundColor = UIColor.systemGreen.withAlphaComponent(0.8) // 浅绿色
                } else {
                    circleView.backgroundColor = UIColor.systemBlue.withAlphaComponent(0.8) // 浅蓝色
                }
                
                annotationView?.addSubview(circleView)
                annotationView?.frame = circleView.frame
            }
            
            return annotationView
        }
        
        return nil
    }
    
    func mapView(_ mapView: MKMapView, didSelect view: MKAnnotationView) {
        if let cityAnnotation = view.annotation as? CityAnnotation,
           let city = cityAnnotation.city {
            
            let alert = UIAlertController(
                title: city.name,
                message: "访问来源: \(city.visitSource == .photoGPS ? "照片GPS" : "手动标记")",
                preferredStyle: .alert
            )
            
            alert.addAction(UIAlertAction(title: "取消点亮", style: .destructive) { [weak self] _ in
                self?.dataManager.unmarkRegionAsVisited(city.id)
                self?.loadData()
            })
            
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            
            present(alert, animated: true)
        }
    }
    
    func mapView(_ mapView: MKMapView, didTapAt coordinate: CLLocationCoordinate2D) {
        // 检查点击的位置是否在某个省份区域内
        for overlay in mapView.overlays {
            if let polygon = overlay as? MKPolygon,
               let provinceName = polygon.title {
                
                // 简单的点击检测（实际应用中需要更精确的几何计算）
                if let province = dataManager.getProvinces().first(where: { $0.name == provinceName }) {
                    let distance = coordinate.distance(to: province.center)
                    
                    // 如果点击位置在省份中心附近（根据省份大小调整距离）
                    let maxDistance: Double
                    switch province.name {
                    case "新疆", "西藏", "内蒙古":
                        maxDistance = 500000 // 500公里
                    case "青海", "甘肃", "四川", "云南":
                        maxDistance = 300000 // 300公里
                    case "北京", "天津", "上海", "香港", "澳门":
                        maxDistance = 50000  // 50公里
                    default:
                        maxDistance = 200000 // 200公里
                    }
                    
                    if distance < maxDistance {
                        showProvinceInfo(province)
                        break
                    }
                }
            }
        }
    }
    
    private func showProvinceInfo(_ province: Province) {
        let visitedCities = province.cities.filter { $0.isVisited }
        let message = """
        已访问城市: \(visitedCities.count)/\(province.cities.count)
        覆盖率: \(String(format: "%.1f", Double(visitedCities.count) / Double(province.cities.count) * 100))%
        
        \(province.isVisited ? "✅ 已点亮" : "⭕ 未点亮")
        """
        
        let alert = UIAlertController(
            title: province.name,
            message: message,
            preferredStyle: .alert
        )
        
        if !province.isVisited {
            alert.addAction(UIAlertAction(title: "点亮整个省份", style: .default) { [weak self] _ in
                self?.dataManager.markRegionAsVisited(province.id, source: .manual)
                self?.loadData()
            })
        } else {
            alert.addAction(UIAlertAction(title: "取消点亮", style: .destructive) { [weak self] _ in
                self?.dataManager.unmarkRegionAsVisited(province.id)
                // 同时取消该省份所有城市的点亮
                for city in province.cities {
                    if city.isVisited {
                        self?.dataManager.unmarkRegionAsVisited(city.id)
                    }
                }
                self?.loadData()
            })
        }
        
        alert.addAction(UIAlertAction(title: "查看城市列表", style: .default) { [weak self] _ in
            self?.showCityListForProvince(province)
        })
        
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        
        present(alert, animated: true)
    }
    
    private func showCityListForProvince(_ province: Province) {
        let citySelectionVC = CitySelectionViewController()
        citySelectionVC.selectedProvince = province
        citySelectionVC.delegate = self
        let navController = UINavigationController(rootViewController: citySelectionVC)
        present(navController, animated: true)
    }
}

// MARK: - CitySelectionDelegate
extension GlobalLifeViewController: CitySelectionDelegate {
    func didSelectCities(_ cities: [City]) {
        // 收集需要点亮的省份
        var provincesToLight: Set<String> = []
        
        for city in cities {
            // 点亮城市
            dataManager.markRegionAsVisited(city.id, source: .manual)
            
            // 找到城市所属的省份并点亮
            if let province = dataManager.getProvinces().first(where: { $0.cities.contains(where: { $0.id == city.id }) }) {
                provincesToLight.insert(province.id)
            }
        }
        
        // 点亮所有相关省份
        for provinceId in provincesToLight {
            dataManager.markRegionAsVisited(provinceId, source: .manual)
        }
        
        loadData()
        
        // 移除自动放大地图的功能
    }
    
    func didSelectProvinces(_ provinces: [Province]) {
        for province in provinces {
            dataManager.markRegionAsVisited(province.id, source: .manual)
        }
        loadData()
        
        // 移除自动放大地图的功能
    }
}

// MARK: - Custom Annotation
class CityAnnotation: NSObject, MKAnnotation {
    var coordinate: CLLocationCoordinate2D = CLLocationCoordinate2D()
    var title: String?
    var subtitle: String?
    var city: City?
} 