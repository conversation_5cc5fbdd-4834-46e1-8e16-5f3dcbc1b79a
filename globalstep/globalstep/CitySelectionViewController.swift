import UIKit

protocol CitySelectionDelegate: AnyObject {
    func didSelectCities(_ cities: [City])
    func didSelectProvinces(_ provinces: [Province])
}

class CitySelectionViewController: UIViewController {
    
    // MARK: - Properties
    weak var delegate: CitySelectionDelegate?
    private let dataManager = GlobalLifeDataManager.shared
    var selectedProvince: Province? // 用于显示特定省份的城市
    
    // 多选状态管理
    private var selectedCities: Set<String> = []
    private var selectedProvinces: Set<String> = []
    
    // MARK: - UI Components
    private let segmentedControl = UISegmentedControl(items: ["中国", "亚洲", "欧洲", "北美洲", "非洲", "南美洲"])
    private let tableView = UITableView(frame: .zero, style: .grouped)
    private let selectionCountLabel = UILabel()
    
    // MARK: - Data
    private var currentContinent = 0 // 0: 中国, 1: 亚洲, 2: 欧洲, 等等
    private var provinces: [Province] = []
    private var countries: [Country] = []
    private var displayProvinces: [Province] = []
    private var displayCities: [City] = []
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // 如果是显示特定省份，修改标题
        if let province = selectedProvince {
            title = "\(province.name) - 选择城市"
        } else {
            title = "选择城市"
        }
        
        view.backgroundColor = .systemBackground
        
        // 导航栏
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            title: "确定",
            style: .done,
            target: self,
            action: #selector(confirmTapped)
        )
        
        // 选择数量标签
        selectionCountLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        selectionCountLabel.textColor = .systemBlue
        selectionCountLabel.textAlignment = .center
        selectionCountLabel.text = "已选择 0 个地点"
        selectionCountLabel.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(selectionCountLabel)
        
        // 如果是显示特定省份，隐藏分段控制器
        if selectedProvince == nil {
            // 分段控制器
            segmentedControl.selectedSegmentIndex = 0
            segmentedControl.addTarget(self, action: #selector(segmentChanged), for: .valueChanged)
            segmentedControl.translatesAutoresizingMaskIntoConstraints = false
            view.addSubview(segmentedControl)
        }
        
        // 表格视图
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "Cell")
        tableView.register(ProvinceTableViewCell.self, forCellReuseIdentifier: "ProvinceCell")
        tableView.allowsMultipleSelection = true
        tableView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(tableView)
        
        // 约束
        if selectedProvince == nil {
            NSLayoutConstraint.activate([
                segmentedControl.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 10),
                segmentedControl.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
                segmentedControl.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
                segmentedControl.heightAnchor.constraint(equalToConstant: 32),
                
                selectionCountLabel.topAnchor.constraint(equalTo: segmentedControl.bottomAnchor, constant: 10),
                selectionCountLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
                selectionCountLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
                selectionCountLabel.heightAnchor.constraint(equalToConstant: 20),
                
                tableView.topAnchor.constraint(equalTo: selectionCountLabel.bottomAnchor, constant: 10),
                tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
            ])
        } else {
            NSLayoutConstraint.activate([
                selectionCountLabel.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 10),
                selectionCountLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
                selectionCountLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
                selectionCountLabel.heightAnchor.constraint(equalToConstant: 20),
                
                tableView.topAnchor.constraint(equalTo: selectionCountLabel.bottomAnchor, constant: 10),
                tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
            ])
        }
    }
    
    // MARK: - Data Loading
    private func loadData() {
        provinces = dataManager.getProvinces()
        countries = dataManager.getCountries()
        updateTableData()
    }
    
    private func updateTableData() {
        // 如果是显示特定省份，只显示该省份的城市
        if let province = selectedProvince {
            displayProvinces = [province]
            displayCities = []
            tableView.reloadData()
            return
        }
        
        switch currentContinent {
        case 0: // 中国
            displayProvinces = provinces
            displayCities = []
        case 1: // 亚洲
            displayProvinces = []
            displayCities = countries.filter { ["japan", "korea", "thailand", "singapore", "malaysia"].contains($0.id) }
                .flatMap { $0.cities }
        case 2: // 欧洲
            displayProvinces = []
            displayCities = countries.filter { ["uk", "france", "germany", "italy", "spain"].contains($0.id) }
                .flatMap { $0.cities }
        case 3: // 北美洲
            displayProvinces = []
            displayCities = countries.filter { ["usa", "canada"].contains($0.id) }
                .flatMap { $0.cities }
        case 4: // 非洲
            displayProvinces = []
            displayCities = []
        case 5: // 南美洲
            displayProvinces = []
            displayCities = countries.filter { ["brazil"].contains($0.id) }
                .flatMap { $0.cities }
        default:
            displayProvinces = []
            displayCities = []
        }
        
        tableView.reloadData()
        updateSelectionCount()
    }
    
    private func updateSelectionCount() {
        let totalSelected = selectedCities.count + selectedProvinces.count
        selectionCountLabel.text = "已选择 \(totalSelected) 个地点"
    }
    
    // MARK: - Actions
    @objc private func cancelTapped() {
        dismiss(animated: true)
    }
    
    @objc private func confirmTapped() {
        // 收集选中的城市和省份
        var selectedCityObjects: [City] = []
        var selectedProvinceObjects: [Province] = []
        
        // 添加选中的城市
        for cityId in selectedCities {
            if let city = findCityById(cityId) {
                selectedCityObjects.append(city)
            }
        }
        
        // 添加选中的省份
        for provinceId in selectedProvinces {
            if let province = provinces.first(where: { $0.id == provinceId }) {
                selectedProvinceObjects.append(province)
            }
        }
        
        // 通知代理
        if !selectedCityObjects.isEmpty {
            delegate?.didSelectCities(selectedCityObjects)
        }
        if !selectedProvinceObjects.isEmpty {
            delegate?.didSelectProvinces(selectedProvinceObjects)
        }
        
        dismiss(animated: true)
    }
    
    @objc private func segmentChanged() {
        currentContinent = segmentedControl.selectedSegmentIndex
        // 清除当前选择状态
        selectedCities.removeAll()
        selectedProvinces.removeAll()
        updateTableData()
    }
    
    private func findCityById(_ cityId: String) -> City? {
        for province in provinces {
            if let city = province.cities.first(where: { $0.id == cityId }) {
                return city
            }
        }
        for city in displayCities {
            if city.id == cityId {
                return city
            }
        }
        return nil
    }
}

// MARK: - UITableViewDataSource
extension CitySelectionViewController: UITableViewDataSource {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        if currentContinent == 0 {
            return displayProvinces.count
        } else {
            // 按国家分组
            let countries = Set(displayCities.map { $0.countryId })
            return countries.count
        }
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if currentContinent == 0 {
            let province = displayProvinces[section]
            
            // 如果是显示特定省份，只显示城市，不显示省份本身
            if selectedProvince != nil {
                return province.cities.count
            } else {
                // 正常模式：显示省份下的城市，加上省份本身
                return province.cities.count + 1 // +1 for the province itself
            }
        } else {
            // 其他大洲：显示国家下的城市
            let countries = Array(Set(displayCities.map { $0.countryId })).sorted()
            let countryId = countries[section]
            return displayCities.filter { $0.countryId == countryId }.count
        }
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        if currentContinent == 0 {
            let province = displayProvinces[section]
            
            // 如果是显示特定省份，显示更详细的标题
            if selectedProvince != nil {
                let visitedCount = province.cities.filter { $0.isVisited }.count
                return "\(province.name) - \(visitedCount)/\(province.cities.count) 个城市已访问"
            } else {
                return province.name
            }
        } else {
            let countries = Array(Set(displayCities.map { $0.countryId })).sorted()
            let countryId = countries[section]
            return dataManager.getCountries().first { $0.id == countryId }?.name ?? countryId
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if currentContinent == 0 {
            // 中国省份和城市
            let province = displayProvinces[indexPath.section]
            
            // 如果是显示特定省份，直接显示城市
            if selectedProvince != nil {
                let cell = tableView.dequeueReusableCell(withIdentifier: "Cell", for: indexPath)
                let city = province.cities[indexPath.row]
                cell.textLabel?.text = city.name
                
                // 设置选中状态
                if selectedCities.contains(city.id) {
                    cell.accessoryType = .checkmark
                    cell.textLabel?.textColor = .systemBlue
                } else {
                    cell.accessoryType = .none
                    cell.textLabel?.textColor = .label
                }
                
                // 添加访问来源标识
                if city.isVisited {
                    let sourceLabel = UILabel()
                    sourceLabel.text = city.visitSource == .photoGPS ? "📷" : "📍"
                    sourceLabel.font = UIFont.systemFont(ofSize: 12)
                    cell.accessoryView = sourceLabel
                } else if selectedCities.contains(city.id) {
                    cell.accessoryView = nil
                }
                
                return cell
            } else {
                // 正常模式：显示省份和城市
                if indexPath.row == 0 {
                    // 省份行
                    let cell = tableView.dequeueReusableCell(withIdentifier: "ProvinceCell", for: indexPath) as! ProvinceTableViewCell
                    cell.configure(with: province)
                    
                    // 设置省份选中状态
                    if selectedProvinces.contains(province.id) {
                        cell.accessoryType = .checkmark
                    } else {
                        cell.accessoryType = province.isVisited ? .checkmark : .disclosureIndicator
                    }
                    
                    return cell
                } else {
                    // 城市行
                    let cell = tableView.dequeueReusableCell(withIdentifier: "Cell", for: indexPath)
                    let city = province.cities[indexPath.row - 1]
                    cell.textLabel?.text = city.name
                    
                    // 设置选中状态
                    if selectedCities.contains(city.id) {
                        cell.accessoryType = .checkmark
                        cell.textLabel?.textColor = .systemBlue
                    } else {
                        cell.accessoryType = city.isVisited ? .checkmark : .none
                        cell.textLabel?.textColor = city.isVisited ? .systemBlue : .label
                    }
                    
                    // 添加访问来源标识
                    if city.isVisited {
                        let sourceLabel = UILabel()
                        sourceLabel.text = city.visitSource == .photoGPS ? "📷" : "📍"
                        sourceLabel.font = UIFont.systemFont(ofSize: 12)
                        cell.accessoryView = sourceLabel
                    } else if selectedCities.contains(city.id) {
                        cell.accessoryView = nil
                    }
                    
                    return cell
                }
            }
        } else {
            // 其他大洲的城市
            let countries = Array(Set(displayCities.map { $0.countryId })).sorted()
            let countryId = countries[indexPath.section]
            let countryCities = displayCities.filter { $0.countryId == countryId }
            let city = countryCities[indexPath.row]
            
            let cell = tableView.dequeueReusableCell(withIdentifier: "Cell", for: indexPath)
            cell.textLabel?.text = city.name
            
            // 设置选中状态
            if selectedCities.contains(city.id) {
                cell.accessoryType = .checkmark
                cell.textLabel?.textColor = .systemBlue
            } else {
                cell.accessoryType = city.isVisited ? .checkmark : .none
                cell.textLabel?.textColor = city.isVisited ? .systemBlue : .label
            }
            
            return cell
        }
    }
}

// MARK: - UITableViewDelegate
extension CitySelectionViewController: UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        if currentContinent == 0 {
            // 中国
            let province = displayProvinces[indexPath.section]
            
            // 如果是显示特定省份，直接选择城市
            if selectedProvince != nil {
                let city = province.cities[indexPath.row]
                if selectedCities.contains(city.id) {
                    selectedCities.remove(city.id)
                } else {
                    selectedCities.insert(city.id)
                }
                tableView.reloadRows(at: [indexPath], with: .none)
                updateSelectionCount()
            } else {
                // 正常模式
                if indexPath.row == 0 {
                    // 选择了省份
                    let province = displayProvinces[indexPath.section]
                    if selectedProvinces.contains(province.id) {
                        selectedProvinces.remove(province.id)
                    } else {
                        selectedProvinces.insert(province.id)
                    }
                    tableView.reloadRows(at: [indexPath], with: .none)
                    updateSelectionCount()
                } else {
                    // 选择了城市
                    let city = province.cities[indexPath.row - 1]
                    if selectedCities.contains(city.id) {
                        selectedCities.remove(city.id)
                    } else {
                        selectedCities.insert(city.id)
                    }
                    tableView.reloadRows(at: [indexPath], with: .none)
                    updateSelectionCount()
                }
            }
        } else {
            // 其他大洲
            let countries = Array(Set(displayCities.map { $0.countryId })).sorted()
            let countryId = countries[indexPath.section]
            let countryCities = displayCities.filter { $0.countryId == countryId }
            let city = countryCities[indexPath.row]
            
            if selectedCities.contains(city.id) {
                selectedCities.remove(city.id)
            } else {
                selectedCities.insert(city.id)
            }
            tableView.reloadRows(at: [indexPath], with: .none)
            updateSelectionCount()
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        // 如果是显示特定省份，所有行都是城市行
        if selectedProvince != nil {
            return 44
        }
        
        if currentContinent == 0 && indexPath.row == 0 {
            return 60 // 省份行高一些
        }
        return 44
    }
}

// MARK: - Custom Cell
class ProvinceTableViewCell: UITableViewCell {
    
    private let nameLabel = UILabel()
    private let statsLabel = UILabel()
    private let visitedIndicator = UIView()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        // 省份名称
        nameLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(nameLabel)
        
        // 统计信息
        statsLabel.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        statsLabel.textColor = .secondaryLabel
        statsLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(statsLabel)
        
        // 访问指示器
        visitedIndicator.layer.cornerRadius = 4
        visitedIndicator.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(visitedIndicator)
        
        NSLayoutConstraint.activate([
            visitedIndicator.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 15),
            visitedIndicator.centerYAnchor.constraint(equalTo: contentView.centerYAnchor),
            visitedIndicator.widthAnchor.constraint(equalToConstant: 8),
            visitedIndicator.heightAnchor.constraint(equalToConstant: 8),
            
            nameLabel.leadingAnchor.constraint(equalTo: visitedIndicator.trailingAnchor, constant: 10),
            nameLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            nameLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -15),
            
            statsLabel.leadingAnchor.constraint(equalTo: nameLabel.leadingAnchor),
            statsLabel.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 2),
            statsLabel.trailingAnchor.constraint(equalTo: nameLabel.trailingAnchor),
            statsLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8)
        ])
    }
    
    func configure(with province: Province) {
        nameLabel.text = province.name
        statsLabel.text = "\(province.visitedCount)/\(province.totalCount) 个城市"
        
        if province.isVisited {
            visitedIndicator.backgroundColor = province.visitSource == .photoGPS ? .systemGreen : .systemBlue
            nameLabel.textColor = .systemBlue
        } else {
            visitedIndicator.backgroundColor = .systemGray4
            nameLabel.textColor = .label
        }
        
        accessoryType = province.isVisited ? .checkmark : .disclosureIndicator
    }
} 