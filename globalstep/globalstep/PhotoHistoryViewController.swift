import UIKit

class PhotoHistoryViewController: UIViewController {
    
    // MARK: - Properties
    private let dataManager = PhotoAlbumDataManager.shared
    private var photoRecords: [PhotoRecord] = []
    private var groupedRecords: [String: [PhotoRecord]] = [:]
    private var sectionTitles: [String] = []
    
    // MARK: - UI Components
    private let tableView = UITableView(frame: .zero, style: .grouped)
    private let segmentedControl = UISegmentedControl(items: ["全部", "本周", "本月", "本年"])
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        refreshData()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "打卡历史"
        view.backgroundColor = .systemBackground
        
        setupNavigationBar()
        setupSegmentedControl()
        setupTableView()
        setupConstraints()
    }
    
    private func setupNavigationBar() {
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "calendar"),
            style: .plain,
            target: self,
            action: #selector(calendarTapped)
        )
    }
    
    private func setupSegmentedControl() {
        segmentedControl.selectedSegmentIndex = 0
        segmentedControl.addTarget(self, action: #selector(segmentChanged), for: .valueChanged)
        segmentedControl.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(segmentedControl)
    }
    
    private func setupTableView() {
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(PhotoHistoryTableViewCell.self, forCellReuseIdentifier: "PhotoHistoryCell")
        tableView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(tableView)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            segmentedControl.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 16),
            segmentedControl.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16),
            segmentedControl.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16),
            
            tableView.topAnchor.constraint(equalTo: segmentedControl.bottomAnchor, constant: 16),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    // MARK: - Data Loading
    private func loadData() {
        photoRecords = dataManager.getPhotoRecords()
        filterRecords()
    }
    
    private func refreshData() {
        loadData()
    }
    
    private func filterRecords() {
        let calendar = Calendar.current
        let now = Date()
        
        let filteredRecords: [PhotoRecord]
        
        switch segmentedControl.selectedSegmentIndex {
        case 1: // 本周
            let weekAgo = calendar.date(byAdding: .weekOfYear, value: -1, to: now) ?? now
            filteredRecords = photoRecords.filter { $0.timestamp >= weekAgo }
        case 2: // 本月
            let monthAgo = calendar.date(byAdding: .month, value: -1, to: now) ?? now
            filteredRecords = photoRecords.filter { $0.timestamp >= monthAgo }
        case 3: // 本年
            let yearAgo = calendar.date(byAdding: .year, value: -1, to: now) ?? now
            filteredRecords = photoRecords.filter { $0.timestamp >= yearAgo }
        default: // 全部
            filteredRecords = photoRecords
        }
        
        groupRecordsByDate(filteredRecords)
        tableView.reloadData()
    }
    
    private func groupRecordsByDate(_ records: [PhotoRecord]) {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        
        groupedRecords = Dictionary(grouping: records) { record in
            formatter.string(from: record.timestamp)
        }
        
        sectionTitles = groupedRecords.keys.sorted { date1, date2 in
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy年MM月dd日"
            let d1 = formatter.date(from: date1) ?? Date()
            let d2 = formatter.date(from: date2) ?? Date()
            return d1 > d2 // 降序排列，最新的在前面
        }
    }
    
    // MARK: - Actions
    @objc private func segmentChanged() {
        filterRecords()
    }
    
    @objc private func calendarTapped() {
        let alert = UIAlertController(title: "选择时间", message: "功能开发中", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension PhotoHistoryViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return sectionTitles.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        let sectionTitle = sectionTitles[section]
        return groupedRecords[sectionTitle]?.count ?? 0
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        let sectionTitle = sectionTitles[section]
        let recordCount = groupedRecords[sectionTitle]?.count ?? 0
        return "\(sectionTitle) (\(recordCount)条记录)"
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "PhotoHistoryCell", for: indexPath) as! PhotoHistoryTableViewCell
        
        let sectionTitle = sectionTitles[indexPath.section]
        if let records = groupedRecords[sectionTitle] {
            let record = records[indexPath.row]
            cell.configure(with: record)
        }
        
        return cell
    }
}

// MARK: - UITableViewDelegate
extension PhotoHistoryViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let sectionTitle = sectionTitles[indexPath.section]
        if let records = groupedRecords[sectionTitle] {
            let record = records[indexPath.row]
            
            let detailVC = PhotoDetailViewController()
            detailVC.photoRecord = record
            navigationController?.pushViewController(detailVC, animated: true)
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 80
    }
}

// MARK: - Custom Cell
class PhotoHistoryTableViewCell: UITableViewCell {
    
    private let locationLabel = UILabel()
    private let timeLabel = UILabel()
    private let moodLabel = UILabel()
    private let noteLabel = UILabel()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        accessoryType = .disclosureIndicator
        
        locationLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        locationLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(locationLabel)
        
        timeLabel.font = UIFont.systemFont(ofSize: 14)
        timeLabel.textColor = .secondaryLabel
        timeLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(timeLabel)
        
        moodLabel.font = UIFont.systemFont(ofSize: 20)
        moodLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(moodLabel)
        
        noteLabel.font = UIFont.systemFont(ofSize: 14)
        noteLabel.textColor = .secondaryLabel
        noteLabel.numberOfLines = 2
        noteLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(noteLabel)
        
        NSLayoutConstraint.activate([
            moodLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            moodLabel.centerYAnchor.constraint(equalTo: contentView.centerYAnchor),
            moodLabel.widthAnchor.constraint(equalToConstant: 30),
            
            locationLabel.leadingAnchor.constraint(equalTo: moodLabel.trailingAnchor, constant: 12),
            locationLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 12),
            locationLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -40),
            
            timeLabel.leadingAnchor.constraint(equalTo: locationLabel.leadingAnchor),
            timeLabel.topAnchor.constraint(equalTo: locationLabel.bottomAnchor, constant: 4),
            timeLabel.trailingAnchor.constraint(equalTo: locationLabel.trailingAnchor),
            
            noteLabel.leadingAnchor.constraint(equalTo: locationLabel.leadingAnchor),
            noteLabel.topAnchor.constraint(equalTo: timeLabel.bottomAnchor, constant: 4),
            noteLabel.trailingAnchor.constraint(equalTo: locationLabel.trailingAnchor),
            noteLabel.bottomAnchor.constraint(lessThanOrEqualTo: contentView.bottomAnchor, constant: -12)
        ])
    }
    
    func configure(with record: PhotoRecord) {
        locationLabel.text = record.location?.displayAddress ?? "未知位置"
        timeLabel.text = record.formattedTime
        moodLabel.text = record.mood?.emoji ?? "📍"
        noteLabel.text = record.customNote ?? ""
        noteLabel.isHidden = (record.customNote?.isEmpty ?? true)
    }
} 