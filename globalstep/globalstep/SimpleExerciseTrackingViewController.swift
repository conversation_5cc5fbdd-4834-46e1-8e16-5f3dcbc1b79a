import UIKit
import MapKit
import CoreLocation

class SimpleExerciseTrackingViewController: UIViewController {
    
    // MARK: - Properties
    private var selectedExerciseType: ExerciseType = .running
    private let locationManager = CLLocationManager()
    private let dataManager = ExerciseDataManager.shared
    
    private var isTracking = false
    private var startTime: Date?
    private var currentDistance: Double = 0
    private var trajectoryPoints: [TrajectoryPoint] = []
    private var lastLocation: CLLocation?
    private var currentLocation: CLLocation?
    
    private var timer: Timer?
    private var currentDuration: TimeInterval = 0
    
    // MARK: - UI Components
    private let mapView = MKMapView()
    
    // 运动类型选择器
    private let exerciseTypeSelector = UISegmentedControl()
    
    // 运动数据显示
    private let dataDisplayView = UIView()
    private let distanceLabel = UILabel()
    private let distanceUnitLabel = UILabel()
    
    // 运动中状态显示
    private let trackingOverlayView = UIView()
    private let trackingDataStackView = UIStackView()
    private let paceLabel = UILabel()
    private let timeLabel = UILabel()
    private let caloriesLabel = UILabel()
    
    // 控制按钮
    private let controlButtonsView = UIView()
    private let startButton = UIButton(type: .system)
    private let pauseButton = UIButton(type: .system)
    private let stopButton = UIButton(type: .system)
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupLocationManager()
        requestLocationPermission()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        centerMapOnUserLocation()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "运动追踪"
        view.backgroundColor = .systemBackground
        
        // 导航栏配置
        navigationController?.navigationBar.prefersLargeTitles = false
        
        // 左侧菜单按钮
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "line.horizontal.3"),
            style: .plain,
            target: self,
            action: #selector(showMenuTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "chart.bar"),
            style: .plain,
            target: self,
            action: #selector(showStatsTapped)
        )
        
        setupMapView()
        setupExerciseTypeSelector()
        setupDataDisplay()
        setupTrackingOverlay()
        setupControlButtons()
        setupConstraints()
        updateUIForTrackingState()
    }
    
    private func setupMapView() {
        mapView.delegate = self
        mapView.mapType = .standard
        mapView.showsUserLocation = true
        mapView.userTrackingMode = .none
        mapView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(mapView)
    }
    
    private func setupExerciseTypeSelector() {
        let exerciseTypes: [ExerciseType] = [.running, .walking, .cycling, .hiking]
        
        for (index, type) in exerciseTypes.enumerated() {
            exerciseTypeSelector.insertSegment(withTitle: type.displayName, at: index, animated: false)
        }
        
        exerciseTypeSelector.selectedSegmentIndex = 0
        exerciseTypeSelector.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.9)
        exerciseTypeSelector.layer.cornerRadius = 8
        exerciseTypeSelector.translatesAutoresizingMaskIntoConstraints = false
        exerciseTypeSelector.addTarget(self, action: #selector(exerciseTypeChanged), for: .valueChanged)
        view.addSubview(exerciseTypeSelector)
    }
    
    private func setupDataDisplay() {
        dataDisplayView.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.95)
        dataDisplayView.layer.cornerRadius = 12
        dataDisplayView.layer.shadowColor = UIColor.black.cgColor
        dataDisplayView.layer.shadowOffset = CGSize(width: 0, height: 2)
        dataDisplayView.layer.shadowOpacity = 0.1
        dataDisplayView.layer.shadowRadius = 4
        dataDisplayView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(dataDisplayView)
        
        // 距离显示
        distanceLabel.text = "0.00"
        distanceLabel.font = UIFont.systemFont(ofSize: 48, weight: .bold)
        distanceLabel.textColor = .systemGreen
        distanceLabel.textAlignment = .center
        distanceLabel.adjustsFontSizeToFitWidth = true
        distanceLabel.minimumScaleFactor = 0.7
        distanceLabel.translatesAutoresizingMaskIntoConstraints = false
        dataDisplayView.addSubview(distanceLabel)
        
        distanceUnitLabel.text = "跑步总里程(公里)"
        distanceUnitLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        distanceUnitLabel.textColor = .secondaryLabel
        distanceUnitLabel.textAlignment = .center
        distanceUnitLabel.translatesAutoresizingMaskIntoConstraints = false
        dataDisplayView.addSubview(distanceUnitLabel)
    }
    
    private func setupTrackingOverlay() {
        trackingOverlayView.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        trackingOverlayView.isHidden = true
        trackingOverlayView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(trackingOverlayView)
        
        // 创建数据显示栈视图
        trackingDataStackView.axis = .horizontal
        trackingDataStackView.distribution = .fillEqually
        trackingDataStackView.spacing = 20
        trackingDataStackView.translatesAutoresizingMaskIntoConstraints = false
        trackingOverlayView.addSubview(trackingDataStackView)
        
        // 配速
        let paceContainer = createDataContainer(title: "配速", valueLabel: paceLabel, initialValue: "--'--\"")
        trackingDataStackView.addArrangedSubview(paceContainer)
        
        // 用时
        let timeContainer = createDataContainer(title: "用时", valueLabel: timeLabel, initialValue: "00:00:00")
        trackingDataStackView.addArrangedSubview(timeContainer)
        
        // 千卡
        let caloriesContainer = createDataContainer(title: "千卡", valueLabel: caloriesLabel, initialValue: "0")
        trackingDataStackView.addArrangedSubview(caloriesContainer)
    }
    
    private func createDataContainer(title: String, valueLabel: UILabel, initialValue: String) -> UIView {
        let container = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .white
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        valueLabel.text = initialValue
        valueLabel.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        valueLabel.textColor = .white
        valueLabel.textAlignment = .center
        valueLabel.adjustsFontSizeToFitWidth = true
        valueLabel.minimumScaleFactor = 0.8
        valueLabel.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(titleLabel)
        container.addSubview(valueLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            
            valueLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 8),
            valueLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            valueLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            valueLabel.bottomAnchor.constraint(equalTo: container.bottomAnchor)
        ])
        
        return container
    }
    
    private func setupControlButtons() {
        controlButtonsView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(controlButtonsView)
        
        // 开始按钮
        startButton.setTitle("开始", for: .normal)
        startButton.backgroundColor = .systemGreen
        startButton.setTitleColor(.white, for: .normal)
        startButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        startButton.layer.cornerRadius = 35
        startButton.translatesAutoresizingMaskIntoConstraints = false
        startButton.addTarget(self, action: #selector(startButtonTapped), for: .touchUpInside)
        controlButtonsView.addSubview(startButton)
        
        // 暂停按钮
        pauseButton.setImage(UIImage(systemName: "pause.fill"), for: .normal)
        pauseButton.backgroundColor = .systemGreen
        pauseButton.tintColor = .white
        pauseButton.layer.cornerRadius = 35
        pauseButton.translatesAutoresizingMaskIntoConstraints = false
        pauseButton.addTarget(self, action: #selector(pauseButtonTapped), for: .touchUpInside)
        pauseButton.isHidden = true
        controlButtonsView.addSubview(pauseButton)
        
        // 停止按钮
        stopButton.setImage(UIImage(systemName: "stop.fill"), for: .normal)
        stopButton.backgroundColor = .systemRed
        stopButton.tintColor = .white
        stopButton.layer.cornerRadius = 35
        stopButton.translatesAutoresizingMaskIntoConstraints = false
        stopButton.addTarget(self, action: #selector(stopButtonTapped), for: .touchUpInside)
        stopButton.isHidden = true
        controlButtonsView.addSubview(stopButton)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 地图视图 - 不覆盖底部安全区域
            mapView.topAnchor.constraint(equalTo: view.topAnchor),
            mapView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            mapView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            mapView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor),
            
            // 运动类型选择器
            exerciseTypeSelector.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 10),
            exerciseTypeSelector.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            exerciseTypeSelector.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            exerciseTypeSelector.heightAnchor.constraint(equalToConstant: 32),
            
            // 数据显示区域
            dataDisplayView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            dataDisplayView.centerYAnchor.constraint(equalTo: view.centerYAnchor, constant: -50),
            dataDisplayView.widthAnchor.constraint(equalToConstant: 280),
            dataDisplayView.heightAnchor.constraint(equalToConstant: 120),
            
            distanceLabel.centerXAnchor.constraint(equalTo: dataDisplayView.centerXAnchor),
            distanceLabel.centerYAnchor.constraint(equalTo: dataDisplayView.centerYAnchor, constant: -10),
            
            distanceUnitLabel.topAnchor.constraint(equalTo: distanceLabel.bottomAnchor, constant: 8),
            distanceUnitLabel.centerXAnchor.constraint(equalTo: dataDisplayView.centerXAnchor),
            
            // 运动追踪覆盖层 - 也不覆盖底部安全区域
            trackingOverlayView.topAnchor.constraint(equalTo: view.topAnchor),
            trackingOverlayView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            trackingOverlayView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            trackingOverlayView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor),
            
            trackingDataStackView.centerXAnchor.constraint(equalTo: trackingOverlayView.centerXAnchor),
            trackingDataStackView.centerYAnchor.constraint(equalTo: trackingOverlayView.centerYAnchor, constant: -50),
            trackingDataStackView.leadingAnchor.constraint(greaterThanOrEqualTo: trackingOverlayView.leadingAnchor, constant: 40),
            trackingDataStackView.trailingAnchor.constraint(lessThanOrEqualTo: trackingOverlayView.trailingAnchor, constant: -40),
            
            // 控制按钮
            controlButtonsView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -30),
            controlButtonsView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            controlButtonsView.widthAnchor.constraint(equalToConstant: 200),
            controlButtonsView.heightAnchor.constraint(equalToConstant: 70),
            
            // 开始按钮
            startButton.centerXAnchor.constraint(equalTo: controlButtonsView.centerXAnchor),
            startButton.centerYAnchor.constraint(equalTo: controlButtonsView.centerYAnchor),
            startButton.widthAnchor.constraint(equalToConstant: 120),
            startButton.heightAnchor.constraint(equalToConstant: 70),
            
            // 暂停按钮
            pauseButton.leadingAnchor.constraint(equalTo: controlButtonsView.leadingAnchor),
            pauseButton.centerYAnchor.constraint(equalTo: controlButtonsView.centerYAnchor),
            pauseButton.widthAnchor.constraint(equalToConstant: 70),
            pauseButton.heightAnchor.constraint(equalToConstant: 70),
            
            // 停止按钮
            stopButton.trailingAnchor.constraint(equalTo: controlButtonsView.trailingAnchor),
            stopButton.centerYAnchor.constraint(equalTo: controlButtonsView.centerYAnchor),
            stopButton.widthAnchor.constraint(equalToConstant: 70),
            stopButton.heightAnchor.constraint(equalToConstant: 70)
        ])
    }
    
    // MARK: - Location Manager
    private func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        locationManager.distanceFilter = 5.0
    }
    
    private func requestLocationPermission() {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.startUpdatingLocation()
        case .denied, .restricted:
            showLocationPermissionAlert()
        @unknown default:
            break
        }
    }
    
    private func centerMapOnUserLocation() {
        guard let userLocation = locationManager.location else { return }
        
        let region = MKCoordinateRegion(
            center: userLocation.coordinate,
            latitudinalMeters: 1000,
            longitudinalMeters: 1000
        )
        mapView.setRegion(region, animated: true)
        currentLocation = userLocation
    }
    
    private func showLocationPermissionAlert() {
        let alert = UIAlertController(
            title: "需要位置权限",
            message: "请在设置中允许应用使用位置服务以进行运动追踪",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "设置", style: .default) { _ in
            if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsURL)
            }
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        present(alert, animated: true)
    }
    
    // MARK: - Actions
    @objc private func exerciseTypeChanged() {
        let exerciseTypes: [ExerciseType] = [.running, .walking, .cycling, .hiking]
        selectedExerciseType = exerciseTypes[exerciseTypeSelector.selectedSegmentIndex]
        updateDataDisplayForExerciseType()
    }
    
    @objc private func startButtonTapped() {
        guard currentLocation != nil else {
            showAlert(title: "位置未就绪", message: "请等待GPS定位完成后再开始运动")
            return
        }
        startTracking()
    }
    
    @objc private func pauseButtonTapped() {
        pauseTracking()
    }
    
    @objc private func stopButtonTapped() {
        showStopConfirmation()
    }
    
    @objc private func showStatsTapped() {
        let statsVC = ExerciseStatsViewController()
        let dataManager = ExerciseDataManager.shared
        statsVC.exerciseStats = dataManager.getExerciseStats()
        let navController = UINavigationController(rootViewController: statsVC)
        present(navController, animated: true)
    }
    
    @objc private func showMenuTapped() {
        // 显示菜单选项
        let alert = UIAlertController(title: "运动菜单", message: nil, preferredStyle: .actionSheet)
        
        // 历史记录
        alert.addAction(UIAlertAction(title: "历史记录", style: .default) { [weak self] _ in
            self?.showExerciseHistory()
        })
        
        // 运动列表（原来的界面）
        alert.addAction(UIAlertAction(title: "运动列表", style: .default) { [weak self] _ in
            self?.showExerciseList()
        })
        
        // 设置
        alert.addAction(UIAlertAction(title: "设置", style: .default) { [weak self] _ in
            self?.showSettings()
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        // 为iPad设置来源
        if let popover = alert.popoverPresentationController {
            popover.barButtonItem = navigationItem.leftBarButtonItem
        }
        
        present(alert, animated: true)
    }
    
    private func showExerciseHistory() {
        let statsVC = ExerciseStatsViewController()
        let dataManager = ExerciseDataManager.shared
        statsVC.exerciseStats = dataManager.getExerciseStats()
        navigationController?.pushViewController(statsVC, animated: true)
    }
    
    private func showExerciseList() {
        let exerciseListVC = ExerciseViewController()
        navigationController?.pushViewController(exerciseListVC, animated: true)
    }
    
    private func showSettings() {
        let alert = UIAlertController(title: "设置", message: "运动设置功能正在开发中", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    // MARK: - Tracking Control
    private func startTracking() {
        guard !isTracking else { return }
        
        isTracking = true
        startTime = Date()
        currentDistance = 0
        trajectoryPoints.removeAll()
        lastLocation = currentLocation
        
        locationManager.startUpdatingLocation()
        startTimer()
        updateUIForTrackingState()
        
        print("开始运动追踪: \(selectedExerciseType.displayName)")
    }
    
    private func pauseTracking() {
        // 实现暂停逻辑
        locationManager.stopUpdatingLocation()
        stopTimer()
        
        // 可以添加暂停状态的UI更新
        pauseButton.setImage(UIImage(systemName: "play.fill"), for: .normal)
    }
    
    private func stopTracking() {
        guard isTracking else { return }
        
        isTracking = false
        locationManager.stopUpdatingLocation()
        stopTimer()
        
        saveExerciseRecord()
        updateUIForTrackingState()
        resetData()
    }
    
    private func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateTrackingData()
        }
    }
    
    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }
    
    private func updateTrackingData() {
        guard let startTime = startTime else { return }
        currentDuration = Date().timeIntervalSince(startTime)
        
        // 更新距离显示
        distanceLabel.text = String(format: "%.2f", currentDistance / 1000.0)
        
        // 更新用时
        let hours = Int(currentDuration) / 3600
        let minutes = Int(currentDuration) % 3600 / 60
        let seconds = Int(currentDuration) % 60
        timeLabel.text = String(format: "%02d:%02d:%02d", hours, minutes, seconds)
        
        // 更新配速（分钟/公里）
        if currentDistance > 0 {
            let pace = currentDuration / (currentDistance / 1000.0)
            let paceMinutes = Int(pace) / 60
            let paceSeconds = Int(pace) % 60
            paceLabel.text = String(format: "%d'%02d\"", paceMinutes, paceSeconds)
        }
        
        // 更新卡路里（简单估算）
        let calories = calculateCalories()
        caloriesLabel.text = String(format: "%.0f", calories)
    }
    
    private func calculateCalories() -> Double {
        // 简单的卡路里计算（基于运动类型和距离）
        let distanceKm = currentDistance / 1000.0
        let baseCaloriesPerKm: Double
        
        switch selectedExerciseType {
        case .running:
            baseCaloriesPerKm = 60
        case .walking:
            baseCaloriesPerKm = 40
        case .cycling:
            baseCaloriesPerKm = 30
        case .hiking:
            baseCaloriesPerKm = 50
        default:
            baseCaloriesPerKm = 40
        }
        
        return distanceKm * baseCaloriesPerKm
    }
    
    // MARK: - UI Updates
    private func updateUIForTrackingState() {
        if isTracking {
            // 显示追踪界面
            trackingOverlayView.isHidden = false
            dataDisplayView.isHidden = true
            exerciseTypeSelector.isHidden = true
            
            startButton.isHidden = true
            pauseButton.isHidden = false
            stopButton.isHidden = false
        } else {
            // 显示准备界面
            trackingOverlayView.isHidden = true
            dataDisplayView.isHidden = false
            exerciseTypeSelector.isHidden = false
            
            startButton.isHidden = false
            pauseButton.isHidden = true
            stopButton.isHidden = true
        }
    }
    
    private func updateDataDisplayForExerciseType() {
        distanceUnitLabel.text = "\(selectedExerciseType.displayName)总里程(公里)"
    }
    
    private func saveExerciseRecord() {
        guard let startTime = startTime else { return }
        
        let record = ExerciseRecord(
            id: UUID().uuidString,
            type: selectedExerciseType,
            startTime: startTime,
            endTime: Date(),
            duration: currentDuration,
            distance: currentDistance,
            calories: calculateCalories(),
            averageSpeed: currentDistance / currentDuration,
            maxSpeed: 0, // 可以计算最大速度
            trajectory: trajectoryPoints,
            notes: nil,
            weather: nil,
            temperature: nil,
            heartRate: nil
        )
        
        dataManager.addExerciseRecord(record)
        
        // 显示完成界面
        showExerciseCompletion(record: record)
    }
    
    private func resetData() {
        currentDuration = 0
        currentDistance = 0
        trajectoryPoints.removeAll()
        distanceLabel.text = "0.00"
        timeLabel.text = "00:00:00"
        paceLabel.text = "--'--\""
        caloriesLabel.text = "0"
    }
    
    private func showExerciseCompletion(record: ExerciseRecord) {
        let alert = UIAlertController(
            title: "运动完成",
            message: "距离: \(String(format: "%.2f", record.distanceInKm))公里\n用时: \(record.formattedDuration)\n卡路里: \(String(format: "%.0f", record.calories))",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    private func showStopConfirmation() {
        let alert = UIAlertController(
            title: "结束运动",
            message: "确定要结束当前运动吗？",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "确定", style: .destructive) { [weak self] _ in
            self?.stopTracking()
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        present(alert, animated: true)
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - CLLocationManagerDelegate
extension SimpleExerciseTrackingViewController: CLLocationManagerDelegate {
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let newLocation = locations.last else { return }
        
        currentLocation = newLocation
        
        if isTracking {
            // 记录轨迹点
            let trajectoryPoint = TrajectoryPoint(location: newLocation)
            trajectoryPoints.append(trajectoryPoint)
            
            // 计算距离
            if let lastLocation = lastLocation {
                let distance = newLocation.distance(from: lastLocation)
                if distance > 2 { // 过滤掉过小的移动
                    currentDistance += distance
                    self.lastLocation = newLocation
                }
            }
        }
        
        // 首次获取位置时居中地图
        if mapView.region.span.latitudeDelta > 0.01 {
            centerMapOnUserLocation()
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.startUpdatingLocation()
        case .denied, .restricted:
            showLocationPermissionAlert()
        case .notDetermined:
            break
        @unknown default:
            break
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("位置更新失败: \(error.localizedDescription)")
        showAlert(title: "定位失败", message: "无法获取当前位置，请检查GPS设置")
    }
}

// MARK: - MKMapViewDelegate
extension SimpleExerciseTrackingViewController: MKMapViewDelegate {
    func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
        if let polyline = overlay as? MKPolyline {
            let renderer = MKPolylineRenderer(polyline: polyline)
            renderer.strokeColor = selectedExerciseType.color
            renderer.lineWidth = 4.0
            return renderer
        }
        return MKOverlayRenderer(overlay: overlay)
    }
} 