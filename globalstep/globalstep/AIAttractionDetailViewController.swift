import UIKit

class AIAttractionDetailViewController: UIViewController {
    
    private let cityName: String
    private let interests: [String]
    private let days: Int
    
    // UI元素
    private let loadingLabel = UILabel()
    private let activityIndicator = UIActivityIndicatorView(style: .large)
    
    init(cityName: String, interests: [String], days: Int) {
        self.cityName = cityName
        self.interests = interests
        self.days = days
        super.init(nibName: nil, bundle: nil)
        print("🎯🎯🎯 AIAttractionDetailViewController INIT: \(cityName) 🎯🎯🎯")
        print("🔥🔥🔥 CREATED AI CONTROLLER - SHOULD BE VISIBLE NOW! 🔥🔥🔥")
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        print("🏁🏁🏁 AIAttractionDetailViewController viewDidLoad START 🏁🏁🏁")
        print("🚨🚨🚨 RED BACKGROUND SHOULD BE VISIBLE NOW! 🚨🚨🚨")
        
        // 强制设置背景色和基本属性
        view.backgroundColor = .systemRed  // 用红色确保能看到
        view.isHidden = false
        view.alpha = 1.0
        
        setupUI()
        startAIRecommendation()
        
        // 强制布局更新
        view.setNeedsLayout()
        view.layoutIfNeeded()
        
        print("✅✅✅ viewDidLoad COMPLETED - YOU SHOULD SEE RED SCREEN! ✅✅✅")
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        print("🎬 AIAttractionDetailViewController viewWillAppear")
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        print("🎪 AIAttractionDetailViewController viewDidAppear")
    }
    
    private func setupUI() {
        print("🔧 setupUI 开始")
        
        // 设置背景和导航 - 保持红色背景用于调试
        // view.backgroundColor = .systemBackground
        navigationItem.title = "🚀 AI景点推荐"
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            title: "返回",
            style: .plain,
            target: self,
            action: #selector(backTapped)
        )
        
        print("📱 导航栏设置完成")
        
        // 设置加载标签 - 使用更明显的样式
        loadingLabel.text = "🚀 AI正在为您推荐\(cityName)景点\n\n🔥 DeepSeek正在思考中..."
        loadingLabel.font = UIFont.boldSystemFont(ofSize: 20)
        loadingLabel.textColor = .white  // 白色文字在红色背景上
        loadingLabel.textAlignment = .center
        loadingLabel.numberOfLines = 0
        loadingLabel.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        loadingLabel.layer.cornerRadius = 15
        loadingLabel.layer.masksToBounds = true
        loadingLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // 设置活动指示器
        activityIndicator.color = .white
        activityIndicator.style = .large
        activityIndicator.translatesAutoresizingMaskIntoConstraints = false
        activityIndicator.startAnimating()
        
        print("🎨 UI元素创建完成")
        
        // 添加到视图
        view.addSubview(loadingLabel)
        view.addSubview(activityIndicator)
        
        print("➕ UI元素已添加到视图")
        
        // 设置约束 - 使用最简单的中心对齐
        let constraints = [
            loadingLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            loadingLabel.centerYAnchor.constraint(equalTo: view.centerYAnchor, constant: -50),
            loadingLabel.leadingAnchor.constraint(greaterThanOrEqualTo: view.leadingAnchor, constant: 30),
            loadingLabel.trailingAnchor.constraint(lessThanOrEqualTo: view.trailingAnchor, constant: -30),
            loadingLabel.heightAnchor.constraint(greaterThanOrEqualToConstant: 100),
            
            activityIndicator.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            activityIndicator.topAnchor.constraint(equalTo: loadingLabel.bottomAnchor, constant: 30)
        ]
        
        NSLayoutConstraint.activate(constraints)
        
        print("🔗 约束设置完成: \(constraints.count)个")
        print("✅ UI设置完成")
    }
    
    private func startAIRecommendation() {
        print("🚀 开始AI景点推荐")
        
        // 更新UI提示正在加载
        DispatchQueue.main.async {
            self.loadingLabel.text = "🔥 DeepSeek AI正在分析\(self.cityName)...\n\n根据您的兴趣: \(self.interests.joined(separator: ", "))\n推荐最适合的\(self.days)日游景点"
        }
        
        // 调用AI推荐
        TripPlanningEngine.shared.getAIAttractionRecommendations(
            city: cityName,
            interests: interests,
            days: days
        ) { [weak self] recommendations in
            DispatchQueue.main.async {
                self?.handleAIResponse(recommendations)
            }
        }
    }
    
    private func handleAIResponse(_ recommendations: [AttractionRecommendation]) {
        print("📥 收到AI推荐结果: \(recommendations.count)个景点")
        
        // 停止加载动画
        activityIndicator.stopAnimating()
        
        if recommendations.isEmpty {
            // 显示失败信息
            loadingLabel.text = "😔 AI推荐暂时不可用\n\n请稍后重试或返回查看本地推荐"
            
            // 添加重试按钮
            let retryButton = UIButton(type: .system)
            retryButton.setTitle("重新获取AI推荐", for: .normal)
            retryButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
            retryButton.backgroundColor = .systemBlue
            retryButton.setTitleColor(.white, for: .normal)
            retryButton.layer.cornerRadius = 25
            retryButton.translatesAutoresizingMaskIntoConstraints = false
            retryButton.addTarget(self, action: #selector(retryTapped), for: .touchUpInside)
            
            view.addSubview(retryButton)
            
            NSLayoutConstraint.activate([
                retryButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
                retryButton.topAnchor.constraint(equalTo: loadingLabel.bottomAnchor, constant: 30),
                retryButton.widthAnchor.constraint(equalToConstant: 200),
                retryButton.heightAnchor.constraint(equalToConstant: 50)
            ])
        } else {
            // 显示成功信息和推荐结果
            displayRecommendations(recommendations)
        }
    }
    
    private func displayRecommendations(_ recommendations: [AttractionRecommendation]) {
        // 更新标题显示成功
        loadingLabel.text = "🎉 AI推荐成功！为您找到\(recommendations.count)个精彩景点"
        
        // 创建滚动视图显示推荐结果
        let scrollView = UIScrollView()
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)
        
        let contentView = UIView()
        contentView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(contentView)
        
        // 创建垂直堆栈视图
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 20
        stackView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(stackView)
        
        // 添加每个推荐景点
        for (index, recommendation) in recommendations.enumerated() {
            let attractionView = createSimpleAttractionView(recommendation, index: index + 1)
            stackView.addArrangedSubview(attractionView)
        }
        
        // 设置约束
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: loadingLabel.bottomAnchor, constant: 20),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            stackView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            stackView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20)
        ])
    }
    
    private func createSimpleAttractionView(_ recommendation: AttractionRecommendation, index: Int) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .secondarySystemBackground
        containerView.layer.cornerRadius = 12
        
        let titleLabel = UILabel()
        titleLabel.text = "\(index). \(recommendation.name)"
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textColor = .label
        titleLabel.numberOfLines = 0
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let descLabel = UILabel()
        descLabel.text = recommendation.description
        descLabel.font = UIFont.systemFont(ofSize: 14)
        descLabel.textColor = .secondaryLabel
        descLabel.numberOfLines = 0
        descLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let ratingLabel = UILabel()
        ratingLabel.text = "⭐ \(recommendation.rating) | 💰 \(recommendation.ticketPrice)"
        ratingLabel.font = UIFont.systemFont(ofSize: 12)
        ratingLabel.textColor = .systemBlue
        ratingLabel.translatesAutoresizingMaskIntoConstraints = false
        
        containerView.addSubview(titleLabel)
        containerView.addSubview(descLabel)
        containerView.addSubview(ratingLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 15),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 15),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -15),
            
            descLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 8),
            descLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 15),
            descLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -15),
            
            ratingLabel.topAnchor.constraint(equalTo: descLabel.bottomAnchor, constant: 8),
            ratingLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 15),
            ratingLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -15),
            ratingLabel.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -15)
        ])
        
        return containerView
    }
    
    @objc private func backTapped() {
        print("🔙 返回按钮被点击")
        navigationController?.popViewController(animated: true)
    }
    
    @objc private func retryTapped() {
        print("🔄 重试按钮被点击")
        // 重新设置UI
        view.subviews.forEach { $0.removeFromSuperview() }
        setupUI()
        startAIRecommendation()
    }
}