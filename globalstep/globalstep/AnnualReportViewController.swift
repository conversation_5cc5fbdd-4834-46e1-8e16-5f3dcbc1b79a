import UIKit

class AnnualReportViewController: UIViewController {
    
    // MARK: - Properties
    var stats: GlobalLifeStats?
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let headerView = UIView()
    private let titleLabel = UILabel()
    private let yearLabel = UILabel()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupContent()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "年度报告"
        view.backgroundColor = .systemBackground
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .done,
            target: self,
            action: #selector(doneTapped)
        )
        
        // 滚动视图
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)
        
        contentView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(contentView)
        
        // 头部视图
        headerView.backgroundColor = UIColor.systemBlue
        headerView.layer.cornerRadius = 12
        headerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(headerView)
        
        // 标题
        titleLabel.text = "环球足迹年度报告"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = .white
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        headerView.addSubview(titleLabel)
        
        // 年份
        yearLabel.text = "\(Calendar.current.component(.year, from: Date())) 年"
        yearLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        yearLabel.textColor = .white
        yearLabel.textAlignment = .center
        yearLabel.translatesAutoresizingMaskIntoConstraints = false
        headerView.addSubview(yearLabel)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            headerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            headerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            headerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            headerView.heightAnchor.constraint(equalToConstant: 120),
            
            titleLabel.topAnchor.constraint(equalTo: headerView.topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: headerView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -20),
            
            yearLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 10),
            yearLabel.leadingAnchor.constraint(equalTo: headerView.leadingAnchor, constant: 20),
            yearLabel.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -20),
            yearLabel.bottomAnchor.constraint(equalTo: headerView.bottomAnchor, constant: -20)
        ])
    }
    
    private func setupContent() {
        guard let stats = stats else { return }
        
        var lastView: UIView = headerView
        
        // 主要成就
        let achievementCard = createAchievementCard(stats: stats)
        contentView.addSubview(achievementCard)
        achievementCard.topAnchor.constraint(equalTo: lastView.bottomAnchor, constant: 20).isActive = true
        achievementCard.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20).isActive = true
        achievementCard.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20).isActive = true
        lastView = achievementCard
        
        // 距离统计
        let distanceCard = createDistanceCard(stats: stats)
        contentView.addSubview(distanceCard)
        distanceCard.topAnchor.constraint(equalTo: lastView.bottomAnchor, constant: 20).isActive = true
        distanceCard.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20).isActive = true
        distanceCard.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20).isActive = true
        lastView = distanceCard
        
        // 地区统计
        let regionCard = createRegionCard(stats: stats)
        contentView.addSubview(regionCard)
        regionCard.topAnchor.constraint(equalTo: lastView.bottomAnchor, constant: 20).isActive = true
        regionCard.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20).isActive = true
        regionCard.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20).isActive = true
        lastView = regionCard
        
        // 照片统计
        let photoCard = createPhotoCard(stats: stats)
        contentView.addSubview(photoCard)
        photoCard.topAnchor.constraint(equalTo: lastView.bottomAnchor, constant: 20).isActive = true
        photoCard.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20).isActive = true
        photoCard.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20).isActive = true
        lastView = photoCard
        
        // 排名统计
        let rankingCard = createRankingCard(stats: stats)
        contentView.addSubview(rankingCard)
        rankingCard.topAnchor.constraint(equalTo: lastView.bottomAnchor, constant: 20).isActive = true
        rankingCard.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20).isActive = true
        rankingCard.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20).isActive = true
        lastView = rankingCard
        
        // 底部间距
        lastView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20).isActive = true
    }
    
    // MARK: - Card Creation Methods
    private func createAchievementCard(stats: GlobalLifeStats) -> UIView {
        let card = createCard(title: "🏆 主要成就", color: .systemOrange)
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.translatesAutoresizingMaskIntoConstraints = false
        card.addSubview(stackView)
        
        // 成就项目
        let achievements = [
            ("🌍", "探索了 \(stats.visitedCountries) 个国家"),
            ("🏙️", "点亮了 \(stats.visitedCities) 个城市"),
            ("📍", "访问了 \(stats.visitedProvinces) 个省份"),
            ("📅", "旅行了 \(stats.totalTravelDays) 天"),
            ("🚀", "战胜了 \(String(format: "%.1f", stats.userRanking))% 的用户")
        ]
        
        for (emoji, text) in achievements {
            let achievementView = createAchievementItem(emoji: emoji, text: text)
            stackView.addArrangedSubview(achievementView)
        }
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: card.topAnchor, constant: 50),
            stackView.leadingAnchor.constraint(equalTo: card.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: card.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: card.bottomAnchor, constant: -20)
        ])
        
        return card
    }
    
    private func createDistanceCard(stats: GlobalLifeStats) -> UIView {
        let card = createCard(title: "🛣️ 距离统计", color: .systemGreen)
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 15
        stackView.translatesAutoresizingMaskIntoConstraints = false
        card.addSubview(stackView)
        
        // 总距离
        let totalDistanceLabel = UILabel()
        totalDistanceLabel.text = "总行程：\(String(format: "%.0f", stats.totalDistance)) 公里"
        totalDistanceLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        totalDistanceLabel.textAlignment = .center
        stackView.addArrangedSubview(totalDistanceLabel)
        
        // 对比数据
        let comparisons = [
            "🌍 相当于绕地球 \(String(format: "%.1f", stats.earthRounds)) 圈",
            "🇨🇳 相当于横跨中国 \(String(format: "%.1f", stats.chinaWidthTimes)) 次",
            "📏 相当于中国南北跨度 \(String(format: "%.1f", stats.chinaHeightTimes)) 次"
        ]
        
        for comparison in comparisons {
            let label = UILabel()
            label.text = comparison
            label.font = UIFont.systemFont(ofSize: 14)
            label.textColor = .secondaryLabel
            stackView.addArrangedSubview(label)
        }
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: card.topAnchor, constant: 50),
            stackView.leadingAnchor.constraint(equalTo: card.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: card.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: card.bottomAnchor, constant: -20)
        ])
        
        return card
    }
    
    private func createRegionCard(stats: GlobalLifeStats) -> UIView {
        let card = createCard(title: "🗺️ 地区覆盖", color: .systemPurple)
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 15
        stackView.translatesAutoresizingMaskIntoConstraints = false
        card.addSubview(stackView)
        
        // 覆盖率
        let coverageItems = [
            ("世界覆盖率", stats.worldCoveragePercentage, UIColor.systemBlue),
            ("中国覆盖率", stats.countryCoveragePercentage, UIColor.systemRed)
        ]
        
        for (title, percentage, color) in coverageItems {
            let progressView = createProgressView(title: title, percentage: percentage, color: color)
            stackView.addArrangedSubview(progressView)
        }
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: card.topAnchor, constant: 50),
            stackView.leadingAnchor.constraint(equalTo: card.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: card.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: card.bottomAnchor, constant: -20)
        ])
        
        return card
    }
    
    private func createPhotoCard(stats: GlobalLifeStats) -> UIView {
        let card = createCard(title: "📸 照片统计", color: .systemTeal)
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.translatesAutoresizingMaskIntoConstraints = false
        card.addSubview(stackView)
        
        let photoStats = [
            ("📷", "总照片数：\(stats.totalPhotos) 张"),
            ("📍", "带GPS照片：\(stats.photosWithGPS) 张"),
            ("✋", "手动标记：\(stats.manuallyMarkedPlaces) 个地点"),
            ("📊", "GPS覆盖率：\(String(format: "%.1f", Double(stats.photosWithGPS) / Double(stats.totalPhotos) * 100))%")
        ]
        
        for (emoji, text) in photoStats {
            let photoView = createAchievementItem(emoji: emoji, text: text)
            stackView.addArrangedSubview(photoView)
        }
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: card.topAnchor, constant: 50),
            stackView.leadingAnchor.constraint(equalTo: card.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: card.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: card.bottomAnchor, constant: -20)
        ])
        
        return card
    }
    
    private func createRankingCard(stats: GlobalLifeStats) -> UIView {
        let card = createCard(title: "🏅 排名统计", color: .systemIndigo)
        
        let rankingLabel = UILabel()
        rankingLabel.text = "您已战胜了\n\(String(format: "%.1f", stats.userRanking))%\n的用户"
        rankingLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        rankingLabel.textAlignment = .center
        rankingLabel.numberOfLines = 0
        rankingLabel.translatesAutoresizingMaskIntoConstraints = false
        card.addSubview(rankingLabel)
        
        let descriptionLabel = UILabel()
        descriptionLabel.text = "基于您的足迹覆盖率和旅行距离计算"
        descriptionLabel.font = UIFont.systemFont(ofSize: 12)
        descriptionLabel.textColor = .secondaryLabel
        descriptionLabel.textAlignment = .center
        descriptionLabel.translatesAutoresizingMaskIntoConstraints = false
        card.addSubview(descriptionLabel)
        
        NSLayoutConstraint.activate([
            rankingLabel.centerXAnchor.constraint(equalTo: card.centerXAnchor),
            rankingLabel.centerYAnchor.constraint(equalTo: card.centerYAnchor, constant: -10),
            
            descriptionLabel.topAnchor.constraint(equalTo: rankingLabel.bottomAnchor, constant: 10),
            descriptionLabel.leadingAnchor.constraint(equalTo: card.leadingAnchor, constant: 20),
            descriptionLabel.trailingAnchor.constraint(equalTo: card.trailingAnchor, constant: -20)
        ])
        
        return card
    }
    
    // MARK: - Helper Methods
    private func createCard(title: String, color: UIColor) -> UIView {
        let card = UIView()
        card.backgroundColor = .secondarySystemBackground
        card.layer.cornerRadius = 12
        card.layer.shadowColor = UIColor.black.cgColor
        card.layer.shadowOffset = CGSize(width: 0, height: 2)
        card.layer.shadowOpacity = 0.1
        card.layer.shadowRadius = 4
        card.translatesAutoresizingMaskIntoConstraints = false
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        titleLabel.textColor = color
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        card.addSubview(titleLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: card.topAnchor, constant: 15),
            titleLabel.leadingAnchor.constraint(equalTo: card.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: card.trailingAnchor, constant: -20),
            card.heightAnchor.constraint(greaterThanOrEqualToConstant: 120)
        ])
        
        return card
    }
    
    private func createAchievementItem(emoji: String, text: String) -> UIView {
        let container = UIView()
        
        let emojiLabel = UILabel()
        emojiLabel.text = emoji
        emojiLabel.font = UIFont.systemFont(ofSize: 20)
        emojiLabel.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(emojiLabel)
        
        let textLabel = UILabel()
        textLabel.text = text
        textLabel.font = UIFont.systemFont(ofSize: 14)
        textLabel.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(textLabel)
        
        NSLayoutConstraint.activate([
            emojiLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            emojiLabel.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            emojiLabel.widthAnchor.constraint(equalToConstant: 30),
            
            textLabel.leadingAnchor.constraint(equalTo: emojiLabel.trailingAnchor, constant: 10),
            textLabel.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            textLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            
            container.heightAnchor.constraint(equalToConstant: 30)
        ])
        
        return container
    }
    
    private func createProgressView(title: String, percentage: Double, color: UIColor) -> UIView {
        let container = UIView()
        container.translatesAutoresizingMaskIntoConstraints = false
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(titleLabel)
        
        let percentageLabel = UILabel()
        percentageLabel.text = "\(String(format: "%.1f", percentage))%"
        percentageLabel.font = UIFont.systemFont(ofSize: 14, weight: .bold)
        percentageLabel.textColor = color
        percentageLabel.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(percentageLabel)
        
        let progressBackground = UIView()
        progressBackground.backgroundColor = UIColor.systemGray5
        progressBackground.layer.cornerRadius = 4
        progressBackground.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(progressBackground)
        
        let progressBar = UIView()
        progressBar.backgroundColor = color
        progressBar.layer.cornerRadius = 4
        progressBar.translatesAutoresizingMaskIntoConstraints = false
        progressBackground.addSubview(progressBar)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            
            percentageLabel.topAnchor.constraint(equalTo: container.topAnchor),
            percentageLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            
            progressBackground.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 8),
            progressBackground.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            progressBackground.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            progressBackground.heightAnchor.constraint(equalToConstant: 8),
            progressBackground.bottomAnchor.constraint(equalTo: container.bottomAnchor),
            
            progressBar.topAnchor.constraint(equalTo: progressBackground.topAnchor),
            progressBar.leadingAnchor.constraint(equalTo: progressBackground.leadingAnchor),
            progressBar.bottomAnchor.constraint(equalTo: progressBackground.bottomAnchor),
            progressBar.widthAnchor.constraint(equalTo: progressBackground.widthAnchor, multiplier: min(percentage / 100.0, 1.0))
        ])
        
        return container
    }
    
    // MARK: - Actions
    @objc private func doneTapped() {
        dismiss(animated: true)
    }
} 