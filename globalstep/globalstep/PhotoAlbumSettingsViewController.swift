import UIKit

class PhotoAlbumSettingsViewController: UIViewController {
    
    // MARK: - Properties
    private let dataManager = PhotoAlbumDataManager.shared
    private var watermarkConfig: WatermarkConfig
    
    // MARK: - UI Components
    private let tableView = UITableView(frame: .zero, style: .grouped)
    
    // MARK: - Lifecycle
    init() {
        self.watermarkConfig = dataManager.getWatermarkConfig()
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "相册设置"
        view.backgroundColor = .systemBackground
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .save,
            target: self,
            action: #selector(saveTapped)
        )
        
        setupTableView()
    }
    
    private func setupTableView() {
        tableView.delegate = self
        tableView.dataSource = self
        tableView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(tableView)
        
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    // MARK: - Actions
    @objc private func cancelTapped() {
        dismiss(animated: true)
    }
    
    @objc private func saveTapped() {
        dataManager.updateWatermarkConfig(watermarkConfig)
        dismiss(animated: true)
    }
}

// MARK: - UITableViewDataSource
extension PhotoAlbumSettingsViewController: UITableViewDataSource {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return 3
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        switch section {
        case 0: return 4 // 水印内容设置
        case 1: return 1 // 水印位置设置
        case 2: return 2 // 水印样式设置
        default: return 0
        }
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        switch section {
        case 0: return "水印内容"
        case 1: return "水印位置"
        case 2: return "水印样式"
        default: return nil
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = UITableViewCell(style: .value1, reuseIdentifier: nil)
        
        switch indexPath.section {
        case 0: // 水印内容
            switch indexPath.row {
            case 0:
                cell.textLabel?.text = "显示时间"
                let switchControl = UISwitch()
                switchControl.isOn = watermarkConfig.showTime
                switchControl.addTarget(self, action: #selector(showTimeSwitchChanged(_:)), for: .valueChanged)
                cell.accessoryView = switchControl
                
            case 1:
                cell.textLabel?.text = "显示位置"
                let switchControl = UISwitch()
                switchControl.isOn = watermarkConfig.showLocation
                switchControl.addTarget(self, action: #selector(showLocationSwitchChanged(_:)), for: .valueChanged)
                cell.accessoryView = switchControl
                
            case 2:
                cell.textLabel?.text = "显示天气"
                let switchControl = UISwitch()
                switchControl.isOn = watermarkConfig.showWeather
                switchControl.addTarget(self, action: #selector(showWeatherSwitchChanged(_:)), for: .valueChanged)
                cell.accessoryView = switchControl
                
            case 3:
                cell.textLabel?.text = "显示备注"
                let switchControl = UISwitch()
                switchControl.isOn = watermarkConfig.showCustomText
                switchControl.addTarget(self, action: #selector(showCustomTextSwitchChanged(_:)), for: .valueChanged)
                cell.accessoryView = switchControl
                
            default:
                break
            }
            
        case 1: // 水印位置
            cell.textLabel?.text = "水印位置"
            cell.detailTextLabel?.text = watermarkConfig.position.displayName
            cell.accessoryType = .disclosureIndicator
            
        case 2: // 水印样式
            switch indexPath.row {
            case 0:
                cell.textLabel?.text = "字体大小"
                cell.detailTextLabel?.text = "\(Int(watermarkConfig.fontSize))"
                cell.accessoryType = .disclosureIndicator
                
            case 1:
                cell.textLabel?.text = "透明度"
                cell.detailTextLabel?.text = "\(Int(watermarkConfig.opacity * 100))%"
                cell.accessoryType = .disclosureIndicator
                
            default:
                break
            }
            
        default:
            break
        }
        
        return cell
    }
}

// MARK: - UITableViewDelegate
extension PhotoAlbumSettingsViewController: UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        switch indexPath.section {
        case 1: // 水印位置
            showPositionPicker()
            
        case 2: // 水印样式
            switch indexPath.row {
            case 0: // 字体大小
                showFontSizePicker()
            case 1: // 透明度
                showOpacityPicker()
            default:
                break
            }
            
        default:
            break
        }
    }
}

// MARK: - Switch Actions
extension PhotoAlbumSettingsViewController {
    
    @objc private func showTimeSwitchChanged(_ sender: UISwitch) {
        watermarkConfig = WatermarkConfig(
            showLocation: watermarkConfig.showLocation,
            showTime: sender.isOn,
            showWeather: watermarkConfig.showWeather,
            showCustomText: watermarkConfig.showCustomText,
            fontSize: watermarkConfig.fontSize,
            textColor: watermarkConfig.textColor,
            backgroundColor: watermarkConfig.backgroundColor,
            opacity: watermarkConfig.opacity,
            position: watermarkConfig.position
        )
    }
    
    @objc private func showLocationSwitchChanged(_ sender: UISwitch) {
        watermarkConfig = WatermarkConfig(
            showLocation: sender.isOn,
            showTime: watermarkConfig.showTime,
            showWeather: watermarkConfig.showWeather,
            showCustomText: watermarkConfig.showCustomText,
            fontSize: watermarkConfig.fontSize,
            textColor: watermarkConfig.textColor,
            backgroundColor: watermarkConfig.backgroundColor,
            opacity: watermarkConfig.opacity,
            position: watermarkConfig.position
        )
    }
    
    @objc private func showWeatherSwitchChanged(_ sender: UISwitch) {
        watermarkConfig = WatermarkConfig(
            showLocation: watermarkConfig.showLocation,
            showTime: watermarkConfig.showTime,
            showWeather: sender.isOn,
            showCustomText: watermarkConfig.showCustomText,
            fontSize: watermarkConfig.fontSize,
            textColor: watermarkConfig.textColor,
            backgroundColor: watermarkConfig.backgroundColor,
            opacity: watermarkConfig.opacity,
            position: watermarkConfig.position
        )
    }
    
    @objc private func showCustomTextSwitchChanged(_ sender: UISwitch) {
        watermarkConfig = WatermarkConfig(
            showLocation: watermarkConfig.showLocation,
            showTime: watermarkConfig.showTime,
            showWeather: watermarkConfig.showWeather,
            showCustomText: sender.isOn,
            fontSize: watermarkConfig.fontSize,
            textColor: watermarkConfig.textColor,
            backgroundColor: watermarkConfig.backgroundColor,
            opacity: watermarkConfig.opacity,
            position: watermarkConfig.position
        )
    }
}

// MARK: - Picker Methods
extension PhotoAlbumSettingsViewController {
    
    private func showPositionPicker() {
        let alertController = UIAlertController(title: "选择水印位置", message: nil, preferredStyle: .actionSheet)
        
        for position in WatermarkPosition.allCases {
            let action = UIAlertAction(title: position.displayName, style: .default) { _ in
                self.watermarkConfig = WatermarkConfig(
                    showLocation: self.watermarkConfig.showLocation,
                    showTime: self.watermarkConfig.showTime,
                    showWeather: self.watermarkConfig.showWeather,
                    showCustomText: self.watermarkConfig.showCustomText,
                    fontSize: self.watermarkConfig.fontSize,
                    textColor: self.watermarkConfig.textColor,
                    backgroundColor: self.watermarkConfig.backgroundColor,
                    opacity: self.watermarkConfig.opacity,
                    position: position
                )
                self.tableView.reloadData()
            }
            
            if position == watermarkConfig.position {
                action.setValue(true, forKey: "checked")
            }
            
            alertController.addAction(action)
        }
        
        alertController.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        if let popover = alertController.popoverPresentationController {
            popover.sourceView = tableView
            popover.sourceRect = tableView.rectForRow(at: IndexPath(row: 0, section: 1))
        }
        
        present(alertController, animated: true)
    }
    
    private func showFontSizePicker() {
        let alertController = UIAlertController(title: "选择字体大小", message: nil, preferredStyle: .actionSheet)
        
        let fontSizes: [CGFloat] = [12, 14, 16, 18, 20, 22, 24]
        
        for fontSize in fontSizes {
            let action = UIAlertAction(title: "\(Int(fontSize))", style: .default) { _ in
                self.watermarkConfig = WatermarkConfig(
                    showLocation: self.watermarkConfig.showLocation,
                    showTime: self.watermarkConfig.showTime,
                    showWeather: self.watermarkConfig.showWeather,
                    showCustomText: self.watermarkConfig.showCustomText,
                    fontSize: fontSize,
                    textColor: self.watermarkConfig.textColor,
                    backgroundColor: self.watermarkConfig.backgroundColor,
                    opacity: self.watermarkConfig.opacity,
                    position: self.watermarkConfig.position
                )
                self.tableView.reloadData()
            }
            
            if fontSize == watermarkConfig.fontSize {
                action.setValue(true, forKey: "checked")
            }
            
            alertController.addAction(action)
        }
        
        alertController.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        if let popover = alertController.popoverPresentationController {
            popover.sourceView = tableView
            popover.sourceRect = tableView.rectForRow(at: IndexPath(row: 0, section: 2))
        }
        
        present(alertController, animated: true)
    }
    
    private func showOpacityPicker() {
        let alertController = UIAlertController(title: "选择透明度", message: nil, preferredStyle: .actionSheet)
        
        let opacities: [CGFloat] = [0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
        
        for opacity in opacities {
            let action = UIAlertAction(title: "\(Int(opacity * 100))%", style: .default) { _ in
                self.watermarkConfig = WatermarkConfig(
                    showLocation: self.watermarkConfig.showLocation,
                    showTime: self.watermarkConfig.showTime,
                    showWeather: self.watermarkConfig.showWeather,
                    showCustomText: self.watermarkConfig.showCustomText,
                    fontSize: self.watermarkConfig.fontSize,
                    textColor: self.watermarkConfig.textColor,
                    backgroundColor: self.watermarkConfig.backgroundColor,
                    opacity: opacity,
                    position: self.watermarkConfig.position
                )
                self.tableView.reloadData()
            }
            
            if abs(opacity - watermarkConfig.opacity) < 0.01 {
                action.setValue(true, forKey: "checked")
            }
            
            alertController.addAction(action)
        }
        
        alertController.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        if let popover = alertController.popoverPresentationController {
            popover.sourceView = tableView
            popover.sourceRect = tableView.rectForRow(at: IndexPath(row: 1, section: 2))
        }
        
        present(alertController, animated: true)
    }
} 