import UIKit

class PhotoDetailViewController: UIViewController {
    
    // MARK: - Properties
    var photoRecord: PhotoRecord?
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let imageView = UIImageView()
    private let infoContainerView = UIView()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        displayPhotoData()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "照片详情"
        view.backgroundColor = .systemBackground
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .action,
            target: self,
            action: #selector(shareButtonTapped)
        )
        
        setupScrollView()
        setupImageView()
        setupInfoContainer()
        setupConstraints()
    }
    
    private func setupScrollView() {
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
    }
    
    private func setupImageView() {
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .secondarySystemBackground
        imageView.layer.cornerRadius = 12
        imageView.clipsToBounds = true
        imageView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(imageView)
        
        // 添加点击手势查看大图
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(imageViewTapped))
        imageView.addGestureRecognizer(tapGesture)
        imageView.isUserInteractionEnabled = true
    }
    
    private func setupInfoContainer() {
        infoContainerView.backgroundColor = .secondarySystemBackground
        infoContainerView.layer.cornerRadius = 12
        infoContainerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(infoContainerView)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 滚动视图
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            // 图片视图
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            imageView.heightAnchor.constraint(equalToConstant: 300),
            
            // 信息容器
            infoContainerView.topAnchor.constraint(equalTo: imageView.bottomAnchor, constant: 20),
            infoContainerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            infoContainerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            infoContainerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20)
        ])
    }
    
    // MARK: - Display Photo Data
    private func displayPhotoData() {
        guard let record = photoRecord else { return }
        
        // 显示图片
        if let image = PhotoAlbumDataManager.shared.loadImage(from: record.imagePath) {
            imageView.image = image
        } else if let thumbnail = PhotoAlbumDataManager.shared.loadImage(from: record.thumbnailPath) {
            imageView.image = thumbnail
        } else {
            imageView.image = UIImage(systemName: "photo")
        }
        
        // 显示信息
        setupInfoViews(for: record)
    }
    
    private func setupInfoViews(for record: PhotoRecord) {
        // 清除现有内容
        infoContainerView.subviews.forEach { $0.removeFromSuperview() }
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.translatesAutoresizingMaskIntoConstraints = false
        infoContainerView.addSubview(stackView)
        
        // 时间信息
        let timeView = createInfoRow(
            icon: "calendar",
            title: "拍摄时间",
            content: "\(record.formattedDate) \(record.formattedTime)",
            color: .systemBlue
        )
        stackView.addArrangedSubview(timeView)
        
        // 位置信息
        if let location = record.location {
            let locationView = createInfoRow(
                icon: "location",
                title: "拍摄地点",
                content: location.displayAddress,
                color: .systemGreen
            )
            stackView.addArrangedSubview(locationView)
        }
        
        // 天气信息
        if let weather = record.weather {
            let weatherView = createInfoRow(
                icon: weather.condition.icon,
                title: "天气状况",
                content: weather.displayDescription,
                color: weather.condition.color
            )
            stackView.addArrangedSubview(weatherView)
        }
        
        // 心情信息
        if let mood = record.mood {
            let moodView = createInfoRow(
                icon: nil,
                title: "当时心情",
                content: "\(mood.emoji) \(mood.displayName)",
                color: mood.color
            )
            stackView.addArrangedSubview(moodView)
        }
        
        // 标签信息
        if !record.tags.isEmpty {
            let tagsView = createTagsView(tags: record.tags)
            stackView.addArrangedSubview(tagsView)
        }
        
        // 备注信息
        if let note = record.customNote, !note.isEmpty {
            let noteView = createNoteView(note: note)
            stackView.addArrangedSubview(noteView)
        }
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: infoContainerView.topAnchor, constant: 20),
            stackView.leadingAnchor.constraint(equalTo: infoContainerView.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: infoContainerView.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: infoContainerView.bottomAnchor, constant: -20)
        ])
    }
    
    private func createInfoRow(icon: String?, title: String, content: String, color: UIColor) -> UIView {
        let container = UIView()
        
        let iconImageView = UIImageView()
        if let iconName = icon {
            iconImageView.image = UIImage(systemName: iconName)
        }
        iconImageView.tintColor = color
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .secondaryLabel
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let contentLabel = UILabel()
        contentLabel.text = content
        contentLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        contentLabel.textColor = .label
        contentLabel.numberOfLines = 0
        contentLabel.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(iconImageView)
        container.addSubview(titleLabel)
        container.addSubview(contentLabel)
        
        NSLayoutConstraint.activate([
            iconImageView.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            iconImageView.topAnchor.constraint(equalTo: container.topAnchor),
            iconImageView.widthAnchor.constraint(equalToConstant: 24),
            iconImageView.heightAnchor.constraint(equalToConstant: 24),
            
            titleLabel.leadingAnchor.constraint(equalTo: iconImageView.trailingAnchor, constant: 12),
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            
            contentLabel.leadingAnchor.constraint(equalTo: titleLabel.leadingAnchor),
            contentLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 4),
            contentLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            contentLabel.bottomAnchor.constraint(equalTo: container.bottomAnchor)
        ])
        
        return container
    }
    
    private func createTagsView(tags: [String]) -> UIView {
        let container = UIView()
        
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: "tag")
        iconImageView.tintColor = .systemOrange
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        
        let titleLabel = UILabel()
        titleLabel.text = "标签"
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .secondaryLabel
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let tagsStackView = UIStackView()
        tagsStackView.axis = .horizontal
        tagsStackView.spacing = 8
        tagsStackView.translatesAutoresizingMaskIntoConstraints = false
        
        for tag in tags {
            let tagLabel = UILabel()
            tagLabel.text = tag
            tagLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
            tagLabel.textColor = .white
            tagLabel.backgroundColor = .systemBlue
            tagLabel.layer.cornerRadius = 12
            tagLabel.clipsToBounds = true
            tagLabel.textAlignment = .center
            tagLabel.translatesAutoresizingMaskIntoConstraints = false
            
            NSLayoutConstraint.activate([
                tagLabel.heightAnchor.constraint(equalToConstant: 24),
                tagLabel.widthAnchor.constraint(greaterThanOrEqualToConstant: 40)
            ])
            
            tagsStackView.addArrangedSubview(tagLabel)
        }
        
        container.addSubview(iconImageView)
        container.addSubview(titleLabel)
        container.addSubview(tagsStackView)
        
        NSLayoutConstraint.activate([
            iconImageView.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            iconImageView.topAnchor.constraint(equalTo: container.topAnchor),
            iconImageView.widthAnchor.constraint(equalToConstant: 24),
            iconImageView.heightAnchor.constraint(equalToConstant: 24),
            
            titleLabel.leadingAnchor.constraint(equalTo: iconImageView.trailingAnchor, constant: 12),
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            
            tagsStackView.leadingAnchor.constraint(equalTo: titleLabel.leadingAnchor),
            tagsStackView.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 8),
            tagsStackView.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            tagsStackView.bottomAnchor.constraint(equalTo: container.bottomAnchor)
        ])
        
        return container
    }
    
    private func createNoteView(note: String) -> UIView {
        let container = UIView()
        container.backgroundColor = .systemYellow.withAlphaComponent(0.1)
        container.layer.cornerRadius = 8
        
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: "note.text")
        iconImageView.tintColor = .systemYellow
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        
        let titleLabel = UILabel()
        titleLabel.text = "备注"
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .secondaryLabel
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let noteLabel = UILabel()
        noteLabel.text = note
        noteLabel.font = UIFont.systemFont(ofSize: 16)
        noteLabel.textColor = .label
        noteLabel.numberOfLines = 0
        noteLabel.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(iconImageView)
        container.addSubview(titleLabel)
        container.addSubview(noteLabel)
        
        NSLayoutConstraint.activate([
            iconImageView.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 15),
            iconImageView.topAnchor.constraint(equalTo: container.topAnchor, constant: 15),
            iconImageView.widthAnchor.constraint(equalToConstant: 24),
            iconImageView.heightAnchor.constraint(equalToConstant: 24),
            
            titleLabel.leadingAnchor.constraint(equalTo: iconImageView.trailingAnchor, constant: 12),
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor, constant: 15),
            titleLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -15),
            
            noteLabel.leadingAnchor.constraint(equalTo: titleLabel.leadingAnchor),
            noteLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 8),
            noteLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -15),
            noteLabel.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -15)
        ])
        
        return container
    }
    
    // MARK: - Actions
    @objc private func imageViewTapped() {
        // 全屏查看图片
        guard let record = photoRecord,
              let image = PhotoAlbumDataManager.shared.loadImage(from: record.imagePath) else { return }
        
        let fullScreenVC = FullScreenImageViewController()
        fullScreenVC.image = image
        fullScreenVC.modalPresentationStyle = .fullScreen
        present(fullScreenVC, animated: true)
    }
    
    @objc private func shareButtonTapped() {
        guard let record = photoRecord else { return }
        
        var shareItems: [Any] = []
        
        // 添加图片
        if let image = PhotoAlbumDataManager.shared.loadImage(from: record.imagePath) {
            // 添加水印
            let watermarkedImage = PhotoAlbumDataManager.shared.addWatermark(to: image, record: record)
            shareItems.append(watermarkedImage)
        }
        
        // 添加文本
        var shareText = "📸 时光相册分享\n\n"
        shareText += "📅 \(record.formattedDate) \(record.formattedTime)\n"
        
        if let location = record.location {
            shareText += "📍 \(location.displayAddress)\n"
        }
        
        if let weather = record.weather {
            shareText += "🌤 \(weather.displayDescription)\n"
        }
        
        if let mood = record.mood {
            shareText += "\(mood.emoji) \(mood.displayName)\n"
        }
        
        if let note = record.customNote, !note.isEmpty {
            shareText += "\n💭 \(note)\n"
        }
        
        shareText += "\n#环球足迹 #时光相册"
        shareItems.append(shareText)
        
        let activityVC = UIActivityViewController(activityItems: shareItems, applicationActivities: nil)
        activityVC.popoverPresentationController?.barButtonItem = navigationItem.rightBarButtonItem
        present(activityVC, animated: true)
    }
}

// MARK: - Full Screen Image View Controller
class FullScreenImageViewController: UIViewController {
    
    var image: UIImage?
    
    private let scrollView = UIScrollView()
    private let imageView = UIImageView()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        displayImage()
    }
    
    private func setupUI() {
        view.backgroundColor = .black
        
        // 添加关闭手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissTapped))
        view.addGestureRecognizer(tapGesture)
        
        scrollView.delegate = self
        scrollView.minimumZoomScale = 1.0
        scrollView.maximumZoomScale = 3.0
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)
        
        imageView.contentMode = .scaleAspectFit
        imageView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(imageView)
        
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: view.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            imageView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            imageView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            imageView.heightAnchor.constraint(equalTo: scrollView.heightAnchor)
        ])
    }
    
    private func displayImage() {
        imageView.image = image
    }
    
    @objc private func dismissTapped() {
        dismiss(animated: true)
    }
}

// MARK: - UIScrollViewDelegate
extension FullScreenImageViewController: UIScrollViewDelegate {
    func viewForZooming(in scrollView: UIScrollView) -> UIView? {
        return imageView
    }
} 