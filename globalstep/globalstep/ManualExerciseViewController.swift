import UIKit

class ManualExerciseViewController: UIViewController {
    
    // MARK: - Properties
    var exerciseType: ExerciseType = .fitness
    
    private let dataManager = ExerciseDataManager.shared
    private var startTime: Date?
    private var isRecording = false
    private var timer: Timer?
    private var currentDuration: TimeInterval = 0
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let headerView = UIView()
    private let exerciseTypeLabel = UILabel()
    private let exerciseIconImageView = UIImageView()
    
    private let timerView = UIView()
    private let timerLabel = UILabel()
    
    private let inputContainerView = UIView()
    private let distanceInputView = UIView()
    private let caloriesInputView = UIView()
    private let notesInputView = UIView()
    
    private let distanceTextField = UITextField()
    private let caloriesTextField = UITextField()
    private let notesTextView = UITextView()
    
    private let controlButtonsStackView = UIStackView()
    private let startStopButton = UIButton(type: .system)
    private let saveButton = UIButton(type: .system)
    private let cancelButton = UIButton(type: .system)
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupKeyboardHandling()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        stopTimer()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = exerciseType.displayName
        view.backgroundColor = .systemBackground
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelTapped)
        )
        
        setupScrollView()
        setupHeader()
        setupTimer()
        setupInputContainer()
        setupControlButtons()
        setupConstraints()
        
        updateUI()
    }
    
    private func setupScrollView() {
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
    }
    
    private func setupHeader() {
        headerView.backgroundColor = exerciseType.color
        headerView.layer.cornerRadius = 12
        headerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(headerView)
        
        exerciseIconImageView.image = UIImage(systemName: exerciseType.icon)
        exerciseIconImageView.tintColor = .white
        exerciseIconImageView.contentMode = .scaleAspectFit
        exerciseIconImageView.translatesAutoresizingMaskIntoConstraints = false
        headerView.addSubview(exerciseIconImageView)
        
        exerciseTypeLabel.text = exerciseType.displayName
        exerciseTypeLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        exerciseTypeLabel.textColor = .white
        exerciseTypeLabel.textAlignment = .center
        exerciseTypeLabel.translatesAutoresizingMaskIntoConstraints = false
        headerView.addSubview(exerciseTypeLabel)
    }
    
    private func setupTimer() {
        timerView.backgroundColor = .secondarySystemBackground
        timerView.layer.cornerRadius = 12
        timerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(timerView)
        
        let timerTitleLabel = UILabel()
        timerTitleLabel.text = "运动时长"
        timerTitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        timerTitleLabel.textColor = .secondaryLabel
        timerTitleLabel.textAlignment = .center
        timerTitleLabel.translatesAutoresizingMaskIntoConstraints = false
        timerView.addSubview(timerTitleLabel)
        
        timerLabel.text = "00:00"
        timerLabel.font = UIFont.monospacedDigitSystemFont(ofSize: 48, weight: .bold)
        timerLabel.textColor = exerciseType.color
        timerLabel.textAlignment = .center
        timerLabel.translatesAutoresizingMaskIntoConstraints = false
        timerView.addSubview(timerLabel)
        
        NSLayoutConstraint.activate([
            timerTitleLabel.topAnchor.constraint(equalTo: timerView.topAnchor, constant: 20),
            timerTitleLabel.leadingAnchor.constraint(equalTo: timerView.leadingAnchor, constant: 20),
            timerTitleLabel.trailingAnchor.constraint(equalTo: timerView.trailingAnchor, constant: -20),
            
            timerLabel.topAnchor.constraint(equalTo: timerTitleLabel.bottomAnchor, constant: 10),
            timerLabel.leadingAnchor.constraint(equalTo: timerView.leadingAnchor, constant: 20),
            timerLabel.trailingAnchor.constraint(equalTo: timerView.trailingAnchor, constant: -20),
            timerLabel.bottomAnchor.constraint(equalTo: timerView.bottomAnchor, constant: -20)
        ])
    }
    
    private func setupInputContainer() {
        inputContainerView.backgroundColor = .secondarySystemBackground
        inputContainerView.layer.cornerRadius = 12
        inputContainerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(inputContainerView)
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 20
        stackView.translatesAutoresizingMaskIntoConstraints = false
        inputContainerView.addSubview(stackView)
        
        // 距离输入（仅对某些运动类型显示）
        if exerciseType == .swimming {
            setupDistanceInput()
            stackView.addArrangedSubview(distanceInputView)
        }
        
        // 卡路里输入
        setupCaloriesInput()
        stackView.addArrangedSubview(caloriesInputView)
        
        // 备注输入
        setupNotesInput()
        stackView.addArrangedSubview(notesInputView)
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: inputContainerView.topAnchor, constant: 20),
            stackView.leadingAnchor.constraint(equalTo: inputContainerView.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: inputContainerView.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: inputContainerView.bottomAnchor, constant: -20)
        ])
    }
    
    private func setupDistanceInput() {
        distanceInputView.translatesAutoresizingMaskIntoConstraints = false
        
        let titleLabel = UILabel()
        titleLabel.text = "距离 (\(exerciseType.unit))"
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        distanceInputView.addSubview(titleLabel)
        
        distanceTextField.placeholder = "输入距离"
        distanceTextField.keyboardType = .decimalPad
        distanceTextField.borderStyle = .roundedRect
        distanceTextField.translatesAutoresizingMaskIntoConstraints = false
        distanceInputView.addSubview(distanceTextField)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: distanceInputView.topAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: distanceInputView.leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: distanceInputView.trailingAnchor),
            
            distanceTextField.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 8),
            distanceTextField.leadingAnchor.constraint(equalTo: distanceInputView.leadingAnchor),
            distanceTextField.trailingAnchor.constraint(equalTo: distanceInputView.trailingAnchor),
            distanceTextField.bottomAnchor.constraint(equalTo: distanceInputView.bottomAnchor),
            distanceTextField.heightAnchor.constraint(equalToConstant: 44)
        ])
    }
    
    private func setupCaloriesInput() {
        caloriesInputView.translatesAutoresizingMaskIntoConstraints = false
        
        let titleLabel = UILabel()
        titleLabel.text = "卡路里 (可选)"
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        caloriesInputView.addSubview(titleLabel)
        
        caloriesTextField.placeholder = "系统将自动计算"
        caloriesTextField.keyboardType = .numberPad
        caloriesTextField.borderStyle = .roundedRect
        caloriesTextField.translatesAutoresizingMaskIntoConstraints = false
        caloriesInputView.addSubview(caloriesTextField)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: caloriesInputView.topAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: caloriesInputView.leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: caloriesInputView.trailingAnchor),
            
            caloriesTextField.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 8),
            caloriesTextField.leadingAnchor.constraint(equalTo: caloriesInputView.leadingAnchor),
            caloriesTextField.trailingAnchor.constraint(equalTo: caloriesInputView.trailingAnchor),
            caloriesTextField.bottomAnchor.constraint(equalTo: caloriesInputView.bottomAnchor),
            caloriesTextField.heightAnchor.constraint(equalToConstant: 44)
        ])
    }
    
    private func setupNotesInput() {
        notesInputView.translatesAutoresizingMaskIntoConstraints = false
        
        let titleLabel = UILabel()
        titleLabel.text = "备注 (可选)"
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        notesInputView.addSubview(titleLabel)
        
        notesTextView.font = UIFont.systemFont(ofSize: 16)
        notesTextView.layer.borderColor = UIColor.systemGray4.cgColor
        notesTextView.layer.borderWidth = 1
        notesTextView.layer.cornerRadius = 8
        notesTextView.translatesAutoresizingMaskIntoConstraints = false
        notesInputView.addSubview(notesTextView)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: notesInputView.topAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: notesInputView.leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: notesInputView.trailingAnchor),
            
            notesTextView.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 8),
            notesTextView.leadingAnchor.constraint(equalTo: notesInputView.leadingAnchor),
            notesTextView.trailingAnchor.constraint(equalTo: notesInputView.trailingAnchor),
            notesTextView.bottomAnchor.constraint(equalTo: notesInputView.bottomAnchor),
            notesTextView.heightAnchor.constraint(equalToConstant: 80)
        ])
    }
    
    private func setupControlButtons() {
        controlButtonsStackView.axis = .horizontal
        controlButtonsStackView.distribution = .fillEqually
        controlButtonsStackView.spacing = 15
        controlButtonsStackView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(controlButtonsStackView)
        
        // 开始/停止按钮
        startStopButton.setTitle("开始", for: .normal)
        startStopButton.backgroundColor = .systemGreen
        startStopButton.setTitleColor(.white, for: .normal)
        startStopButton.layer.cornerRadius = 25
        startStopButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        startStopButton.addTarget(self, action: #selector(startStopButtonTapped), for: .touchUpInside)
        
        // 保存按钮
        saveButton.setTitle("保存", for: .normal)
        saveButton.backgroundColor = .systemBlue
        saveButton.setTitleColor(.white, for: .normal)
        saveButton.layer.cornerRadius = 25
        saveButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        saveButton.isEnabled = false
        saveButton.alpha = 0.5
        saveButton.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)
        
        // 取消按钮
        cancelButton.setTitle("取消", for: .normal)
        cancelButton.backgroundColor = .systemGray
        cancelButton.setTitleColor(.white, for: .normal)
        cancelButton.layer.cornerRadius = 25
        cancelButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        cancelButton.addTarget(self, action: #selector(cancelTapped), for: .touchUpInside)
        
        controlButtonsStackView.addArrangedSubview(cancelButton)
        controlButtonsStackView.addArrangedSubview(startStopButton)
        controlButtonsStackView.addArrangedSubview(saveButton)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 滚动视图
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            // 头部视图
            headerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            headerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            headerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            headerView.heightAnchor.constraint(equalToConstant: 120),
            
            exerciseIconImageView.leadingAnchor.constraint(equalTo: headerView.leadingAnchor, constant: 30),
            exerciseIconImageView.centerYAnchor.constraint(equalTo: headerView.centerYAnchor),
            exerciseIconImageView.widthAnchor.constraint(equalToConstant: 50),
            exerciseIconImageView.heightAnchor.constraint(equalToConstant: 50),
            
            exerciseTypeLabel.leadingAnchor.constraint(equalTo: exerciseIconImageView.trailingAnchor, constant: 20),
            exerciseTypeLabel.centerYAnchor.constraint(equalTo: headerView.centerYAnchor),
            exerciseTypeLabel.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -30),
            
            // 计时器视图
            timerView.topAnchor.constraint(equalTo: headerView.bottomAnchor, constant: 20),
            timerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            timerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            
            // 输入容器
            inputContainerView.topAnchor.constraint(equalTo: timerView.bottomAnchor, constant: 20),
            inputContainerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            inputContainerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            
            // 控制按钮
            controlButtonsStackView.topAnchor.constraint(equalTo: inputContainerView.bottomAnchor, constant: 30),
            controlButtonsStackView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 40),
            controlButtonsStackView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -40),
            controlButtonsStackView.heightAnchor.constraint(equalToConstant: 50),
            controlButtonsStackView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -30)
        ])
    }
    
    // MARK: - Keyboard Handling
    private func setupKeyboardHandling() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillShow),
            name: UIResponder.keyboardWillShowNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillHide),
            name: UIResponder.keyboardWillHideNotification,
            object: nil
        )
        
        // 添加点击手势关闭键盘
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        view.addGestureRecognizer(tapGesture)
    }
    
    @objc private func keyboardWillShow(notification: NSNotification) {
        guard let keyboardSize = (notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue)?.cgRectValue else { return }
        
        let contentInsets = UIEdgeInsets(top: 0, left: 0, bottom: keyboardSize.height, right: 0)
        scrollView.contentInset = contentInsets
        scrollView.scrollIndicatorInsets = contentInsets
    }
    
    @objc private func keyboardWillHide(notification: NSNotification) {
        scrollView.contentInset = .zero
        scrollView.scrollIndicatorInsets = .zero
    }
    
    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }
    
    // MARK: - Timer Control
    private func startTimer() {
        guard !isRecording else { return }
        
        isRecording = true
        startTime = Date()
        currentDuration = 0
        
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateTimer()
        }
        
        updateUI()
    }
    
    private func stopTimer() {
        isRecording = false
        timer?.invalidate()
        timer = nil
        
        updateUI()
    }
    
    private func updateTimer() {
        guard let startTime = startTime else { return }
        currentDuration = Date().timeIntervalSince(startTime)
        updateTimerDisplay()
    }
    
    private func updateTimerDisplay() {
        let hours = Int(currentDuration) / 3600
        let minutes = Int(currentDuration) % 3600 / 60
        let seconds = Int(currentDuration) % 60
        
        if hours > 0 {
            timerLabel.text = String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            timerLabel.text = String(format: "%02d:%02d", minutes, seconds)
        }
    }
    
    // MARK: - UI Updates
    private func updateUI() {
        if isRecording {
            startStopButton.setTitle("停止", for: .normal)
            startStopButton.backgroundColor = .systemRed
            saveButton.isEnabled = false
            saveButton.alpha = 0.5
        } else {
            if currentDuration > 0 {
                startStopButton.setTitle("重新开始", for: .normal)
                startStopButton.backgroundColor = .systemGreen
                saveButton.isEnabled = true
                saveButton.alpha = 1.0
            } else {
                startStopButton.setTitle("开始", for: .normal)
                startStopButton.backgroundColor = .systemGreen
                saveButton.isEnabled = false
                saveButton.alpha = 0.5
            }
        }
    }
    
    // MARK: - Save Exercise Record
    private func saveExerciseRecord() {
        guard let startTime = startTime, currentDuration > 0 else { return }
        
        let endTime = Date()
        
        // 获取距离（仅对游泳等运动）
        var distance: Double = 0
        if exerciseType == .swimming, let distanceText = distanceTextField.text, !distanceText.isEmpty {
            distance = Double(distanceText) ?? 0
        }
        
        // 获取卡路里（用户输入或自动计算）
        var calories: Double = 0
        if let caloriesText = caloriesTextField.text, !caloriesText.isEmpty {
            calories = Double(caloriesText) ?? 0
        } else {
            calories = dataManager.calculateCalories(
                for: exerciseType,
                duration: currentDuration,
                distance: distance
            )
        }
        
        // 获取备注
        let notes = notesTextView.text?.isEmpty == false ? notesTextView.text : nil
        
        let record = ExerciseRecord(
            id: UUID().uuidString,
            type: exerciseType,
            startTime: startTime,
            endTime: endTime,
            duration: currentDuration,
            distance: distance,
            calories: calories,
            averageSpeed: 0, // 手动运动不计算速度
            maxSpeed: 0,
            trajectory: [], // 手动运动无轨迹
            notes: notes,
            weather: nil,
            temperature: nil,
            heartRate: nil
        )
        
        dataManager.addExerciseRecord(record)
        
        // 显示完成界面
        showCompletionScreen(record: record)
    }
    
    private func showCompletionScreen(record: ExerciseRecord) {
        let completionVC = ExerciseCompletionViewController()
        completionVC.exerciseRecord = record
        completionVC.modalPresentationStyle = .fullScreen
        present(completionVC, animated: true)
    }
    
    // MARK: - Actions
    @objc private func startStopButtonTapped() {
        if isRecording {
            stopTimer()
        } else {
            startTimer()
        }
    }
    
    @objc private func saveButtonTapped() {
        guard currentDuration > 0 else { return }
        
        let alert = UIAlertController(
            title: "保存运动记录",
            message: "确定要保存这次运动记录吗？",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "保存", style: .default) { [weak self] _ in
            self?.saveExerciseRecord()
        })
        
        present(alert, animated: true)
    }
    
    @objc private func cancelTapped() {
        if isRecording || currentDuration > 0 {
            let alert = UIAlertController(
                title: "取消运动",
                message: "确定要取消吗？未保存的数据将丢失。",
                preferredStyle: .alert
            )
            
            alert.addAction(UIAlertAction(title: "继续运动", style: .cancel))
            alert.addAction(UIAlertAction(title: "取消运动", style: .destructive) { [weak self] _ in
                self?.stopTimer()
                self?.dismiss(animated: true)
            })
            
            present(alert, animated: true)
        } else {
            dismiss(animated: true)
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
} 