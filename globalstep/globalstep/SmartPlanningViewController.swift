//
//  SmartPlanningViewController.swift
//  globalstep
//
//  Created by AI Assistant on 2024/12/19.
//

import UIKit

class SmartPlanningViewController: UIViewController {
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    // Input fields
    private let departureButton = UIButton(type: .system)
    private let destinationButton = UIButton(type: .system)
    private let daysButton = UIButton(type: .system)
    
    // Travel type collection view
    private let travelTypeLabel = UILabel()
    private let travelTypeCollectionView: UICollectionView
    private let planTripButton = UIButton(type: .system)

    
    // Data
    private var selectedDepartureCity: String = "济南市"
    private var selectedDestinationCity: String?
    private var selectedDays: String?
    private var isSelectingDeparture: Bool = false
    
    // Data
    private let travelTypes = [
        TravelType(title: "亲子", imageName: "family", isSelected: false),
        TravelType(title: "情侣", imageName: "couple", isSelected: false),
        TravelType(title: "好友", imageName: "friends", isSelected: false),
        TravelType(title: "独自", imageName: "solo", isSelected: false)
    ]
    
    private var selectedTravelTypeIndex: Int?
    
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        // Setup collection view layout
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumInteritemSpacing = 16
        layout.minimumLineSpacing = 16
        
        travelTypeCollectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private var backgroundGradientLayer: CAGradientLayer?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        configureNavigationBar()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // Update background gradient frame
        backgroundGradientLayer?.frame = view.bounds
        
        // Update button gradient frame
        for layer in planTripButton.layer.sublayers ?? [] {
            if layer is CAGradientLayer {
                layer.frame = planTripButton.bounds
            }
        }
    }
    
    private func setupGradientBackground() {
        // Remove existing gradient if any
        backgroundGradientLayer?.removeFromSuperlayer()
        
        // Create new gradient background
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.systemYellow.withAlphaComponent(0.8).cgColor,
            UIColor.systemGreen.withAlphaComponent(0.6).cgColor,
            UIColor.systemBlue.withAlphaComponent(0.4).cgColor,
            UIColor.systemCyan.withAlphaComponent(0.3).cgColor
        ]
        gradientLayer.locations = [0.0, 0.3, 0.7, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        
        // Set initial frame
        gradientLayer.frame = view.bounds
        
        // Insert at the bottom of the layer hierarchy
        view.layer.insertSublayer(gradientLayer, at: 0)
        
        // Store reference for later updates
        backgroundGradientLayer = gradientLayer
    }
    
    private func configureNavigationBar() {
        // Create gradient background immediately
        setupGradientBackground()
        
        // Remove default navigation bar
        navigationController?.setNavigationBarHidden(true, animated: false)
        
        // Add custom close button
        let closeButton = UIButton(type: .system)
        closeButton.setImage(UIImage(systemName: "xmark"), for: .normal)
        closeButton.tintColor = .label
        closeButton.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
        closeButton.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(closeButton)
        
        NSLayoutConstraint.activate([
            closeButton.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 16),
            closeButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            closeButton.widthAnchor.constraint(equalToConstant: 24),
            closeButton.heightAnchor.constraint(equalToConstant: 24)
        ])
        
        // Add title
        let titleLabel = UILabel()
        titleLabel.text = "智能行程规划"
        titleLabel.font = UIFont.boldSystemFont(ofSize: 20)
        titleLabel.textColor = .label
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(titleLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: closeButton.centerYAnchor)
        ])
    }
    
    private func setupUI() {
        // Setup scroll view
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        // Setup input buttons
        setupInputButtons()
        
        // Setup travel type section
        setupTravelTypeSection()
        
        // Setup plan trip button
        setupPlanTripButton()
        
        // Setup AI recommendation button

        
        // Setup travel type label
        travelTypeLabel.text = "旅行类型"
        travelTypeLabel.font = UIFont.boldSystemFont(ofSize: 18)
        travelTypeLabel.textColor = UIColor.label
        travelTypeLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // Add to content view
        contentView.addSubview(departureButton)
        contentView.addSubview(destinationButton)
        contentView.addSubview(daysButton)
        contentView.addSubview(travelTypeLabel)
        contentView.addSubview(travelTypeCollectionView)
        contentView.addSubview(planTripButton)

    }
    
    private func setupInputButtons() {
        // Departure button
        departureButton.translatesAutoresizingMaskIntoConstraints = false
        departureButton.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.95)
        departureButton.layer.cornerRadius = 30
        departureButton.layer.shadowColor = UIColor.black.cgColor
        departureButton.layer.shadowOffset = CGSize(width: 0, height: 4)
        departureButton.layer.shadowRadius = 12
        departureButton.layer.shadowOpacity = 0.15
        departureButton.layer.borderWidth = 0.5
        departureButton.layer.borderColor = UIColor.systemGray4.cgColor
        departureButton.contentHorizontalAlignment = .left
        if #available(iOS 15.0, *) {
            var config = UIButton.Configuration.plain()
            config.contentInsets = NSDirectionalEdgeInsets(top: 0, leading: 24, bottom: 0, trailing: 24)
            departureButton.configuration = config
        } else {
            departureButton.contentEdgeInsets = UIEdgeInsets(top: 0, left: 24, bottom: 0, right: 24)
        }
        departureButton.addTarget(self, action: #selector(departureTapped), for: .touchUpInside)
        
        updateDepartureButtonTitle()
        
        // Destination button
        destinationButton.translatesAutoresizingMaskIntoConstraints = false
        destinationButton.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.95)
        destinationButton.layer.cornerRadius = 30
        destinationButton.layer.shadowColor = UIColor.black.cgColor
        destinationButton.layer.shadowOffset = CGSize(width: 0, height: 4)
        destinationButton.layer.shadowRadius = 12
        destinationButton.layer.shadowOpacity = 0.15
        destinationButton.layer.borderWidth = 0.5
        destinationButton.layer.borderColor = UIColor.systemGray4.cgColor
        destinationButton.contentHorizontalAlignment = .left
        if #available(iOS 15.0, *) {
            var config = UIButton.Configuration.plain()
            config.contentInsets = NSDirectionalEdgeInsets(top: 0, leading: 24, bottom: 0, trailing: 24)
            destinationButton.configuration = config
        } else {
            destinationButton.contentEdgeInsets = UIEdgeInsets(top: 0, left: 24, bottom: 0, right: 24)
        }
        destinationButton.addTarget(self, action: #selector(destinationTapped), for: .touchUpInside)
        
        updateDestinationButtonTitle()
        
        // Days button
        daysButton.translatesAutoresizingMaskIntoConstraints = false
        daysButton.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.95)
        daysButton.layer.cornerRadius = 30
        daysButton.layer.shadowColor = UIColor.black.cgColor
        daysButton.layer.shadowOffset = CGSize(width: 0, height: 4)
        daysButton.layer.shadowRadius = 12
        daysButton.layer.shadowOpacity = 0.15
        daysButton.layer.borderWidth = 0.5
        daysButton.layer.borderColor = UIColor.systemGray4.cgColor
        daysButton.contentHorizontalAlignment = .left
        if #available(iOS 15.0, *) {
            var config = UIButton.Configuration.plain()
            config.contentInsets = NSDirectionalEdgeInsets(top: 0, leading: 24, bottom: 0, trailing: 24)
            daysButton.configuration = config
        } else {
            daysButton.contentEdgeInsets = UIEdgeInsets(top: 0, left: 24, bottom: 0, right: 24)
        }
        daysButton.addTarget(self, action: #selector(daysTapped), for: .touchUpInside)
        
        updateDaysButtonTitle()
    }
    
    private func setupTravelTypeSection() {
        // Collection view setup
        travelTypeCollectionView.translatesAutoresizingMaskIntoConstraints = false
        travelTypeCollectionView.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.9)
        travelTypeCollectionView.layer.cornerRadius = 20
        travelTypeCollectionView.layer.shadowColor = UIColor.black.cgColor
        travelTypeCollectionView.layer.shadowOffset = CGSize(width: 0, height: 4)
        travelTypeCollectionView.layer.shadowRadius = 12
        travelTypeCollectionView.layer.shadowOpacity = 0.1
        travelTypeCollectionView.delegate = self
        travelTypeCollectionView.dataSource = self
        travelTypeCollectionView.register(TravelTypeCell.self, forCellWithReuseIdentifier: "TravelTypeCell")
        travelTypeCollectionView.showsHorizontalScrollIndicator = false
        travelTypeCollectionView.contentInset = UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16)
    }
    
    private func setupPlanTripButton() {
        planTripButton.translatesAutoresizingMaskIntoConstraints = false
        planTripButton.setTitle("🤖 AI智能规划行程", for: .normal)
        planTripButton.setTitleColor(.white, for: .normal)
        planTripButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 18)
        planTripButton.layer.cornerRadius = 28
        planTripButton.addTarget(self, action: #selector(planTripTapped), for: .touchUpInside)
        
        // Add gradient background
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.black.cgColor,
            UIColor.systemGray.cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.cornerRadius = 28
        planTripButton.layer.insertSublayer(gradientLayer, at: 0)
        
        // Add shadow
        planTripButton.layer.shadowColor = UIColor.black.cgColor
        planTripButton.layer.shadowOffset = CGSize(width: 0, height: 6)
        planTripButton.layer.shadowRadius = 12
        planTripButton.layer.shadowOpacity = 0.3
        
        // Store gradient for frame updates
        DispatchQueue.main.async {
            gradientLayer.frame = self.planTripButton.bounds
        }
    }
    
    // AI功能已集成到主规划流程中，不再需要单独的按钮
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // Scroll view
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 80),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            // Content view
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            // Input buttons
            departureButton.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 60),
            departureButton.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 30),
            departureButton.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -30),
            departureButton.heightAnchor.constraint(equalToConstant: 60),
            
            destinationButton.topAnchor.constraint(equalTo: departureButton.bottomAnchor, constant: 24),
            destinationButton.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 30),
            destinationButton.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -30),
            destinationButton.heightAnchor.constraint(equalToConstant: 60),
            
            daysButton.topAnchor.constraint(equalTo: destinationButton.bottomAnchor, constant: 24),
            daysButton.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 30),
            daysButton.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -30),
            daysButton.heightAnchor.constraint(equalToConstant: 60),
            
            // Travel type label
            travelTypeLabel.topAnchor.constraint(equalTo: daysButton.bottomAnchor, constant: 50),
            travelTypeLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 30),
            
            // Travel type collection view
            travelTypeCollectionView.topAnchor.constraint(equalTo: travelTypeLabel.bottomAnchor, constant: 20),
            travelTypeCollectionView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 30),
            travelTypeCollectionView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -30),
            travelTypeCollectionView.heightAnchor.constraint(equalToConstant: 160),
            
            // Plan trip button - 现在包含AI智能规划
            planTripButton.topAnchor.constraint(equalTo: travelTypeCollectionView.bottomAnchor, constant: 60),
            planTripButton.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 30),
            planTripButton.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -30),
            planTripButton.heightAnchor.constraint(equalToConstant: 55),
            planTripButton.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -50)
        ])
    }
    
    // MARK: - Actions
    @objc private func closeTapped() {
        dismiss(animated: true, completion: nil)
    }
    
    // MARK: - Helper Methods
    private func updateDepartureButtonTitle() {
        let departureTitle = NSMutableAttributedString()
        departureTitle.append(NSAttributedString(string: "出发地", attributes: [
            .foregroundColor: UIColor.label,
            .font: UIFont.systemFont(ofSize: 16)
        ]))
        let spacingCount = max(0, 25 - selectedDepartureCity.count * 2)
        let spacing = String(repeating: " ", count: spacingCount)
        departureTitle.append(NSAttributedString(string: "\(spacing)\(selectedDepartureCity) ", attributes: [
            .foregroundColor: UIColor.secondaryLabel,
            .font: UIFont.systemFont(ofSize: 16)
        ]))
        departureTitle.append(NSAttributedString(string: "⌄", attributes: [
            .foregroundColor: UIColor.systemGray,
            .font: UIFont.systemFont(ofSize: 16)
        ]))
        departureButton.setAttributedTitle(departureTitle, for: .normal)
    }
    
    private func updateDestinationButtonTitle() {
        let destinationTitle = NSMutableAttributedString()
        destinationTitle.append(NSAttributedString(string: "目的地", attributes: [
            .foregroundColor: UIColor.label,
            .font: UIFont.systemFont(ofSize: 16)
        ]))
        let displayText = selectedDestinationCity ?? "选中地点"
        let spacingCount = max(0, 25 - displayText.count * 2)
        let spacing = String(repeating: " ", count: spacingCount)
        destinationTitle.append(NSAttributedString(string: "\(spacing)\(displayText) ", attributes: [
            .foregroundColor: selectedDestinationCity != nil ? UIColor.secondaryLabel : UIColor.systemGray,
            .font: UIFont.systemFont(ofSize: 16)
        ]))
        destinationTitle.append(NSAttributedString(string: "⌄", attributes: [
            .foregroundColor: UIColor.systemGray,
            .font: UIFont.systemFont(ofSize: 16)
        ]))
        destinationButton.setAttributedTitle(destinationTitle, for: .normal)
    }
    
    private func updateDaysButtonTitle() {
        let daysTitle = NSMutableAttributedString()
        daysTitle.append(NSAttributedString(string: "旅行天数", attributes: [
            .foregroundColor: UIColor.label,
            .font: UIFont.systemFont(ofSize: 16)
        ]))
        let displayText = selectedDays ?? "选择天数"
        let spacingCount = max(0, 22 - displayText.count * 2)
        let spacing = String(repeating: " ", count: spacingCount)
        daysTitle.append(NSAttributedString(string: "\(spacing)\(displayText) ", attributes: [
            .foregroundColor: selectedDays != nil ? UIColor.secondaryLabel : UIColor.systemGray,
            .font: UIFont.systemFont(ofSize: 16)
        ]))
        daysTitle.append(NSAttributedString(string: "⌄", attributes: [
            .foregroundColor: UIColor.systemGray,
            .font: UIFont.systemFont(ofSize: 16)
        ]))
        daysButton.setAttributedTitle(daysTitle, for: .normal)
    }
    
    @objc private func departureTapped() {
        isSelectingDeparture = true
        let cityPicker = CityPickerViewController()
        cityPicker.navigationItem.title = "选择出发地"
        cityPicker.delegate = self
        navigationController?.pushViewController(cityPicker, animated: true)
    }
    
    @objc private func destinationTapped() {
        isSelectingDeparture = false
        let cityPicker = CityPickerViewController()
        cityPicker.navigationItem.title = "选择目的地"
        cityPicker.delegate = self
        navigationController?.pushViewController(cityPicker, animated: true)
    }
    
    @objc private func daysTapped() {
        let alert = UIAlertController(title: "选择旅行天数", message: nil, preferredStyle: .actionSheet)
        
        let dayOptions = ["1天", "2天", "3天", "4天", "5天", "6天", "7天", "8天", "9天", "10天"]
        
        for day in dayOptions {
            alert.addAction(UIAlertAction(title: day, style: .default) { [weak self] _ in
                self?.selectedDays = day
                self?.updateDaysButtonTitle()
            })
        }
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        // For iPad
        if let popover = alert.popoverPresentationController {
            popover.sourceView = daysButton
            popover.sourceRect = daysButton.bounds
        }
        
        present(alert, animated: true)
    }
    
    @objc private func planTripTapped() {
        guard selectedDestinationCity != nil, selectedDays != nil, selectedTravelTypeIndex != nil else {
            let alert = UIAlertController(title: "提示", message: "请完善旅行信息", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
            return
        }
        
        // Navigate to travel preferences
        let preferencesVC = TravelPreferencesViewController()
        preferencesVC.delegate = self
        let navController = UINavigationController(rootViewController: preferencesVC)
        present(navController, animated: true)
    }
    
    // AI功能已集成到主规划流程中
    
    private func extractDayCount(from daysString: String) -> Int {
        return Int(daysString.replacingOccurrences(of: "天", with: "")) ?? 3
    }
}

// MARK: - UICollectionViewDataSource, UICollectionViewDelegate
extension SmartPlanningViewController: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return travelTypes.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "TravelTypeCell", for: indexPath) as! TravelTypeCell
        var travelType = travelTypes[indexPath.item]
        travelType.isSelected = selectedTravelTypeIndex == indexPath.item
        cell.configure(with: travelType)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = (collectionView.frame.width - 48) / 4 // 4 items with spacing
        let maxHeight = collectionView.frame.height - 32 // Leave space for insets
        let height = min(120, maxHeight) // Reduce height to avoid layout conflicts
        return CGSize(width: width, height: height)
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        selectedTravelTypeIndex = indexPath.item
        collectionView.reloadData()
    }
}

// MARK: - CityPickerDelegate
extension SmartPlanningViewController: CityPickerDelegate {
    func didSelectCity(_ city: String) {
        if isSelectingDeparture {
            selectedDepartureCity = city
            updateDepartureButtonTitle()
        } else {
            selectedDestinationCity = city
            updateDestinationButtonTitle()
        }
    }
}

// MARK: - TravelPreferencesDelegate
extension SmartPlanningViewController: TravelPreferencesDelegate {
    func didCompletePreferences(_ preferences: TravelPreferences) {
        print("🎯 开始AI智能规划流程")
        print("📍 出发地: \(selectedDepartureCity)")
        print("📍 目的地: \(selectedDestinationCity!)")
        print("📅 天数: \(selectedDays!)")
        print("👥 旅行类型: \(selectedTravelTypeIndex != nil ? travelTypes[selectedTravelTypeIndex!].title : "未选择")")
        print("🎨 兴趣偏好: \(preferences.interests)")
        print("🍽️ 美食偏好: \(preferences.foods)")
        print("🏨 住宿偏好: \(preferences.accommodations)")
        
        // 创建AI增强的行程规划页面，显示加载界面
        let resultVC = TripPlanResultViewController()
        resultVC.departureCity = selectedDepartureCity
        resultVC.destinationCity = selectedDestinationCity!
        resultVC.days = selectedDays!
        resultVC.travelPreferences = preferences
        
        // 添加旅行类型信息
        if let selectedIndex = selectedTravelTypeIndex {
            resultVC.travelType = travelTypes[selectedIndex].title
        }
        
        // 导航到结果页面，它会自动开始AI规划
        navigationController?.pushViewController(resultVC, animated: true)
        
        print("✅ 已导航到AI规划结果页面")
    }
}

// MARK: - Models
struct TravelType {
    let title: String
    let imageName: String
    var isSelected: Bool
}

// MARK: - Travel Type Cell
class TravelTypeCell: UICollectionViewCell {
    private let containerView = UIView()
    private let imageView = UIImageView()
    private let titleLabel = UILabel()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        // Container view
        containerView.translatesAutoresizingMaskIntoConstraints = false
        containerView.backgroundColor = .systemBackground
        containerView.layer.cornerRadius = 16
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowRadius = 4
        containerView.layer.shadowOpacity = 0.1
        contentView.addSubview(containerView)
        
        // Image view
        imageView.translatesAutoresizingMaskIntoConstraints = false
        imageView.contentMode = .scaleAspectFit
        imageView.layer.cornerRadius = 12
        imageView.layer.masksToBounds = true
        containerView.addSubview(imageView)
        
        // Title label
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .label
        titleLabel.textAlignment = .center
        containerView.addSubview(titleLabel)
        
        NSLayoutConstraint.activate([
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            imageView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 12),
            imageView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 12),
            imageView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -12),
            imageView.heightAnchor.constraint(equalTo: imageView.widthAnchor),
            
            titleLabel.topAnchor.constraint(equalTo: imageView.bottomAnchor, constant: 8),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 8),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -8),
            titleLabel.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -8)
        ])
    }
    
    func configure(with travelType: TravelType) {
        titleLabel.text = travelType.title
        
        // Create gradient backgrounds and add emoji icons
        switch travelType.title {
        case "亲子":
            createGradientBackground(colors: [UIColor.systemYellow, UIColor.systemOrange])
            addEmojiIcon("👨‍👩‍👧‍👦")
        case "情侣":
            createGradientBackground(colors: [UIColor.systemPink, UIColor.systemRed])
            addEmojiIcon("💑")
        case "好友":
            createGradientBackground(colors: [UIColor.systemGreen, UIColor.systemTeal])
            addEmojiIcon("👫")
        case "独自":
            createGradientBackground(colors: [UIColor.systemBlue, UIColor.systemPurple])
            addEmojiIcon("🧳")
        default:
            createGradientBackground(colors: [UIColor.systemGray, UIColor.systemGray2])
            addEmojiIcon("✈️")
        }
        
        // Update selection state
        if travelType.isSelected {
            containerView.layer.borderWidth = 3
            containerView.layer.borderColor = UIColor.systemBlue.cgColor
            containerView.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
        } else {
            containerView.layer.borderWidth = 0
            containerView.transform = CGAffineTransform.identity
        }
    }
    
    private func createGradientBackground(colors: [UIColor]) {
        // Remove existing gradient layers
        imageView.layer.sublayers?.removeAll(where: { $0 is CAGradientLayer })
        
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = colors.map { $0.withAlphaComponent(0.6).cgColor }
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        gradientLayer.cornerRadius = 12
        imageView.layer.insertSublayer(gradientLayer, at: 0)
        
        // Update frame in layoutSubviews
        DispatchQueue.main.async {
            gradientLayer.frame = self.imageView.bounds
        }
    }
    
    private func addEmojiIcon(_ emoji: String) {
        // Remove existing emoji labels
        imageView.subviews.forEach { $0.removeFromSuperview() }
        
        let emojiLabel = UILabel()
        emojiLabel.text = emoji
        emojiLabel.font = UIFont.systemFont(ofSize: 32)
        emojiLabel.textAlignment = .center
        emojiLabel.translatesAutoresizingMaskIntoConstraints = false
        imageView.addSubview(emojiLabel)
        
        NSLayoutConstraint.activate([
            emojiLabel.centerXAnchor.constraint(equalTo: imageView.centerXAnchor),
            emojiLabel.centerYAnchor.constraint(equalTo: imageView.centerYAnchor)
        ])
    }
} 