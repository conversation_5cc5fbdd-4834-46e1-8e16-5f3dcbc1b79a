import Foundation
import CoreLocation

// MARK: - 地区数据模型
struct Region {
    let id: String
    let name: String
    let type: RegionType
    let coordinates: [CLLocationCoordinate2D] // 地区边界坐标
    let center: CLLocationCoordinate2D
    let isVisited: Bool
    let visitDate: Date?
    let visitSource: VisitSource // 访问来源：照片GPS或手动点亮
}

enum RegionType {
    case country
    case province
    case city
    case district
}

enum VisitSource {
    case photoGPS    // 照片GPS信息（浅绿色）
    case manual      // 手动点亮（浅蓝色）
}

// MARK: - 大洲数据模型
struct Continent {
    let id: String
    let name: String
    let countries: [Country]
    let visitedCount: Int
    let totalCount: Int
    
    var visitPercentage: Double {
        return totalCount > 0 ? Double(visitedCount) / Double(totalCount) * 100 : 0
    }
}

// MARK: - 国家数据模型
struct Country {
    let id: String
    let name: String
    let code: String // 国家代码
    let provinces: [Province]
    let cities: [City]
    let visitedCount: Int
    let totalCount: Int
    let coordinates: [CLLocationCoordinate2D]
    let center: CLLocationCoordinate2D
    
    var visitPercentage: Double {
        return totalCount > 0 ? Double(visitedCount) / Double(totalCount) * 100 : 0
    }
}

// MARK: - 省份数据模型
struct Province {
    let id: String
    let name: String
    let countryId: String
    let cities: [City]
    let visitedCount: Int
    let totalCount: Int
    let coordinates: [CLLocationCoordinate2D]
    let center: CLLocationCoordinate2D
    let isVisited: Bool
    let visitSource: VisitSource?
    
    var visitPercentage: Double {
        return totalCount > 0 ? Double(visitedCount) / Double(totalCount) * 100 : 0
    }
}

// MARK: - 城市数据模型
struct City {
    let id: String
    let name: String
    let provinceId: String
    let countryId: String
    let coordinate: CLLocationCoordinate2D
    let isVisited: Bool
    let visitDate: Date?
    let visitSource: VisitSource?
    let photoCount: Int // 该城市的照片数量
    let stayDuration: TimeInterval? // 停留时长
}

// MARK: - 统计信息模型
struct GlobalLifeStats {
    // 基础统计
    let totalDistance: Double // 总移动距离（公里）
    let visitedCountries: Int
    let totalCountries: Int
    let visitedProvinces: Int
    let totalProvinces: Int
    let visitedCities: Int
    let totalCities: Int
    
    // 百分比统计
    let worldCoveragePercentage: Double // 点亮世界百分比
    let countryCoveragePercentage: Double // 点亮国家百分比
    let userRanking: Double // 战胜用户百分比
    
    // 距离对比统计
    let earthCircumference: Double = 40075.0 // 地球周长（公里）
    let chinaWidth: Double = 5200.0 // 中国东西跨度（公里）
    let chinaHeight: Double = 5500.0 // 中国南北跨度（公里）
    
    // 计算属性
    var earthRounds: Double {
        return totalDistance / earthCircumference
    }
    
    var chinaWidthTimes: Double {
        return totalDistance / chinaWidth
    }
    
    var chinaHeightTimes: Double {
        return totalDistance / chinaHeight
    }
    
    // 时间统计
    let totalTravelDays: Int
    let firstTravelDate: Date?
    let lastTravelDate: Date?
    
    // 照片统计
    let totalPhotos: Int
    let photosWithGPS: Int
    let manuallyMarkedPlaces: Int
}

// MARK: - 年度报告模型
struct AnnualReport {
    let year: Int
    let stats: GlobalLifeStats
    let topDestinations: [City] // 最常去的城市
    let longestTrip: TripSummary? // 最长旅程
    let farthestDestination: City? // 最远目的地
    let newDestinations: [City] // 新去的地方
    let travelMethods: [TravelMethod] // 出行方式统计
    let monthlyStats: [MonthlyStats] // 月度统计
}

struct TripSummary {
    let startDate: Date
    let endDate: Date
    let distance: Double
    let cities: [City]
    let duration: TimeInterval
}

struct TravelMethod {
    let type: String // 步行、驾车、骑行等
    let distance: Double
    let percentage: Double
    let color: String // 显示颜色
}

struct MonthlyStats {
    let month: Int
    let distance: Double
    let newCities: Int
    let photos: Int
    let travelDays: Int
}

// MARK: - 地图显示配置
struct MapDisplayConfig {
    let visitedRegionColor: String = "#4A90E2" // 已访问区域颜色（蓝色）
    let photoGPSColor: String = "#90EE90" // 照片GPS标记颜色（浅绿色）
    let manualMarkColor: String = "#87CEEB" // 手动标记颜色（浅蓝色）
    let unvisitedRegionColor: String = "#F5F5F5" // 未访问区域颜色（浅灰色）
    let borderColor: String = "#CCCCCC" // 边界颜色
    let selectedRegionColor: String = "#FF6B6B" // 选中区域颜色
} 