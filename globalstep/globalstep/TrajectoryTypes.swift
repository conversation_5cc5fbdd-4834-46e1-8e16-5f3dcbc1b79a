import Foundation
import CoreLocation
import MapKit
import UIKit

// MARK: - CLLocationCoordinate2D Extension
extension CLLocationCoordinate2D: Codable {
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let latitude = try container.decode(Double.self, forKey: .latitude)
        let longitude = try container.decode(Double.self, forKey: .longitude)
        self.init(latitude: latitude, longitude: longitude)
    }
    
    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(latitude, forKey: .latitude)
        try container.encode(longitude, forKey: .longitude)
    }
    
    private enum CodingKeys: String, CodingKey {
        case latitude
        case longitude
    }
}

// MARK: - Transport Mode
enum TransportMode: String, Codable {
    case walking
    case running
    case cycling
    case driving
    case transit
    case flying
    case unknown
}

// MARK: - Trajectory Record
struct TrajectoryRecord: Codable {
    let id: String
    let name: String
    let points: [CLLocationCoordinate2D]
    let startTime: Date
    let endTime: Date
    let transportModes: [TransportMode]
    
    init(name: String, points: [CLLocationCoordinate2D], startTime: Date, endTime: Date, transportModes: [TransportMode]) {
        self.id = UUID().uuidString
        self.name = name
        self.points = points
        self.startTime = startTime
        self.endTime = endTime
        self.transportModes = transportModes
    }
}

// MARK: - Video Quality
enum VideoQuality {
    case low
    case medium
    case high
    case ultra
}

// MARK: - Route Type
enum RouteType {
    case realPath
    case straightLine
    case arcPath
    case loopPath
}

// MARK: - Trajectory Settings
struct TrajectorySettings {
    var animationSpeed: Double
    var videoQuality: VideoQuality
    var mapType: MKMapType
    var routeType: RouteType
}

// MARK: - Video Generation Settings
struct VideoGenerationSettings {
    let resolution: CGSize
    let bitRate: Int
    let videoDuration: Double
    let maxFrameCount: Int
    let mapType: MKMapType
    let pathColor: UIColor
    let pathWidth: CGFloat
    let markerColor: UIColor
    let markerSize: CGFloat
    
    static var defaultSettings: VideoGenerationSettings {
        return VideoGenerationSettings(
            resolution: CGSize(width: 1920, height: 1080),
            bitRate: 10_000_000,
            videoDuration: 30.0,
            maxFrameCount: 600,
            mapType: .standard,
            pathColor: .systemBlue,
            pathWidth: 5.0,
            markerColor: .systemRed,
            markerSize: 12.0
        )
    }
}
