import UIKit
import MapKit

class TrajectorySettingsViewController: UIViewController {
    
    // MARK: - Protocol
    protocol TrajectorySettingsDelegate: AnyObject {
        func didUpdateSettings(_ settings: TrajectorySettings)
    }
    
    // MARK: - Properties
    weak var delegate: TrajectorySettingsDelegate?
    var currentSettings: TrajectorySettings?
    
    private let tableView = UITableView(frame: .zero, style: .grouped)
    
    private enum Section: Int, CaseIterable {
        case animation
        case video
        case map
        case route
        
        var title: String {
            switch self {
            case .animation: return "动画设置"
            case .video: return "视频设置"
            case .map: return "地图设置"
            case .route: return "路线设置"
            }
        }
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "轨迹动画设置"
        view.backgroundColor = .systemBackground
        
        // Navigation bar buttons
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelButtonTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .save,
            target: self,
            action: #selector(saveButtonTapped)
        )
        
        // Table view setup
        tableView.delegate = self
        tableView.dataSource = self
        tableView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(tableView)
        
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor)
        ])
        
        // Register cell types
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "cell")
        tableView.register(SliderTableViewCell.self, forCellReuseIdentifier: "sliderCell")
    }
    
    // MARK: - Actions
    @objc private func cancelButtonTapped() {
        dismiss(animated: true)
    }
    
    @objc private func saveButtonTapped() {
        guard let settings = currentSettings else { return }
        delegate?.didUpdateSettings(settings)
        dismiss(animated: true)
    }
    
    // MARK: - Helper Methods
    private func updateSettings() {
        if currentSettings == nil {
            currentSettings = TrajectorySettings(
                animationSpeed: 1.0,
                videoQuality: .high,
                mapType: .standard,
                routeType: .realPath
            )
        }
    }
}

// MARK: - UITableViewDataSource
extension TrajectorySettingsViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return Section.allCases.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let section = Section(rawValue: section) else { return 0 }
        
        switch section {
        case .animation:
            return 1 // Animation speed
        case .video:
            return 1 // Video quality
        case .map:
            return 4 // Map types
        case .route:
            return 4 // Route types
        }
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        guard let section = Section(rawValue: section) else { return nil }
        return section.title
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        updateSettings()
        guard let section = Section(rawValue: indexPath.section), let settings = currentSettings else {
            return UITableViewCell()
        }
        
        switch section {
        case .animation:
            let cell = tableView.dequeueReusableCell(withIdentifier: "sliderCell", for: indexPath) as! SliderTableViewCell
            cell.titleLabel.text = "动画速度"
            cell.slider.minimumValue = 0.1
            cell.slider.maximumValue = 2.0
            cell.slider.value = Float(settings.animationSpeed)
            cell.valueLabel.text = String(format: "%.1fx", settings.animationSpeed)
            
            cell.valueChangedHandler = { [weak self] value in
                self?.currentSettings?.animationSpeed = Double(value)
                cell.valueLabel.text = String(format: "%.1fx", value)
            }
            
            return cell
            
        case .video:
            let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath)
            cell.textLabel?.text = "视频质量"
            
            let qualities = ["低", "中", "高", "超高"]
            let selectedQuality: String
            
            switch settings.videoQuality {
            case .low: selectedQuality = qualities[0]
            case .medium: selectedQuality = qualities[1]
            case .high: selectedQuality = qualities[2]
            case .ultra: selectedQuality = qualities[3]
            }
            
            cell.detailTextLabel?.text = selectedQuality
            cell.accessoryType = .disclosureIndicator
            
            return cell
            
        case .map:
            let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath)
            
            let mapTypes = ["标准", "卫星", "混合", "3D"]
            cell.textLabel?.text = mapTypes[indexPath.row]
            
            let isSelected: Bool
            switch indexPath.row {
            case 0: isSelected = settings.mapType == .standard
            case 1: isSelected = settings.mapType == .satellite
            case 2: isSelected = settings.mapType == .hybrid
            case 3: isSelected = settings.mapType == .hybridFlyover
            default: isSelected = false
            }
            
            cell.accessoryType = isSelected ? .checkmark : .none
            
            return cell
            
        case .route:
            let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath)
            
            let routeTypes = ["真实路径", "直线", "弧线", "环线"]
            cell.textLabel?.text = routeTypes[indexPath.row]
            
            let isSelected: Bool
            switch indexPath.row {
            case 0: isSelected = settings.routeType == .realPath
            case 1: isSelected = settings.routeType == .straightLine
            case 2: isSelected = settings.routeType == .arcPath
            case 3: isSelected = settings.routeType == .loopPath
            default: isSelected = false
            }
            
            cell.accessoryType = isSelected ? .checkmark : .none
            
            return cell
        }
    }
}

// MARK: - UITableViewDelegate
extension TrajectorySettingsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        guard let section = Section(rawValue: indexPath.section) else { return }
        
        switch section {
        case .video:
            showQualityPicker()
            
        case .map:
            updateMapType(indexPath.row)
            tableView.reloadSections(IndexSet(integer: section.rawValue), with: .automatic)
            
        case .route:
            updateRouteType(indexPath.row)
            tableView.reloadSections(IndexSet(integer: section.rawValue), with: .automatic)
            
        default:
            break
        }
    }
    
    private func showQualityPicker() {
        let alertController = UIAlertController(title: "选择视频质量", message: nil, preferredStyle: .actionSheet)
        
        let qualities: [(title: String, quality: VideoQuality)] = [
            ("低 (640x480)", .low),
            ("中 (1280x720)", .medium),
            ("高 (1920x1080)", .high),
            ("超高 (3840x2160)", .ultra)
        ]
        
        for (title, quality) in qualities {
            let action = UIAlertAction(title: title, style: .default) { [weak self] _ in
                self?.currentSettings?.videoQuality = quality
                self?.tableView.reloadSections(IndexSet(integer: Section.video.rawValue), with: .automatic)
            }
            alertController.addAction(action)
        }
        
        alertController.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(alertController, animated: true)
    }
    
    private func updateMapType(_ index: Int) {
        let mapType: MKMapType
        
        switch index {
        case 0: mapType = .standard
        case 1: mapType = .satellite
        case 2: mapType = .hybrid
        case 3: mapType = .hybridFlyover
        default: mapType = .standard
        }
        
        currentSettings?.mapType = mapType
    }
    
    private func updateRouteType(_ index: Int) {
        let routeType: RouteType
        
        switch index {
        case 0: routeType = .realPath
        case 1: routeType = .straightLine
        case 2: routeType = .arcPath
        case 3: routeType = .loopPath
        default: routeType = .realPath
        }
        
        currentSettings?.routeType = routeType
    }
}

// MARK: - SliderTableViewCell
class SliderTableViewCell: UITableViewCell {
    let titleLabel = UILabel()
    let slider = UISlider()
    let valueLabel = UILabel()
    
    var valueChangedHandler: ((Float) -> Void)?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        selectionStyle = .none
        
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 10
        stackView.alignment = .center
        stackView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(stackView)
        
        titleLabel.setContentHuggingPriority(.defaultHigh, for: .horizontal)
        valueLabel.setContentHuggingPriority(.defaultHigh, for: .horizontal)
        valueLabel.textAlignment = .right
        valueLabel.font = UIFont.systemFont(ofSize: 14)
        valueLabel.textColor = .systemGray
        
        slider.addTarget(self, action: #selector(sliderValueChanged(_:)), for: .valueChanged)
        
        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(slider)
        stackView.addArrangedSubview(valueLabel)
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 10),
            stackView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            stackView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            stackView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -10),
            
            valueLabel.widthAnchor.constraint(equalToConstant: 40)
        ])
    }
    
    @objc private func sliderValueChanged(_ sender: UISlider) {
        valueChangedHandler?(sender.value)
    }
}
