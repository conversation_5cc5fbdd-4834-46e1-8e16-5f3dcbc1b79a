import UIKit
import MapKit

class ExerciseCompletionViewController: UIViewController {
    
    // MARK: - Properties
    var exerciseRecord: ExerciseRecord?
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let headerView = UIView()
    private let congratsLabel = UILabel()
    private let exerciseTypeLabel = UILabel()
    
    private let mapView = MKMapView()
    private let statsContainerView = UIView()
    
    private let actionButtonsStackView = UIStackView()
    private let shareButton = UIButton(type: .system)
    private let saveButton = UIButton(type: .system)
    private let doneButton = UIButton(type: .system)
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        displayExerciseData()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        setupScrollView()
        setupHeader()
        setupMapView()
        setupStatsContainer()
        setupActionButtons()
        setupConstraints()
    }
    
    private func setupScrollView() {
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
    }
    
    private func setupHeader() {
        headerView.backgroundColor = .systemGreen
        headerView.layer.cornerRadius = 12
        headerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(headerView)
        
        congratsLabel.text = "🎉 运动完成！"
        congratsLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        congratsLabel.textColor = .white
        congratsLabel.textAlignment = .center
        congratsLabel.translatesAutoresizingMaskIntoConstraints = false
        headerView.addSubview(congratsLabel)
        
        exerciseTypeLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        exerciseTypeLabel.textColor = .white
        exerciseTypeLabel.textAlignment = .center
        exerciseTypeLabel.translatesAutoresizingMaskIntoConstraints = false
        headerView.addSubview(exerciseTypeLabel)
    }
    
    private func setupMapView() {
        mapView.delegate = self
        mapView.mapType = .standard
        mapView.layer.cornerRadius = 12
        mapView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(mapView)
    }
    
    private func setupStatsContainer() {
        statsContainerView.backgroundColor = .secondarySystemBackground
        statsContainerView.layer.cornerRadius = 12
        statsContainerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(statsContainerView)
    }
    
    private func setupActionButtons() {
        actionButtonsStackView.axis = .horizontal
        actionButtonsStackView.distribution = .fillEqually
        actionButtonsStackView.spacing = 15
        actionButtonsStackView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(actionButtonsStackView)
        
        // 分享按钮
        shareButton.setTitle("分享", for: .normal)
        shareButton.backgroundColor = .systemBlue
        shareButton.setTitleColor(.white, for: .normal)
        shareButton.layer.cornerRadius = 25
        shareButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        shareButton.addTarget(self, action: #selector(shareButtonTapped), for: .touchUpInside)
        
        // 保存按钮
        saveButton.setTitle("保存到相册", for: .normal)
        saveButton.backgroundColor = .systemPurple
        saveButton.setTitleColor(.white, for: .normal)
        saveButton.layer.cornerRadius = 25
        saveButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        saveButton.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)
        
        // 完成按钮
        doneButton.setTitle("完成", for: .normal)
        doneButton.backgroundColor = .systemGreen
        doneButton.setTitleColor(.white, for: .normal)
        doneButton.layer.cornerRadius = 25
        doneButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        doneButton.addTarget(self, action: #selector(doneButtonTapped), for: .touchUpInside)
        
        actionButtonsStackView.addArrangedSubview(shareButton)
        actionButtonsStackView.addArrangedSubview(saveButton)
        actionButtonsStackView.addArrangedSubview(doneButton)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 滚动视图
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            // 头部视图
            headerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            headerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            headerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            headerView.heightAnchor.constraint(equalToConstant: 100),
            
            congratsLabel.topAnchor.constraint(equalTo: headerView.topAnchor, constant: 15),
            congratsLabel.leadingAnchor.constraint(equalTo: headerView.leadingAnchor, constant: 20),
            congratsLabel.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -20),
            
            exerciseTypeLabel.topAnchor.constraint(equalTo: congratsLabel.bottomAnchor, constant: 5),
            exerciseTypeLabel.leadingAnchor.constraint(equalTo: headerView.leadingAnchor, constant: 20),
            exerciseTypeLabel.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -20),
            exerciseTypeLabel.bottomAnchor.constraint(equalTo: headerView.bottomAnchor, constant: -15),
            
            // 地图视图
            mapView.topAnchor.constraint(equalTo: headerView.bottomAnchor, constant: 20),
            mapView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            mapView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            mapView.heightAnchor.constraint(equalToConstant: 200),
            
            // 统计容器
            statsContainerView.topAnchor.constraint(equalTo: mapView.bottomAnchor, constant: 20),
            statsContainerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            statsContainerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            statsContainerView.heightAnchor.constraint(greaterThanOrEqualToConstant: 200),
            
            // 操作按钮
            actionButtonsStackView.topAnchor.constraint(equalTo: statsContainerView.bottomAnchor, constant: 30),
            actionButtonsStackView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 40),
            actionButtonsStackView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -40),
            actionButtonsStackView.heightAnchor.constraint(equalToConstant: 50),
            actionButtonsStackView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -30)
        ])
    }
    
    // MARK: - Display Exercise Data
    private func displayExerciseData() {
        guard let record = exerciseRecord else { return }
        
        // 更新头部信息
        exerciseTypeLabel.text = record.type.displayName
        
        // 显示地图轨迹
        displayMapTrajectory()
        
        // 显示统计信息
        displayStats()
    }
    
    private func displayMapTrajectory() {
        guard let record = exerciseRecord, !record.trajectory.isEmpty else {
            mapView.isHidden = true
            return
        }
        
        // 添加轨迹线
        let coordinates = record.trajectory.map { $0.coordinate.clLocationCoordinate }
        let polyline = MKPolyline(coordinates: coordinates, count: coordinates.count)
        mapView.addOverlay(polyline)
        
        // 添加起点和终点标记
        if let startPoint = record.trajectory.first {
            let startAnnotation = MKPointAnnotation()
            startAnnotation.coordinate = startPoint.coordinate.clLocationCoordinate
            startAnnotation.title = "起点"
            mapView.addAnnotation(startAnnotation)
        }
        
        if let endPoint = record.trajectory.last {
            let endAnnotation = MKPointAnnotation()
            endAnnotation.coordinate = endPoint.coordinate.clLocationCoordinate
            endAnnotation.title = "终点"
            mapView.addAnnotation(endAnnotation)
        }
        
        // 设置地图显示区域
        let region = MKCoordinateRegion(coordinates: coordinates)
        mapView.setRegion(region, animated: false)
    }
    
    private func displayStats() {
        guard let record = exerciseRecord else { return }
        
        // 清除现有内容
        statsContainerView.subviews.forEach { $0.removeFromSuperview() }
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 20
        stackView.translatesAutoresizingMaskIntoConstraints = false
        statsContainerView.addSubview(stackView)
        
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "运动数据"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        titleLabel.textAlignment = .center
        stackView.addArrangedSubview(titleLabel)
        
        // 主要数据网格
        let mainStatsGrid = createMainStatsGrid(record: record)
        stackView.addArrangedSubview(mainStatsGrid)
        
        // 详细数据
        let detailStats = createDetailStats(record: record)
        stackView.addArrangedSubview(detailStats)
        
        // 成就信息
        let achievements = createAchievements(record: record)
        stackView.addArrangedSubview(achievements)
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: statsContainerView.topAnchor, constant: 20),
            stackView.leadingAnchor.constraint(equalTo: statsContainerView.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: statsContainerView.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: statsContainerView.bottomAnchor, constant: -20)
        ])
    }
    
    private func createMainStatsGrid(record: ExerciseRecord) -> UIView {
        let container = UIView()
        
        let topRow = UIStackView()
        topRow.axis = .horizontal
        topRow.distribution = .fillEqually
        topRow.spacing = 20
        topRow.translatesAutoresizingMaskIntoConstraints = false
        
        let bottomRow = UIStackView()
        bottomRow.axis = .horizontal
        bottomRow.distribution = .fillEqually
        bottomRow.spacing = 20
        bottomRow.translatesAutoresizingMaskIntoConstraints = false
        
        // 时长
        let durationView = createStatView(
            title: "时长",
            value: record.formattedDuration,
            color: .systemBlue
        )
        
        // 距离
        let distanceView = createStatView(
            title: "距离",
            value: record.formattedDistance,
            color: .systemGreen
        )
        
        // 平均速度
        let speedView = createStatView(
            title: "平均速度",
            value: String(format: "%.1f km/h", record.averageSpeedKmh),
            color: .systemOrange
        )
        
        // 卡路里
        let caloriesView = createStatView(
            title: "卡路里",
            value: String(format: "%.0f kcal", record.calories),
            color: .systemRed
        )
        
        topRow.addArrangedSubview(durationView)
        topRow.addArrangedSubview(distanceView)
        
        bottomRow.addArrangedSubview(speedView)
        bottomRow.addArrangedSubview(caloriesView)
        
        container.addSubview(topRow)
        container.addSubview(bottomRow)
        
        NSLayoutConstraint.activate([
            topRow.topAnchor.constraint(equalTo: container.topAnchor),
            topRow.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            topRow.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            topRow.heightAnchor.constraint(equalToConstant: 60),
            
            bottomRow.topAnchor.constraint(equalTo: topRow.bottomAnchor, constant: 15),
            bottomRow.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            bottomRow.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            bottomRow.heightAnchor.constraint(equalToConstant: 60),
            bottomRow.bottomAnchor.constraint(equalTo: container.bottomAnchor)
        ])
        
        return container
    }
    
    private func createStatView(title: String, value: String, color: UIColor) -> UIView {
        let container = UIView()
        container.backgroundColor = .tertiarySystemBackground
        container.layer.cornerRadius = 8
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        titleLabel.textColor = .secondaryLabel
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        valueLabel.textColor = color
        valueLabel.textAlignment = .center
        valueLabel.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(titleLabel)
        container.addSubview(valueLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor, constant: 8),
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 5),
            titleLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -5),
            
            valueLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 5),
            valueLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 5),
            valueLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -5),
            valueLabel.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -8)
        ])
        
        return container
    }
    
    private func createDetailStats(record: ExerciseRecord) -> UIView {
        let container = UIView()
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 8
        stackView.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(stackView)
        
        // 详细统计项
        let detailItems = [
            ("配速", record.pace),
            ("最大速度", String(format: "%.1f km/h", record.maxSpeedKmh)),
            ("开始时间", formatTime(record.startTime)),
            ("结束时间", formatTime(record.endTime ?? Date()))
        ]
        
        for (title, value) in detailItems {
            let itemView = createDetailItem(title: title, value: value)
            stackView.addArrangedSubview(itemView)
        }
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: container.topAnchor),
            stackView.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            stackView.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            stackView.bottomAnchor.constraint(equalTo: container.bottomAnchor)
        ])
        
        return container
    }
    
    private func createDetailItem(title: String, value: String) -> UIView {
        let container = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = UIFont.systemFont(ofSize: 14)
        valueLabel.textColor = .secondaryLabel
        valueLabel.textAlignment = .right
        valueLabel.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(titleLabel)
        container.addSubview(valueLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            
            valueLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            valueLabel.centerYAnchor.constraint(equalTo: container.centerYAnchor),
            valueLabel.leadingAnchor.constraint(greaterThanOrEqualTo: titleLabel.trailingAnchor, constant: 10),
            
            container.heightAnchor.constraint(equalToConstant: 30)
        ])
        
        return container
    }
    
    private func createAchievements(record: ExerciseRecord) -> UIView {
        let container = UIView()
        container.backgroundColor = .systemYellow.withAlphaComponent(0.1)
        container.layer.cornerRadius = 8
        
        let titleLabel = UILabel()
        titleLabel.text = "🏆 本次成就"
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        let achievementLabel = UILabel()
        achievementLabel.text = generateAchievementText(record: record)
        achievementLabel.font = UIFont.systemFont(ofSize: 14)
        achievementLabel.textColor = .secondaryLabel
        achievementLabel.numberOfLines = 0
        achievementLabel.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(titleLabel)
        container.addSubview(achievementLabel)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor, constant: 15),
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 15),
            titleLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -15),
            
            achievementLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 10),
            achievementLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 15),
            achievementLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -15),
            achievementLabel.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -15)
        ])
        
        return container
    }
    
    // MARK: - Helper Methods
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    private func generateAchievementText(record: ExerciseRecord) -> String {
        var achievements: [String] = []
        
        // 距离成就
        let distanceKm = record.distanceInKm
        if distanceKm >= 10 {
            achievements.append("完成10公里挑战")
        } else if distanceKm >= 5 {
            achievements.append("完成5公里挑战")
        } else if distanceKm >= 1 {
            achievements.append("完成1公里挑战")
        }
        
        // 时长成就
        let durationMinutes = record.duration / 60
        if durationMinutes >= 60 {
            achievements.append("坚持运动1小时")
        } else if durationMinutes >= 30 {
            achievements.append("坚持运动30分钟")
        }
        
        // 卡路里成就
        if record.calories >= 500 {
            achievements.append("燃烧500+卡路里")
        } else if record.calories >= 200 {
            achievements.append("燃烧200+卡路里")
        }
        
        if achievements.isEmpty {
            return "继续加油，下次挑战更高目标！"
        } else {
            return achievements.joined(separator: "\n")
        }
    }
    
    // MARK: - Actions
    @objc private func shareButtonTapped() {
        guard let record = exerciseRecord else { return }
        
        let shareText = """
        刚刚完成了一次\(record.type.displayName)！
        
        📊 运动数据：
        ⏱ 时长：\(record.formattedDuration)
        📏 距离：\(record.formattedDistance)
        🔥 卡路里：\(String(format: "%.0f", record.calories)) kcal
        ⚡️ 平均速度：\(String(format: "%.1f", record.averageSpeedKmh)) km/h
        
        #环球足迹 #运动打卡
        """
        
        let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)
        activityVC.popoverPresentationController?.sourceView = shareButton
        present(activityVC, animated: true)
    }
    
    @objc private func saveButtonTapped() {
        // 生成运动总结图片并保存到相册
        let image = generateSummaryImage()
        UIImageWriteToSavedPhotosAlbum(image, self, #selector(imageSaved(_:didFinishSavingWithError:contextInfo:)), nil)
    }
    
    @objc private func imageSaved(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        let alert = UIAlertController(
            title: error == nil ? "保存成功" : "保存失败",
            message: error == nil ? "运动总结已保存到相册" : error?.localizedDescription,
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    private func generateSummaryImage() -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: CGSize(width: 400, height: 600))
        return renderer.image { context in
            // 简化的图片生成，实际应用中可以创建更精美的设计
            UIColor.systemBackground.setFill()
            context.fill(CGRect(x: 0, y: 0, width: 400, height: 600))
            
            let title = "运动总结"
            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 24, weight: .bold),
                .foregroundColor: UIColor.label
            ]
            title.draw(at: CGPoint(x: 150, y: 50), withAttributes: titleAttributes)
            
            // 添加更多运动数据...
        }
    }
    
    @objc private func doneButtonTapped() {
        // 关闭所有相关页面，返回主界面
        if let presentingVC = presentingViewController {
            presentingVC.dismiss(animated: true)
        }
    }
}

// MARK: - MKMapViewDelegate
extension ExerciseCompletionViewController: MKMapViewDelegate {
    
    func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
        if let polyline = overlay as? MKPolyline {
            let renderer = MKPolylineRenderer(polyline: polyline)
            renderer.strokeColor = exerciseRecord?.type.color ?? .systemBlue
            renderer.lineWidth = 4.0
            return renderer
        }
        return MKOverlayRenderer(overlay: overlay)
    }
    
    func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
        if annotation is MKPointAnnotation {
            let identifier = "PointAnnotation"
            var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)
            
            if annotationView == nil {
                annotationView = MKAnnotationView(annotation: annotation, reuseIdentifier: identifier)
                annotationView?.canShowCallout = true
            }
            
            // 设置起点和终点图标
            if annotation.title == "起点" {
                annotationView?.image = UIImage(systemName: "play.circle.fill")?.withTintColor(.systemGreen, renderingMode: .alwaysOriginal)
            } else if annotation.title == "终点" {
                annotationView?.image = UIImage(systemName: "stop.circle.fill")?.withTintColor(.systemRed, renderingMode: .alwaysOriginal)
            }
            
            return annotationView
        }
        
        return nil
    }
}

// MARK: - MKCoordinateRegion Extension
extension MKCoordinateRegion {
    init(coordinates: [CLLocationCoordinate2D]) {
        guard !coordinates.isEmpty else {
            self.init()
            return
        }
        
        let minLat = coordinates.map { $0.latitude }.min() ?? 0
        let maxLat = coordinates.map { $0.latitude }.max() ?? 0
        let minLon = coordinates.map { $0.longitude }.min() ?? 0
        let maxLon = coordinates.map { $0.longitude }.max() ?? 0
        
        let center = CLLocationCoordinate2D(
            latitude: (minLat + maxLat) / 2,
            longitude: (minLon + maxLon) / 2
        )
        
        let span = MKCoordinateSpan(
            latitudeDelta: (maxLat - minLat) * 1.2,
            longitudeDelta: (maxLon - minLon) * 1.2
        )
        
        self.init(center: center, span: span)
    }
} 