import UIKit
import MapKit

class TripMapViewController: UIViewController {
    
    // MARK: - Properties
    var tripPlans: [TripPlan] = []
    var currentPlanIndex: Int = 0
    var departureCity: String = ""
    var destinationCity: String = ""
    
    // MARK: - UI Components
    private var mapView: MKMapView!
    private var planSelector: UISegmentedControl!
    private var segmentedControl: UISegmentedControl!
    private var bottomSheetView: UIView!
    private var routeLabel: UILabel!
    private var dayPlanView: UIView!
    private var copyButton: UIButton!
    private var highlightLabel: UILabel!
    private var routeContainerView: UIView!
    
    // Trip data
    private var dayPlans: [DayPlan] = []
    private var currentSelectedDay = 0
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupTripData()
        setupMapView()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // 确保导航栏显示
        navigationController?.setNavigationBarHidden(false, animated: false)
        
        // 设置标题显示当前方案
        if currentPlanIndex < tripPlans.count {
            let currentPlan = tripPlans[currentPlanIndex]
            navigationItem.title = currentPlan.title
        } else if !destinationCity.isEmpty {
            navigationItem.title = "\(destinationCity)行程地图"
        } else {
            navigationItem.title = "行程地图"
        }
        
        // 确保返回按钮正常工作
        print("TripMapViewController即将显示，目的地: \(destinationCity)")
        
        // 调试导航控制器状态
        if let navController = navigationController {
            print("导航控制器存在，视图控制器数量: \(navController.viewControllers.count)")
        } else {
            print("警告：没有导航控制器")
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        print("🎯 TripMapViewController已显示")
        
        // 确保地图正确显示标注和路线
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            print("🔄 检查地图状态：annotations=\(self.mapView.annotations.count), overlays=\(self.mapView.overlays.count)")
            
            if self.mapView.annotations.isEmpty && !self.dayPlans.isEmpty {
                print("⚠️ 地图标注为空，重新添加")
                self.addMapAnnotations()
            }
            
            if self.mapView.overlays.isEmpty && !self.dayPlans.isEmpty {
                print("⚠️ 地图路线为空，重新添加")
                self.addRouteLines()
            }
            
            // 强制更新高亮文本和行程计划视图
            self.updateHighlightText()
            self.updateDayPlanView()
        }
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        // Navigation
        navigationItem.title = "行程地图"
        navigationController?.navigationBar.prefersLargeTitles = false
        
        // 添加返回按钮 - 使用更明显的样式
        let backButton = UIBarButtonItem(
            image: UIImage(systemName: "chevron.left.circle.fill"),
            style: .plain,
            target: self,
            action: #selector(backTapped)
        )
        backButton.tintColor = .systemBlue
        navigationItem.leftBarButtonItem = backButton
        
        // 也设置一个文字的返回按钮作为备选
        navigationItem.hidesBackButton = false
        
        // 确保导航栏显示
        navigationController?.setNavigationBarHidden(false, animated: false)
        
        // Map View
        mapView = MKMapView()
        mapView.translatesAutoresizingMaskIntoConstraints = false
        mapView.delegate = self
        mapView.showsUserLocation = true
        view.addSubview(mapView)
        
        // Bottom Sheet
        setupBottomSheet()
    }
    
    private func setupBottomSheet() {
        bottomSheetView = UIView()
        bottomSheetView.translatesAutoresizingMaskIntoConstraints = false
        bottomSheetView.backgroundColor = .systemBackground
        bottomSheetView.layer.cornerRadius = 20
        bottomSheetView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        bottomSheetView.layer.shadowColor = UIColor.black.cgColor
        bottomSheetView.layer.shadowOffset = CGSize(width: 0, height: -2)
        bottomSheetView.layer.shadowRadius = 10
        bottomSheetView.layer.shadowOpacity = 0.1
        view.addSubview(bottomSheetView)
        
        // Handle bar
        let handleBar = UIView()
        handleBar.translatesAutoresizingMaskIntoConstraints = false
        handleBar.backgroundColor = .systemGray4
        handleBar.layer.cornerRadius = 2
        bottomSheetView.addSubview(handleBar)
        
        // Plan Selector (will be updated dynamically based on available plans)
        planSelector = UISegmentedControl(items: ["方案1"])
        planSelector.selectedSegmentIndex = 0
        planSelector.translatesAutoresizingMaskIntoConstraints = false
        planSelector.addTarget(self, action: #selector(planChanged), for: .valueChanged)
        bottomSheetView.addSubview(planSelector)
        
        // Day Segmented Control (will be updated dynamically)
        segmentedControl = UISegmentedControl(items: ["总览"])
        segmentedControl.selectedSegmentIndex = 0
        segmentedControl.translatesAutoresizingMaskIntoConstraints = false
        segmentedControl.addTarget(self, action: #selector(segmentChanged), for: .valueChanged)
        bottomSheetView.addSubview(segmentedControl)
        
        // Route section
        setupRouteSection()
        
        // Day plan view
        dayPlanView = UIView()
        dayPlanView.translatesAutoresizingMaskIntoConstraints = false
        dayPlanView.backgroundColor = .systemGray6
        dayPlanView.layer.cornerRadius = 12
        bottomSheetView.addSubview(dayPlanView)
        
        // Copy button
        copyButton = UIButton(type: .system)
        copyButton.setTitle("复制为我的行程", for: .normal)
        copyButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        copyButton.setTitleColor(.white, for: .normal)
        copyButton.backgroundColor = .systemBlue
        copyButton.layer.cornerRadius = 25
        copyButton.translatesAutoresizingMaskIntoConstraints = false
        copyButton.addTarget(self, action: #selector(copyTapped), for: .touchUpInside)
        bottomSheetView.addSubview(copyButton)
        
        NSLayoutConstraint.activate([
            handleBar.topAnchor.constraint(equalTo: bottomSheetView.topAnchor, constant: 8),
            handleBar.centerXAnchor.constraint(equalTo: bottomSheetView.centerXAnchor),
            handleBar.widthAnchor.constraint(equalToConstant: 40),
            handleBar.heightAnchor.constraint(equalToConstant: 4),
            
            planSelector.topAnchor.constraint(equalTo: handleBar.bottomAnchor, constant: 16),
            planSelector.leadingAnchor.constraint(equalTo: bottomSheetView.leadingAnchor, constant: 20),
            planSelector.trailingAnchor.constraint(equalTo: bottomSheetView.trailingAnchor, constant: -20),
            
            segmentedControl.topAnchor.constraint(equalTo: planSelector.bottomAnchor, constant: 16),
            segmentedControl.leadingAnchor.constraint(equalTo: bottomSheetView.leadingAnchor, constant: 20),
            segmentedControl.trailingAnchor.constraint(equalTo: bottomSheetView.trailingAnchor, constant: -20),
            
            dayPlanView.topAnchor.constraint(equalTo: routeContainerView.bottomAnchor, constant: 20),
            dayPlanView.leadingAnchor.constraint(equalTo: bottomSheetView.leadingAnchor, constant: 20),
            dayPlanView.trailingAnchor.constraint(equalTo: bottomSheetView.trailingAnchor, constant: -20),
            dayPlanView.heightAnchor.constraint(equalToConstant: 200),
            
            copyButton.topAnchor.constraint(equalTo: dayPlanView.bottomAnchor, constant: 20),
            copyButton.leadingAnchor.constraint(equalTo: bottomSheetView.leadingAnchor, constant: 20),
            copyButton.trailingAnchor.constraint(equalTo: bottomSheetView.trailingAnchor, constant: -20),
            copyButton.heightAnchor.constraint(equalToConstant: 50),
            copyButton.bottomAnchor.constraint(equalTo: bottomSheetView.safeAreaLayoutGuide.bottomAnchor, constant: -20)
        ])
    }
    
    private func setupRouteSection() {
        routeContainerView = UIView()
        routeContainerView.translatesAutoresizingMaskIntoConstraints = false
        routeContainerView.backgroundColor = .systemGray6
        routeContainerView.layer.cornerRadius = 12
        bottomSheetView.addSubview(routeContainerView)
        
        routeLabel = UILabel()
        routeLabel.text = "线路亮点 ✈️"
        routeLabel.font = UIFont.boldSystemFont(ofSize: 16)
        routeLabel.textColor = .label
        routeLabel.translatesAutoresizingMaskIntoConstraints = false
        routeContainerView.addSubview(routeLabel)
        
        let highlightLabel = UILabel()
        highlightLabel.text = "精彩的旅程等待您的探索！"  // 初始化时使用默认文本
        highlightLabel.font = UIFont.systemFont(ofSize: 14)
        highlightLabel.textColor = .secondaryLabel
        highlightLabel.numberOfLines = 0
        highlightLabel.translatesAutoresizingMaskIntoConstraints = false
        routeContainerView.addSubview(highlightLabel)
        
        // 保存引用以便后续更新
        self.highlightLabel = highlightLabel
        
        NSLayoutConstraint.activate([
            routeContainerView.topAnchor.constraint(equalTo: segmentedControl.bottomAnchor, constant: 20),
            routeContainerView.leadingAnchor.constraint(equalTo: bottomSheetView.leadingAnchor, constant: 20),
            routeContainerView.trailingAnchor.constraint(equalTo: bottomSheetView.trailingAnchor, constant: -20),
            
            routeLabel.topAnchor.constraint(equalTo: routeContainerView.topAnchor, constant: 12),
            routeLabel.leadingAnchor.constraint(equalTo: routeContainerView.leadingAnchor, constant: 16),
            
            highlightLabel.topAnchor.constraint(equalTo: routeLabel.bottomAnchor, constant: 8),
            highlightLabel.leadingAnchor.constraint(equalTo: routeContainerView.leadingAnchor, constant: 16),
            highlightLabel.trailingAnchor.constraint(equalTo: routeContainerView.trailingAnchor, constant: -16),
            highlightLabel.bottomAnchor.constraint(equalTo: routeContainerView.bottomAnchor, constant: -12)
        ])
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            mapView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            mapView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            mapView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            mapView.bottomAnchor.constraint(equalTo: bottomSheetView.topAnchor),
            
            bottomSheetView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            bottomSheetView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            bottomSheetView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            bottomSheetView.heightAnchor.constraint(equalToConstant: 500)
        ])
    }
    
    private func setupTripData() {
        // 严格检查索引范围
        guard currentPlanIndex >= 0 && currentPlanIndex < tripPlans.count else {
            print("⚠️ 方案索引越界: \(currentPlanIndex), 总方案数: \(tripPlans.count)")
            generateDefaultDayPlansForDestination()
            updateSegmentedControl()
            updateHighlightText() // 确保空状态也显示正确的信息
            return
        }
        
        let currentPlan = tripPlans[currentPlanIndex]
        print("🎯 设置方案数据: \(currentPlan.title)")
        
        // Convert to day plans with error handling
        do {
            dayPlans = convertTripPlanToDayPlans(currentPlan)
            
            // 确保至少有一个dayPlan
            if dayPlans.isEmpty {
                print("⚠️ 未生成任何日程，创建默认日程")
                generateDefaultDayPlansForDestination()
            }
        } catch {
            print("❌ 转换方案时出错: \(error)")
            generateDefaultDayPlansForDestination()
        }
        
        // 更新方案选择器
        updatePlanSelector()
        
        // 更新分段控制器
        updateSegmentedControl()
        
        // 立即更新高亮文本和行程计划视图
        updateHighlightText()
        updateDayPlanView()
        
        // 延迟一点再次更新，确保数据完全加载
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.updateHighlightText()
            self?.updateDayPlanView()
        }
        
        print("✅ 方案数据设置完成，生成\(dayPlans.count)天行程")
    }
    
    private func convertTripPlanToDayPlans(_ tripPlan: TripPlan) -> [DayPlan] {
        var dayPlans: [DayPlan] = []
        
        print("🔄 转换行程方案: '\(tripPlan.title)'")
        print("📊 方案景点数量: \(tripPlan.attractions.count)")
        print("📅 方案天数: \(tripPlan.dayCount)")
        print("📋 每日计划数量: \(tripPlan.dailyPlans?.count ?? 0)")
        
        // 如果方案没有景点，生成默认景点
        guard !tripPlan.attractions.isEmpty else {
            print("⚠️ 方案无景点，生成默认景点")
            generateDefaultDayPlansForDestination()
            return self.dayPlans
        }
        
        // 如果有详细的每日计划，使用它们
        if let dailyPlans = tripPlan.dailyPlans, !dailyPlans.isEmpty {
            for dailyPlan in dailyPlans {
                let attractions = dailyPlan.activities.map { activity in
                    // 尝试从地理编码获取坐标，否则使用默认坐标
                    let coordinate = getCoordinateForLocation(activity.location) ?? 
                                   TravelDataManager.shared.getCityByName(destinationCity)?.coordinate ?? 
                                   CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737) // 上海市中心
                    
                    return Attraction(
                        name: activity.activity,
                        type: "景点",
                        coordinate: coordinate
                    )
                }
                
                let dayPlan = DayPlan(
                    day: dailyPlan.day,
                    title: dailyPlan.title,
                    subtitle: "共\(attractions.count)个活动",
                    attractions: attractions
                )
                dayPlans.append(dayPlan)
            }
        } else {
            // 使用简化的景点列表
            let totalAttractions = tripPlan.attractions.count
            guard totalAttractions > 0 else {
                print("⚠️ 景点列表为空，生成默认方案")
                generateDefaultDayPlansForDestination()
                return self.dayPlans
            }
            
            print("📊 总景点数: \(totalAttractions), 天数: \(tripPlan.dayCount)")
            
            // 改进的分配算法，确保所有景点都被分配
            var remainingAttractions = totalAttractions
            var startIndex = 0
            
            for day in 1...tripPlan.dayCount {
                let remainingDays = tripPlan.dayCount - day + 1
                let attractionsThisDay = max(1, remainingAttractions / remainingDays)
                let endIndex = min(startIndex + attractionsThisDay, totalAttractions)
                
                print("📊 Day\(day): 分配\(attractionsThisDay)个景点 (剩余景点:\(remainingAttractions), 剩余天数:\(remainingDays))")
                
                // 更严格的安全检查
                guard startIndex >= 0 && 
                      startIndex < totalAttractions && 
                      endIndex > startIndex && 
                      endIndex <= totalAttractions else {
                    print("⚠️ Day \(day): 景点分配范围错误 startIndex=\(startIndex), endIndex=\(endIndex), total=\(totalAttractions)")
                    continue
                }
                
                print("📍 Day \(day): 景点范围 [\(startIndex)..<\(endIndex)]")
                
                // 增加二次检查
                guard startIndex < tripPlan.attractions.count && endIndex <= tripPlan.attractions.count else {
                    print("⚠️ Day \(day): 数组索引超出范围，跳过")
                    continue
                }
                
                let dayAttractions = Array(tripPlan.attractions[startIndex..<endIndex]).map { attractionName in
                    let coordinate = getCoordinateForLocation(attractionName) ?? 
                                   TravelDataManager.shared.getCityByName(destinationCity)?.coordinate ?? 
                                   CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737) // 上海市中心
                    
                    return Attraction(
                        name: attractionName,
                        type: "景点",
                        coordinate: coordinate
                    )
                }
                
                let dayPlan = DayPlan(
                    day: day,
                    title: "第\(day)天：\(destinationCity)游览",
                    subtitle: "共\(dayAttractions.count)个景点",
                    attractions: dayAttractions
                )
                dayPlans.append(dayPlan)
                
                // 更新索引和剩余计数
                startIndex = endIndex
                remainingAttractions -= dayAttractions.count
            }
        }
        
        return dayPlans
    }
    
    private func getCoordinateForLocation(_ locationString: String) -> CLLocationCoordinate2D? {
        print("🔍 查找景点坐标: \(locationString)")
        
        // 1. 尝试从数据库获取精确匹配
        if let city = TravelDataManager.shared.getCityByName(destinationCity) {
            let attractions = TravelDataManager.shared.getAttractions(for: city.id)
            
            // 精确匹配
            for attraction in attractions {
                if attraction.name == locationString {
                    print("✅ 精确匹配: \(attraction.name) -> \(attraction.coordinate)")
                    return attraction.coordinate
                }
            }
            
            // 模糊匹配
            for attraction in attractions {
                if locationString.contains(attraction.name) || attraction.name.contains(locationString) {
                    print("✅ 模糊匹配: \(locationString) -> \(attraction.name) -> \(attraction.coordinate)")
                    return attraction.coordinate
                }
            }
        }
        
        // 2. 使用上海著名景点的预定义坐标
        let shanghaiAttractions: [String: CLLocationCoordinate2D] = [
            "豫园": CLLocationCoordinate2D(latitude: 31.2267, longitude: 121.4919),
            "上海城隍庙": CLLocationCoordinate2D(latitude: 31.2267, longitude: 121.4919),
            "城隍庙": CLLocationCoordinate2D(latitude: 31.2267, longitude: 121.4919),
            "上海博物馆": CLLocationCoordinate2D(latitude: 31.2276, longitude: 121.4754),
            "南京路步行街": CLLocationCoordinate2D(latitude: 31.2391, longitude: 121.4744),
            "南京路": CLLocationCoordinate2D(latitude: 31.2391, longitude: 121.4744),
            "外滩": CLLocationCoordinate2D(latitude: 31.2396, longitude: 121.4990),
            "中共一大会址": CLLocationCoordinate2D(latitude: 31.2194, longitude: 121.4693),
            "田子坊": CLLocationCoordinate2D(latitude: 31.2112, longitude: 121.4672),
            "新天地": CLLocationCoordinate2D(latitude: 31.2197, longitude: 121.4733),
            "东方明珠塔": CLLocationCoordinate2D(latitude: 31.2397, longitude: 121.4991),
            "东方明珠": CLLocationCoordinate2D(latitude: 31.2397, longitude: 121.4991),
            "上海动物园": CLLocationCoordinate2D(latitude: 31.1956, longitude: 121.4364),
            "上海植物园": CLLocationCoordinate2D(latitude: 31.1508, longitude: 121.4364),
            "上海自然博物馆": CLLocationCoordinate2D(latitude: 31.2297, longitude: 121.4444),
            "世纪公园": CLLocationCoordinate2D(latitude: 31.2097, longitude: 121.5514),
            "陆家嘴": CLLocationCoordinate2D(latitude: 31.2433, longitude: 121.5081),
            "云南南路美食街": CLLocationCoordinate2D(latitude: 31.2289, longitude: 121.4744),
            "云南南路": CLLocationCoordinate2D(latitude: 31.2289, longitude: 121.4744),
            "上海工艺美术博物馆": CLLocationCoordinate2D(latitude: 31.2089, longitude: 121.4444),
            "徐汇滨江绿地": CLLocationCoordinate2D(latitude: 31.1881, longitude: 121.4581),
            "前滩休闲公园": CLLocationCoordinate2D(latitude: 31.1681, longitude: 121.5281),
            "黄浦江轮渡": CLLocationCoordinate2D(latitude: 31.2397, longitude: 121.4990),
            "正大广场": CLLocationCoordinate2D(latitude: 31.2297, longitude: 121.5067),
            "吴江路": CLLocationCoordinate2D(latitude: 31.2186, longitude: 121.4444),
            "徐家汇": CLLocationCoordinate2D(latitude: 31.1956, longitude: 121.4364)
        ]
        
        // 精确匹配预定义坐标
        if let coordinate = shanghaiAttractions[locationString] {
            print("✅ 预定义匹配: \(locationString) -> \(coordinate)")
            return coordinate
        }
        
        // 模糊匹配预定义坐标
        for (name, coordinate) in shanghaiAttractions {
            if locationString.contains(name) || name.contains(locationString) {
                print("✅ 预定义模糊匹配: \(locationString) -> \(name) -> \(coordinate)")
                return coordinate
            }
        }
        
        print("⚠️ 未找到坐标，使用上海市中心默认坐标")
        return nil
    }
    
    private func generateDefaultDayPlansForDestination() {
        guard let city = TravelDataManager.shared.getCityByName(destinationCity) else {
            print("无法找到目的地城市: \(destinationCity)")
            // 如果在TravelDataManager中找不到，尝试生成通用景点
            generateGenericDayPlans()
            return
        }
        
        let attractions = TravelDataManager.shared.getAttractions(for: city.id)
        
        // 获取最受欢迎的景点作为默认行程
        let popularAttractions = attractions.sorted { $0.popularity > $1.popularity }.prefix(6)
        
        // 优化路线
        let optimizedAttractions = TripPlanningEngine.shared.optimizeRoute(Array(popularAttractions))
        
        // 创建默认行程
        let defaultAttractions = optimizedAttractions.map { attraction in
            Attraction(
                name: attraction.name,
                type: attraction.category.rawValue,
                coordinate: attraction.coordinate
            )
        }
        
        dayPlans = [
            DayPlan(
                day: 1,
                title: "\(destinationCity)一日游",
                subtitle: "共\(defaultAttractions.count)个地点",
                attractions: defaultAttractions
            )
        ]
    }
    
    private func generateGenericDayPlans() {
        print("🎯 生成通用景点规划")
        
        // 尝试从GlobalLifeDataManager获取城市坐标
        let allProvinces = GlobalLifeDataManager.shared.getProvinces()
        let allCities = allProvinces.flatMap { $0.cities }
        let cityCoordinate: CLLocationCoordinate2D
        
        if let matchedCity = allCities.first(where: { $0.name.contains(destinationCity) || destinationCity.contains($0.name.replacingOccurrences(of: "市", with: "")) }) {
            cityCoordinate = matchedCity.coordinate
            print("🎯 使用真实城市坐标: \(matchedCity.name) -> \(cityCoordinate)")
        } else {
            // 使用城市名称匹配一些预定义坐标
            cityCoordinate = getPredefinedCityCoordinate(for: destinationCity)
            print("🎯 使用预定义坐标: \(destinationCity) -> \(cityCoordinate)")
        }
        
        // 生成围绕城市中心的通用景点
        let genericAttractions = generateGenericAttractions(around: cityCoordinate, cityName: destinationCity)
        
        dayPlans = [
            DayPlan(
                day: 1,
                title: "\(destinationCity)经典游",
                subtitle: "共\(genericAttractions.count)个地点",
                attractions: genericAttractions
            ),
            DayPlan(
                day: 2,
                title: "\(destinationCity)深度游",
                subtitle: "共\(genericAttractions.count)个地点",
                attractions: genericAttractions
            )
        ]
        
        print("✅ 生成了\(dayPlans.count)天通用行程")
    }
    
    private func getPredefinedCityCoordinate(for cityName: String) -> CLLocationCoordinate2D {
        let predefinedCoordinates: [String: CLLocationCoordinate2D] = [
            "曲靖": CLLocationCoordinate2D(latitude: 25.5016, longitude: 103.7834),
            "玉溪": CLLocationCoordinate2D(latitude: 24.3552, longitude: 102.5428),
            "保山": CLLocationCoordinate2D(latitude: 25.1117, longitude: 99.1615),
            "昭通": CLLocationCoordinate2D(latitude: 27.3380, longitude: 103.7179),
            "普洱": CLLocationCoordinate2D(latitude: 22.7771, longitude: 100.9729),
            "临沧": CLLocationCoordinate2D(latitude: 23.8878, longitude: 100.0870),
            "德宏": CLLocationCoordinate2D(latitude: 24.4367, longitude: 98.5785),
            "文山": CLLocationCoordinate2D(latitude: 23.3695, longitude: 104.2449),
            "红河": CLLocationCoordinate2D(latitude: 23.3668, longitude: 103.3840),
            "西双版纳": CLLocationCoordinate2D(latitude: 22.0017, longitude: 100.7977),
            "楚雄": CLLocationCoordinate2D(latitude: 25.0460, longitude: 101.5462),
            "大理": CLLocationCoordinate2D(latitude: 25.6956, longitude: 100.2417),
            "丽江": CLLocationCoordinate2D(latitude: 26.8551, longitude: 100.2309),
            "怒江": CLLocationCoordinate2D(latitude: 25.8509, longitude: 98.8571),
            "迪庆": CLLocationCoordinate2D(latitude: 27.8269, longitude: 99.7065)
        ]
        
        // 精确匹配
        if let coordinate = predefinedCoordinates[cityName] {
            return coordinate
        }
        
        // 模糊匹配（去除"市"等后缀）
        let cleanCityName = cityName.replacingOccurrences(of: "市", with: "").replacingOccurrences(of: "自治州", with: "").replacingOccurrences(of: "地区", with: "")
        for (key, coordinate) in predefinedCoordinates {
            if key.contains(cleanCityName) || cleanCityName.contains(key) {
                return coordinate
            }
        }
        
        // 默认返回中国中心坐标
        return CLLocationCoordinate2D(latitude: 35.0, longitude: 105.0)
    }
    
    private func generateGenericAttractions(around coordinate: CLLocationCoordinate2D, cityName: String) -> [Attraction] {
        let baseNames = [
            "市中心广场", "历史文化街区", "特色美食街", "城市公园", "博物馆", "古城墙",
            "文化中心", "购物商圈", "风景名胜区", "传统老街", "现代广场", "地标建筑"
        ]
        
        var attractions: [Attraction] = []
        let radius: Double = 0.02 // 约2公里范围
        
        for (index, baseName) in baseNames.enumerated() {
            // 在城市坐标周围生成随机分布的景点
            let angle = Double(index) * (2.0 * Double.pi / Double(baseNames.count))
            let randomRadius = Double.random(in: 0.005...radius)
            
            let lat = coordinate.latitude + randomRadius * cos(angle)
            let lng = coordinate.longitude + randomRadius * sin(angle)
            
            let attraction = Attraction(
                name: "\(cityName)\(baseName)",
                type: "景点",
                coordinate: CLLocationCoordinate2D(latitude: lat, longitude: lng)
            )
            attractions.append(attraction)
        }
        
        return attractions
    }
    
    private func setupMapView() {
        print("🗺️ 开始设置地图视图")
        
        // Set initial region based on destination city
        var centerCoordinate: CLLocationCoordinate2D
        var cityName: String
        
        if let city = TravelDataManager.shared.getCityByName(destinationCity) {
            centerCoordinate = city.coordinate
            cityName = city.name
            print("📍 找到目的地城市: \(cityName) -> \(centerCoordinate)")
        } else {
            // 如果找不到城市，尝试使用GlobalLifeDataManager的数据
            let allProvinces = GlobalLifeDataManager.shared.getProvinces()
            let allCities = allProvinces.flatMap { $0.cities }
            
            if let matchedCity = allCities.first(where: { $0.name.contains(destinationCity) || destinationCity.contains($0.name.replacingOccurrences(of: "市", with: "")) }) {
                centerCoordinate = matchedCity.coordinate
                cityName = matchedCity.name
                print("🔍 从GlobalLifeDataManager找到城市: \(cityName) -> \(centerCoordinate)")
            } else {
                // 如果还是找不到，使用一个默认的中国中心坐标
                centerCoordinate = CLLocationCoordinate2D(latitude: 35.0, longitude: 105.0)
                cityName = destinationCity
                print("⚠️ 未找到目的地城市: \(destinationCity)，使用中国中心坐标")
                
                // 生成更通用的默认景点
                generateGenericDayPlans()
            }
        }
        
        print("📍 设置地图区域: \(cityName) -> \(centerCoordinate)")
        let region = MKCoordinateRegion(center: centerCoordinate, latitudinalMeters: 50000, longitudinalMeters: 50000)
        mapView.setRegion(region, animated: false)
        
        // Add annotations for all attractions
        addMapAnnotations()
        
        // Add route lines between attractions
        addRouteLines()
        
        print("✅ 地图视图设置完成")
    }
    
    private func updatePlanSelector() {
        // 隐藏方案选择器，只显示当前方案
        planSelector.isHidden = true
        
        // 设置标题显示当前方案名称
        if currentPlanIndex < tripPlans.count {
            let currentPlan = tripPlans[currentPlanIndex]
            // 可以在导航栏或其他地方显示当前方案名称
            print("📋 当前显示方案: \(currentPlan.title)")
        }
    }
    
    private func updateSegmentedControl() {
        // Remove all segments except the first one
        while segmentedControl.numberOfSegments > 1 {
            segmentedControl.removeSegment(at: segmentedControl.numberOfSegments - 1, animated: false)
        }
        
        // Add segments for each day
        for i in 0..<dayPlans.count {
            segmentedControl.insertSegment(withTitle: "Day \(i + 1)", at: i + 1, animated: false)
        }
        
        // Update the selected segment if needed
        if segmentedControl.selectedSegmentIndex >= segmentedControl.numberOfSegments {
            segmentedControl.selectedSegmentIndex = 0
        }
    }
    
    private func updateHighlightText() {
        highlightLabel.text = generateRouteHighlights()
    }
    
    private func addMapAnnotations() {
        print("🏷️ 开始添加地图标注，总天数: \(dayPlans.count)")
        
        mapView.removeAnnotations(mapView.annotations) // 清除现有标注
        
        for dayPlan in dayPlans {
            print("📍 Day\(dayPlan.day): 添加\(dayPlan.attractions.count)个景点标注")
            
            for (index, attraction) in dayPlan.attractions.enumerated() {
                let annotation = AttractionAnnotation()
                annotation.coordinate = attraction.coordinate
                annotation.title = attraction.name
                annotation.subtitle = "Day\(dayPlan.day) - \(attraction.type)"
                annotation.day = dayPlan.day
                annotation.order = index + 1
                
                print("  📌 \(index + 1). \(attraction.name) -> \(attraction.coordinate)")
                mapView.addAnnotation(annotation)
            }
        }
        
        print("✅ 地图标注添加完成，总标注数: \(mapView.annotations.count)")
    }
    
    private func addRouteLines() {
        print("🛣️ 开始添加地图路线")
        
        // Clear existing overlays
        mapView.removeOverlays(mapView.overlays)
        
        for dayPlan in dayPlans {
            let coordinates = dayPlan.attractions.map { $0.coordinate }
            
            print("📍 Day\(dayPlan.day): 准备添加路线，景点坐标:")
            for (index, coord) in coordinates.enumerated() {
                print("  \(index + 1). \(coord)")
            }
            
            // 只创建简单的直线连接，避免频繁的方向API调用
            if coordinates.count > 1 {
                let polyline = MKPolyline(coordinates: coordinates, count: coordinates.count)
                
                // 创建带有天数信息的路线
                let routePolyline = RoutePolyline(polyline: polyline, day: dayPlan.day)
                mapView.addOverlay(routePolyline)
                
                print("✅ 添加Day\(dayPlan.day)路线成功：\(coordinates.count)个景点连接")
            } else {
                print("⚠️ Day\(dayPlan.day)只有\(coordinates.count)个景点，无法创建路线")
            }
        }
        
        print("✅ 地图路线添加完成，总路线数: \(mapView.overlays.count)")
    }
    
    private func calculateRoute(from startCoord: CLLocationCoordinate2D, to endCoord: CLLocationCoordinate2D, day: Int) {
        let startPlacemark = MKPlacemark(coordinate: startCoord)
        let endPlacemark = MKPlacemark(coordinate: endCoord)
        
        let startMapItem = MKMapItem(placemark: startPlacemark)
        let endMapItem = MKMapItem(placemark: endPlacemark)
        
        let request = MKDirections.Request()
        request.source = startMapItem
        request.destination = endMapItem
        request.transportType = .walking // 步行路线
        
        let directions = MKDirections(request: request)
        directions.calculate { [weak self] (response, error) in
            guard let self = self,
                  let response = response,
                  let route = response.routes.first else {
                return
            }
            
            DispatchQueue.main.async {
                // Create custom polyline with day information
                let routePolyline = RoutePolyline(polyline: route.polyline, day: day)
                self.mapView.addOverlay(routePolyline)
            }
        }
    }
    
    private func updateDayPlanView() {
        print("🔄 更新行程计划视图，dayPlans数量: \(dayPlans.count), 选中段: \(segmentedControl.selectedSegmentIndex)")
        
        // Clear existing subviews
        dayPlanView.subviews.forEach { $0.removeFromSuperview() }
        
        if segmentedControl.selectedSegmentIndex == 0 {
            // Overview mode
            print("📊 显示总览模式")
            setupOverviewContent()
        } else {
            // Day mode
            let dayIndex = segmentedControl.selectedSegmentIndex - 1
            if dayIndex < dayPlans.count {
                print("📅 显示第\(dayIndex + 1)天详情")
                setupDayContent(dayPlans[dayIndex])
            } else {
                print("⚠️ 日期索引超出范围: \(dayIndex), 总天数: \(dayPlans.count)")
            }
        }
        
        print("✅ 行程计划视图更新完成")
    }
    
    private func setupOverviewContent() {
        print("🎨 设置总览内容，dayPlans数量: \(dayPlans.count)")
        
        let titleLabel = UILabel()
        titleLabel.text = "行程计划"
        titleLabel.font = UIFont.boldSystemFont(ofSize: 16)
        titleLabel.textColor = .label
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        dayPlanView.addSubview(titleLabel)
        
        guard !dayPlans.isEmpty else {
            // 如果没有行程数据，显示加载状态
            let loadingLabel = UILabel()
            loadingLabel.text = "🚀 行程计划生成中...\n✨ 即将为您呈现精彩行程"
            loadingLabel.font = UIFont.systemFont(ofSize: 14)
            loadingLabel.textColor = .secondaryLabel
            loadingLabel.numberOfLines = 0
            loadingLabel.textAlignment = .center
            loadingLabel.translatesAutoresizingMaskIntoConstraints = false
            dayPlanView.addSubview(loadingLabel)
            
            NSLayoutConstraint.activate([
                titleLabel.topAnchor.constraint(equalTo: dayPlanView.topAnchor, constant: 16),
                titleLabel.leadingAnchor.constraint(equalTo: dayPlanView.leadingAnchor, constant: 16),
                
                loadingLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 20),
                loadingLabel.leadingAnchor.constraint(equalTo: dayPlanView.leadingAnchor, constant: 16),
                loadingLabel.trailingAnchor.constraint(equalTo: dayPlanView.trailingAnchor, constant: -16),
                loadingLabel.centerYAnchor.constraint(equalTo: dayPlanView.centerYAnchor, constant: 10)
            ])
            return
        }
        
        var previousView: UIView = titleLabel
        
        for dayPlan in dayPlans {
            let dayView = createDayOverviewView(dayPlan)
            dayPlanView.addSubview(dayView)
            
            NSLayoutConstraint.activate([
                dayView.topAnchor.constraint(equalTo: previousView.bottomAnchor, constant: 12),
                dayView.leadingAnchor.constraint(equalTo: dayPlanView.leadingAnchor, constant: 16),
                dayView.trailingAnchor.constraint(equalTo: dayPlanView.trailingAnchor, constant: -16),
                dayView.heightAnchor.constraint(equalToConstant: 60)
            ])
            
            previousView = dayView
        }
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: dayPlanView.topAnchor, constant: 16),
            titleLabel.leadingAnchor.constraint(equalTo: dayPlanView.leadingAnchor, constant: 16)
        ])
        
        print("✅ 总览内容设置完成，显示\(dayPlans.count)天行程")
    }
    
    private func createDayOverviewView(_ dayPlan: DayPlan) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .systemBackground
        containerView.layer.cornerRadius = 8
        containerView.translatesAutoresizingMaskIntoConstraints = false
        
        let dayLabel = UILabel()
        dayLabel.text = "Day \(dayPlan.day)"
        dayLabel.font = UIFont.boldSystemFont(ofSize: 16)
        dayLabel.textColor = .white
        dayLabel.textAlignment = .center
        dayLabel.backgroundColor = dayPlan.day == 1 ? .systemBlue : .systemGreen
        dayLabel.layer.cornerRadius = 12
        dayLabel.clipsToBounds = true
        dayLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(dayLabel)
        
        let titleLabel = UILabel()
        titleLabel.text = dayPlan.title
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .label
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(titleLabel)
        
        let subtitleLabel = UILabel()
        subtitleLabel.text = dayPlan.subtitle
        subtitleLabel.font = UIFont.systemFont(ofSize: 12)
        subtitleLabel.textColor = .secondaryLabel
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(subtitleLabel)
        
        NSLayoutConstraint.activate([
            dayLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 12),
            dayLabel.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            dayLabel.widthAnchor.constraint(equalToConstant: 50),
            dayLabel.heightAnchor.constraint(equalToConstant: 24),
            
            titleLabel.leadingAnchor.constraint(equalTo: dayLabel.trailingAnchor, constant: 12),
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 12),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -12),
            
            subtitleLabel.leadingAnchor.constraint(equalTo: dayLabel.trailingAnchor, constant: 12),
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 4),
            subtitleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -12)
        ])
        
        return containerView
    }
    
    private func setupDayContent(_ dayPlan: DayPlan) {
        let scrollView = UIScrollView()
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        dayPlanView.addSubview(scrollView)
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(stackView)
        
        for (index, attraction) in dayPlan.attractions.enumerated() {
            let attractionView = createAttractionView(attraction, order: index + 1)
            stackView.addArrangedSubview(attractionView)
        }
        
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: dayPlanView.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: dayPlanView.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: dayPlanView.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: dayPlanView.bottomAnchor),
            
            stackView.topAnchor.constraint(equalTo: scrollView.topAnchor, constant: 16),
            stackView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor, constant: 16),
            stackView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor, constant: -16),
            stackView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor, constant: -16),
            stackView.widthAnchor.constraint(equalTo: scrollView.widthAnchor, constant: -32)
        ])
    }
    
    private func createAttractionView(_ attraction: Attraction, order: Int) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .systemBackground
        containerView.layer.cornerRadius = 8
        containerView.translatesAutoresizingMaskIntoConstraints = false
        
        let orderLabel = UILabel()
        orderLabel.text = "\(order)"
        orderLabel.font = UIFont.systemFont(ofSize: 12, weight: .bold)
        orderLabel.textColor = .white
        orderLabel.textAlignment = .center
        orderLabel.backgroundColor = .systemBlue
        orderLabel.layer.cornerRadius = 10
        orderLabel.clipsToBounds = true
        orderLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(orderLabel)
        
        let nameLabel = UILabel()
        nameLabel.text = attraction.name
        nameLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        nameLabel.textColor = .label
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(nameLabel)
        
        let typeLabel = UILabel()
        typeLabel.text = attraction.type
        typeLabel.font = UIFont.systemFont(ofSize: 12)
        typeLabel.textColor = .secondaryLabel
        typeLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(typeLabel)
        
        NSLayoutConstraint.activate([
            containerView.heightAnchor.constraint(equalToConstant: 44),
            
            orderLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 12),
            orderLabel.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            orderLabel.widthAnchor.constraint(equalToConstant: 20),
            orderLabel.heightAnchor.constraint(equalToConstant: 20),
            
            nameLabel.leadingAnchor.constraint(equalTo: orderLabel.trailingAnchor, constant: 12),
            nameLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 8),
            nameLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -12),
            
            typeLabel.leadingAnchor.constraint(equalTo: orderLabel.trailingAnchor, constant: 12),
            typeLabel.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 2),
            typeLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -12)
        ])
        
        return containerView
    }
    
    private func generateRouteHighlights() -> String {
        print("📝 生成路线高亮信息，dayPlans数量: \(dayPlans.count)")
        
        guard !dayPlans.isEmpty else {
            // 如果还没有数据，显示加载状态
            return "🚀 精彩的\(destinationCity)之旅正在为您规划中...\n\n💫 AI正在智能分析最佳路线\n🎯 即将为您呈现个性化行程"
        }
        
        var highlights: [String] = []
        let totalAttractions = dayPlans.flatMap { $0.attractions }.count
        
        // 显示当前选中的内容信息
        if segmentedControl.selectedSegmentIndex == 0 {
            // 总览模式
            highlights.append("🗺️ \(destinationCity)\(dayPlans.count)日游总览")
            highlights.append("📊 共\(totalAttractions)个精选景点，分\(dayPlans.count)天游览")
            highlights.append("🎨 每天独立路线，不同颜色标识区分")
            highlights.append("")
            
            // 每日景点统计和详细信息
            for dayPlan in dayPlans {
                guard !dayPlan.attractions.isEmpty else { continue }
                
                let dayColor = getDayColorEmoji(for: dayPlan.day)
                let attractionNames = dayPlan.attractions.prefix(3).map { $0.name }.joined(separator: "→")
                let moreText = dayPlan.attractions.count > 3 ? "等\(dayPlan.attractions.count)站" : ""
                
                highlights.append("\(dayColor) Day\(dayPlan.day) (\(dayPlan.attractions.count)站)")
                highlights.append("   \(attractionNames)\(moreText)")
            }
            
            highlights.append("")
            highlights.append("💡 点击Day按钮查看详细路线")
            
        } else {
            // 单日模式
            let dayIndex = segmentedControl.selectedSegmentIndex - 1
            if dayIndex < dayPlans.count {
                let dayPlan = dayPlans[dayIndex]
                let dayColor = getDayColorEmoji(for: dayPlan.day)
                
                highlights.append("\(dayColor) \(dayPlan.title)")
                highlights.append("📍 \(dayPlan.attractions.count)个景点按游览顺序连接")
                highlights.append("🚶‍♂️ 推荐步行或短途交通工具")
                highlights.append("")
                
                // 显示详细的景点顺序
                highlights.append("🗺️ 游览路线：")
                for (index, attraction) in dayPlan.attractions.enumerated() {
                    guard index < dayPlan.attractions.count else { break }
                    let arrow = index < dayPlan.attractions.count - 1 ? " → " : ""
                    highlights.append("\(index + 1). \(attraction.name)\(arrow)")
                }
                
                highlights.append("")
                highlights.append("💡 地图上显示当天景点标注和连接线")
            }
        }
        
        // 根据景点类型生成主题亮点
        let allAttractions = dayPlans.flatMap { $0.attractions }
        let attractionNames = allAttractions.map { $0.name.lowercased() }
        var themeHighlights: [String] = []
        
        // 检测主题
        if attractionNames.contains(where: { $0.contains("博物馆") || $0.contains("历史") || $0.contains("文化") || $0.contains("豫园") || $0.contains("城隍庙") }) {
            themeHighlights.append("🏛️ 历史文化深度体验")
        }
        
        if attractionNames.contains(where: { $0.contains("美食") || $0.contains("小吃") || $0.contains("餐厅") || $0.contains("云南南路") }) {
            themeHighlights.append("🍜 地道美食品鉴之旅")
        }
        
        if attractionNames.contains(where: { $0.contains("外滩") || $0.contains("陆家嘴") || $0.contains("东方明珠") || $0.contains("南京路") }) {
            themeHighlights.append("🏙️ 现代都市风情体验")
        }
        
        if attractionNames.contains(where: { $0.contains("公园") || $0.contains("动物园") || $0.contains("植物园") || $0.contains("自然") }) {
            themeHighlights.append("🌳 自然风光休闲游览")
        }
        
        if !themeHighlights.isEmpty {
            highlights.append("")
            highlights.append("✨ 行程特色：")
            highlights.append(contentsOf: themeHighlights)
        }
        
        let result = highlights.joined(separator: "\n")
        print("✅ 生成路线高亮完成，内容长度: \(result.count)")
        return result
    }
    
    private func getDayColorEmoji(for day: Int) -> String {
        let colorEmojis = ["🔵", "🟢", "🟠", "🟣", "🔴", "🔶", "🩷", "🟦"]
        let emojiIndex = max(0, min(day - 1, colorEmojis.count - 1))
        guard emojiIndex < colorEmojis.count else { return "🔵" }
        return colorEmojis[emojiIndex]
    }
    
    @objc private func planChanged() {
        // 方案切换功能已禁用，只显示当前方案
        print("⚠️ 方案切换已禁用，只显示当前方案")
        return
    }
    
    @objc private func segmentChanged() {
        print("🔄 段控制器改变: \(segmentedControl.selectedSegmentIndex)")
        
        updateDayPlanView()
        updateHighlightText() // 添加高亮文本更新
        
        // Update map annotations visibility
        updateMapAnnotations()
    }
    
    private func updateMapAnnotations() {
        for annotation in mapView.annotations {
            if let attractionAnnotation = annotation as? AttractionAnnotation {
                // Show/hide annotations based on selected segment
                if segmentedControl.selectedSegmentIndex == 0 {
                    // Show all
                    mapView.view(for: attractionAnnotation)?.isHidden = false
                } else {
                    // Show only selected day
                    let selectedDay = segmentedControl.selectedSegmentIndex
                    mapView.view(for: attractionAnnotation)?.isHidden = attractionAnnotation.day != selectedDay
                }
            }
        }
        
        // Also update route overlays visibility
        updateRouteOverlays()
    }
    
    private func updateRouteOverlays() {
        for overlay in mapView.overlays {
            if let routePolyline = overlay as? RoutePolyline {
                // Show/hide route overlays based on selected segment
                if segmentedControl.selectedSegmentIndex == 0 {
                    // Show all routes
                    if let renderer = mapView.renderer(for: overlay) as? MKPolylineRenderer {
                        renderer.alpha = 1.0
                    }
                } else {
                    // Show only selected day route
                    let selectedDay = segmentedControl.selectedSegmentIndex
                    if let renderer = mapView.renderer(for: overlay) as? MKPolylineRenderer {
                        renderer.alpha = routePolyline.day == selectedDay ? 1.0 : 0.2
                    }
                }
            }
        }
    }
    
    @objc private func backTapped() {
        print("返回按钮被点击 - 返回上一页面")
        
        // 确保在主线程执行
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if let navController = self.navigationController {
                // 先检查是否有可以返回的页面
                if navController.viewControllers.count > 1 {
                    navController.popViewController(animated: true)
                } else {
                    // 如果没有可以返回的页面，则使用dismiss
                    self.dismiss(animated: true, completion: nil)
                }
            } else {
                // 如果没有导航控制器，直接dismiss
                self.dismiss(animated: true, completion: nil)
            }
        }
    }
    
    @objc private func copyTapped() {
        // 获取当前行程计划
        guard currentPlanIndex < tripPlans.count else {
            let alert = UIAlertController(title: "错误", message: "未找到有效的行程信息", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
            return
        }
        
        let currentPlan = tripPlans[currentPlanIndex]
        
        // 创建行程记录
        let tripRecord = TripRecord(
            id: UUID().uuidString,
            title: currentPlan.title,
            destination: destinationCity,
            departure: departureCity,
            startDate: Date(),
            endDate: Calendar.current.date(byAdding: .day, value: currentPlan.dayCount, to: Date()) ?? Date(),
            dayCount: currentPlan.dayCount,
            attractions: currentPlan.attractions,
            currentDay: 1,
            status: .planning,
            createdDate: Date(),
            lastUpdated: Date()
        )
        
        // 保存到数据管理器
        TripDataManager.shared.saveTrip(tripRecord)
        
        let alert = UIAlertController(title: "行程已复制", message: "行程已成功复制到您的首页行程列表中", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
        
        print("💾 行程已复制到首页: \(tripRecord.title)")
    }
}

// MARK: - MKMapViewDelegate
extension TripMapViewController: MKMapViewDelegate {
    func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
        guard let attractionAnnotation = annotation as? AttractionAnnotation else { return nil }
        
        let identifier = "AttractionPin"
        var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier) as? MKMarkerAnnotationView
        
        if annotationView == nil {
            annotationView = MKMarkerAnnotationView(annotation: annotation, reuseIdentifier: identifier)
            annotationView?.canShowCallout = true
            
            // Add detail disclosure button
            let detailButton = UIButton(type: .detailDisclosure)
            annotationView?.rightCalloutAccessoryView = detailButton
        } else {
            annotationView?.annotation = annotation
        }
        
        // Customize based on day - 支持更多天数的颜色
        let dayColors: [UIColor] = [
            .systemBlue,     // Day 1
            .systemGreen,    // Day 2  
            .systemOrange,   // Day 3
            .systemPurple,   // Day 4
            .systemRed,      // Day 5
            .systemTeal,     // Day 6
            .systemPink,     // Day 7
            .systemIndigo    // Day 8+
        ]
        
        let colorIndex = min(attractionAnnotation.day - 1, dayColors.count - 1)
        annotationView?.markerTintColor = dayColors[colorIndex]
        
        annotationView?.glyphText = "\(attractionAnnotation.order)"
        
        return annotationView
    }
    
    func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
        if let routePolyline = overlay as? RoutePolyline {
            let renderer = MKPolylineRenderer(polyline: routePolyline.polyline)
            
            // 使用更鲜艳的颜色和更粗的线条
            let dayColors: [UIColor] = [
                UIColor.systemBlue.withAlphaComponent(0.9),     // Day 1 - 深蓝色
                UIColor.systemGreen.withAlphaComponent(0.9),    // Day 2 - 深绿色  
                UIColor.systemOrange.withAlphaComponent(0.9),   // Day 3 - 橙色
                UIColor.systemPurple.withAlphaComponent(0.9),   // Day 4 - 紫色
                UIColor.systemRed.withAlphaComponent(0.9),      // Day 5 - 红色
                UIColor.systemTeal.withAlphaComponent(0.9),     // Day 6 - 青色
                UIColor.systemPink.withAlphaComponent(0.9),     // Day 7 - 粉色
                UIColor.systemIndigo.withAlphaComponent(0.9)    // Day 8+ - 靛蓝色
            ]
            
            let colorIndex = min(routePolyline.day - 1, dayColors.count - 1)
            renderer.strokeColor = dayColors[colorIndex]
            
            // 更粗的线条和更明显的虚线效果
            renderer.lineWidth = 6.0
            renderer.lineDashPattern = [10, 8] // 更明显的虚线：实线10点，空白8点
            renderer.lineCap = .round  // 圆形线端
            renderer.lineJoin = .round // 圆形连接
            
            print("🎨 渲染Day\(routePolyline.day)路线: 颜色=\(dayColors[colorIndex]), 线宽=6.0")
            return renderer
            
        } else if let polyline = overlay as? MKPolyline {
            let renderer = MKPolylineRenderer(polyline: polyline)
            renderer.strokeColor = UIColor.systemRed.withAlphaComponent(0.7)
            renderer.lineWidth = 5.0
            renderer.lineCap = .round
            return renderer
        }
        
        return MKOverlayRenderer(overlay: overlay)
    }
    
    func mapView(_ mapView: MKMapView, annotationView view: MKAnnotationView, calloutAccessoryControlTapped control: UIControl) {
        guard let annotation = view.annotation as? AttractionAnnotation else { return }
        
        // Show attraction details
        let alert = UIAlertController(title: annotation.title, message: "景点类型: \(annotation.subtitle ?? "")\n游览顺序: 第\(annotation.order)站", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - Models
struct DayPlan {
    let day: Int
    let title: String
    let subtitle: String
    let attractions: [Attraction]
}

struct Attraction {
    let name: String
    let type: String
    let coordinate: CLLocationCoordinate2D
}

class AttractionAnnotation: NSObject, MKAnnotation {
    var coordinate: CLLocationCoordinate2D = CLLocationCoordinate2D()
    var title: String?
    var subtitle: String?
    var day: Int = 1
    var order: Int = 1
}

// MARK: - Custom Route Polyline
class RoutePolyline: NSObject, MKOverlay {
    let polyline: MKPolyline
    let day: Int
    
    init(polyline: MKPolyline, day: Int) {
        self.polyline = polyline
        self.day = day
        super.init()
    }
    
    var coordinate: CLLocationCoordinate2D {
        return polyline.coordinate
    }
    
    var boundingMapRect: MKMapRect {
        return polyline.boundingMapRect
    }
} 