import UIKit
import MapKit
import CoreLocation

class TrajectoryListViewController: UIViewController {
    
    // MARK: - Properties
    private let tableView = UITableView()
    private var trajectories: [TrajectoryRecord] = []
    private let refreshControl = UIRefreshControl()
    
    // 添加顶部统计视图
    private let statsView = UIView()
    private let statsStackView = UIStackView()
    private let totalTrajectoriesLabel = UILabel()
    private let totalDistanceLabel = UILabel()
    private let lastUpdatedLabel = UILabel()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadTrajectories()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "轨迹动画"
        view.backgroundColor = .systemBackground
        
        // 改进导航栏按钮
        navigationItem.rightBarButtonItems = [
            UIBarButtonItem(
                barButtonSystemItem: .add,
                target: self,
                action: #selector(addButtonTapped)
            ),
            UIBarButtonItem(
                image: UIImage(systemName: "gearshape"),
                style: .plain,
                target: self,
                action: #selector(settingsButtonTapped)
            )
        ]
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "square.and.arrow.down"),
            style: .plain,
            target: self,
            action: #selector(importButtonTapped)
        )
        
        // Setup stats view first, then table view
        setupStatsView()
        setupTableView()
    }
    
    private func setupStatsView() {
        statsView.backgroundColor = .secondarySystemBackground
        statsView.layer.cornerRadius = 12
        statsView.layer.shadowColor = UIColor.black.cgColor
        statsView.layer.shadowOffset = CGSize(width: 0, height: 2)
        statsView.layer.shadowOpacity = 0.1
        statsView.layer.shadowRadius = 4
        statsView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(statsView)
        
        // 配置统计标签
        totalTrajectoriesLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        totalTrajectoriesLabel.textColor = .label
        totalTrajectoriesLabel.textAlignment = .center
        
        totalDistanceLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        totalDistanceLabel.textColor = .label
        totalDistanceLabel.textAlignment = .center
        
        lastUpdatedLabel.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        lastUpdatedLabel.textColor = .secondaryLabel
        lastUpdatedLabel.textAlignment = .center
        
        // 设置堆栈视图
        statsStackView.axis = .horizontal
        statsStackView.distribution = .fillEqually
        statsStackView.spacing = 16
        statsStackView.translatesAutoresizingMaskIntoConstraints = false
        statsView.addSubview(statsStackView)
        
        statsStackView.addArrangedSubview(createStatContainer(label: totalTrajectoriesLabel, title: "轨迹数量"))
        statsStackView.addArrangedSubview(createStatContainer(label: totalDistanceLabel, title: "总距离"))
        
        lastUpdatedLabel.translatesAutoresizingMaskIntoConstraints = false
        statsView.addSubview(lastUpdatedLabel)
        
        NSLayoutConstraint.activate([
            statsView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 16),
            statsView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16),
            statsView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16),
            statsView.heightAnchor.constraint(equalToConstant: 80),
            
            statsStackView.topAnchor.constraint(equalTo: statsView.topAnchor, constant: 12),
            statsStackView.leadingAnchor.constraint(equalTo: statsView.leadingAnchor, constant: 16),
            statsStackView.trailingAnchor.constraint(equalTo: statsView.trailingAnchor, constant: -16),
            statsStackView.heightAnchor.constraint(equalToConstant: 40),
            
            lastUpdatedLabel.topAnchor.constraint(equalTo: statsStackView.bottomAnchor, constant: 4),
            lastUpdatedLabel.centerXAnchor.constraint(equalTo: statsView.centerXAnchor)
        ])
    }
    
    private func createStatContainer(label: UILabel, title: String) -> UIView {
        let container = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        titleLabel.textColor = .secondaryLabel
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        label.translatesAutoresizingMaskIntoConstraints = false
        
        container.addSubview(titleLabel)
        container.addSubview(label)
        
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            
            label.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 4),
            label.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            label.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            label.bottomAnchor.constraint(equalTo: container.bottomAnchor)
        ])
        
        return container
    }
    
    private func setupTableView() {
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(EnhancedTrajectoryCell.self, forCellReuseIdentifier: "EnhancedTrajectoryCell")
        tableView.separatorStyle = .none
        tableView.backgroundColor = .clear
        tableView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(tableView)
        
        // 添加下拉刷新
        refreshControl.addTarget(self, action: #selector(refreshData), for: .valueChanged)
        tableView.refreshControl = refreshControl
        
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: statsView.bottomAnchor, constant: 16),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor)
        ])
    }
    
    // MARK: - Data Loading
    private func loadTrajectories() {
        trajectories = TrajectoryDataManager.shared.getAllTrajectories()
        
        // If no trajectories, add sample data for testing
        if trajectories.isEmpty {
            addEnhancedSampleTrajectories()
        }
        
        updateStatsView()
        tableView.reloadData()
        refreshControl.endRefreshing()
    }
    
    @objc private func refreshData() {
        loadTrajectories()
    }
    
    private func updateStatsView() {
        totalTrajectoriesLabel.text = "\(trajectories.count)"
        
        let totalDistance = trajectories.reduce(0.0) { sum, trajectory in
            return sum + calculateTotalDistance(for: trajectory.points)
        }
        totalDistanceLabel.text = String(format: "%.1f公里", totalDistance / 1000)
        
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        lastUpdatedLabel.text = "更新于 \(formatter.string(from: Date()))"
    }
    
    private func calculateTotalDistance(for points: [CLLocationCoordinate2D]) -> Double {
        guard points.count > 1 else { return 0 }
        
        var totalDistance = 0.0
        for i in 0..<points.count - 1 {
            let location1 = CLLocation(latitude: points[i].latitude, longitude: points[i].longitude)
            let location2 = CLLocation(latitude: points[i + 1].latitude, longitude: points[i + 1].longitude)
            totalDistance += location1.distance(from: location2)
        }
        return totalDistance
    }
    
    private func addEnhancedSampleTrajectories() {
        // 更多样化的示例轨迹
        let sampleTrajectories = [
            createSampleTrajectory(
                name: "🇨🇳 北京-上海高铁",
                startCoordinate: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
                endCoordinate: CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737),
                pointCount: 50,
                transportMode: .transit,
                description: "京沪高铁全程1318公里"
            ),
            createSampleTrajectory(
                name: "🌊 珠江三角洲环游",
                startCoordinate: CLLocationCoordinate2D(latitude: 23.1291, longitude: 113.2644),
                endCoordinate: CLLocationCoordinate2D(latitude: 22.5431, longitude: 114.0579),
                pointCount: 30,
                transportMode: .driving,
                description: "广州-深圳-珠海环线"
            ),
            createSampleTrajectory(
                name: "🏔️ 丝绸之路西段",
                startCoordinate: CLLocationCoordinate2D(latitude: 34.2655, longitude: 108.9508),
                endCoordinate: CLLocationCoordinate2D(latitude: 43.8256, longitude: 87.6168),
                pointCount: 60,
                transportMode: .driving,
                description: "西安-乌鲁木齐古丝路"
            ),
            createSampleTrajectory(
                name: "🏃‍♂️ 杭州西湖跑步",
                startCoordinate: CLLocationCoordinate2D(latitude: 30.2489, longitude: 120.1292),
                endCoordinate: CLLocationCoordinate2D(latitude: 30.2489, longitude: 120.1292),
                pointCount: 40,
                transportMode: .running,
                isLoop: true,
                description: "西湖环湖跑步路线"
            ),
            createSampleTrajectory(
                name: "✈️ 京沪空中航线",
                startCoordinate: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
                endCoordinate: CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737),
                pointCount: 25,
                transportMode: .flying,
                description: "首都机场-浦东机场"
            )
        ]
        
        trajectories = sampleTrajectories
        
        // Save to data manager
        for trajectory in trajectories {
            TrajectoryDataManager.shared.saveTrajectory(trajectory)
        }
    }
    
    private func createSampleTrajectory(
        name: String,
        startCoordinate: CLLocationCoordinate2D,
        endCoordinate: CLLocationCoordinate2D,
        pointCount: Int,
        transportMode: TransportMode,
        isLoop: Bool = false,
        description: String = ""
    ) -> TrajectoryRecord {
        var points: [CLLocationCoordinate2D] = []
        
        // Add start point
        points.append(startCoordinate)
        
        // Generate intermediate points with more realistic paths
        for i in 1..<pointCount-1 {
            let ratio = Double(i) / Double(pointCount - 1)
            
            // 基础直线插值
            var lat = startCoordinate.latitude + (endCoordinate.latitude - startCoordinate.latitude) * ratio
            var lon = startCoordinate.longitude + (endCoordinate.longitude - startCoordinate.longitude) * ratio
            
            // 根据交通方式添加不同的路径特征
            switch transportMode {
            case .flying:
                // 飞行路径：更直接，轻微弧度
                let arcOffset = sin(ratio * .pi) * 0.5
                lat += arcOffset
            case .driving:
                // 驾车路径：遵循道路网络，更多曲折
                let roadVariance = (sin(ratio * .pi * 8) + cos(ratio * .pi * 6)) * 0.1
                lat += roadVariance
                lon += roadVariance * 0.5
            case .transit:
                // 公共交通：站点停靠模式
                if i % 8 == 0 { // 模拟车站停靠
                    let stationOffset = (Double.random(in: -1.0...1.0) * 0.05)
                    lat += stationOffset
                    lon += stationOffset
                }
            case .running, .walking:
                // 跑步/步行：更多随机变化
                let jogVariance = (Double.random(in: -1.0...1.0) * 0.02)
                lat += jogVariance
                lon += jogVariance
            default:
                // 默认轻微变化
                let variance = (Double.random(in: -1.0...1.0) * 0.01)
                lat += variance
                lon += variance
            }
            
            points.append(CLLocationCoordinate2D(latitude: lat, longitude: lon))
        }
        
        // Add end point
        points.append(endCoordinate)
        
        // If it's a loop, add return path
        if isLoop {
            let returnPoints = generateReturnPath(from: points)
            points.append(contentsOf: returnPoints)
        }
        
        // Create realistic time span based on transport mode
        let baseTime = Date().addingTimeInterval(-TimeInterval.random(in: 3600...86400)) // 1小时到1天前
        let duration = estimateDuration(for: points, transportMode: transportMode)
        let endTime = baseTime.addingTimeInterval(duration)
        
        return TrajectoryRecord(
            name: name,
            points: points,
            startTime: baseTime,
            endTime: endTime,
            transportModes: [transportMode]
        )
    }
    
    private func generateReturnPath(from outboundPath: [CLLocationCoordinate2D]) -> [CLLocationCoordinate2D] {
        var returnPath: [CLLocationCoordinate2D] = []
        
        // 从倒数第二个点开始，生成返回路径
        for i in stride(from: outboundPath.count - 2, through: 1, by: -1) {
            let originalPoint = outboundPath[i]
            // 添加轻微偏移以模拟不同的返回路径
            let offset = 0.001
            let newLat = originalPoint.latitude + Double.random(in: -offset...offset)
            let newLon = originalPoint.longitude + Double.random(in: -offset...offset)
            returnPath.append(CLLocationCoordinate2D(latitude: newLat, longitude: newLon))
        }
        
        return returnPath
    }
    
    private func estimateDuration(for points: [CLLocationCoordinate2D], transportMode: TransportMode) -> TimeInterval {
        let distance = calculateTotalDistance(for: points)
        
        let avgSpeed: Double // 米/秒
        switch transportMode {
        case .walking:
            avgSpeed = 1.4 // 5 km/h
        case .running:
            avgSpeed = 3.0 // 10.8 km/h
        case .cycling:
            avgSpeed = 5.6 // 20 km/h
        case .driving:
            avgSpeed = 16.7 // 60 km/h
        case .transit:
            avgSpeed = 11.1 // 40 km/h
        case .flying:
            avgSpeed = 250 // 900 km/h
        case .unknown:
            avgSpeed = 5.0
        }
        
        return distance / avgSpeed
    }
    
    // MARK: - Actions
    @objc private func addButtonTapped() {
        let actionSheet = UIAlertController(title: "添加轨迹", message: "选择创建方式", preferredStyle: .actionSheet)
        
        actionSheet.addAction(UIAlertAction(title: "从运动记录导入", style: .default) { [weak self] _ in
            self?.importFromExerciseRecords()
        })
        
        actionSheet.addAction(UIAlertAction(title: "手动创建轨迹", style: .default) { [weak self] _ in
            self?.createCustomTrajectory()
        })
        
        actionSheet.addAction(UIAlertAction(title: "添加示例轨迹", style: .default) { [weak self] _ in
            self?.addMoreSampleTrajectories()
        })
        
        actionSheet.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        if let popover = actionSheet.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItems?.first
        }
        
        present(actionSheet, animated: true)
    }
    
    @objc private func settingsButtonTapped() {
        let settingsVC = TrajectorySettingsViewController()
        let navController = UINavigationController(rootViewController: settingsVC)
        present(navController, animated: true)
    }
    
    @objc private func importButtonTapped() {
        let alert = UIAlertController(title: "导入轨迹", message: "导入功能开发中", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    private func importFromExerciseRecords() {
        let exerciseRecords = ExerciseDataManager.shared.getExerciseRecords()
        let recordsWithTrajectory = exerciseRecords.filter { !$0.trajectory.isEmpty }
        
        if recordsWithTrajectory.isEmpty {
            showAlert(title: "无可用记录", message: "没有找到包含轨迹数据的运动记录")
            return
        }
        
        let importAlert = UIAlertController(title: "选择运动记录", message: "选择要转换为轨迹动画的运动记录", preferredStyle: .actionSheet)
        
        for record in recordsWithTrajectory.prefix(5) { // 只显示前5条
            let title = "\(record.type.displayName) - \(record.formattedDistance)"
            importAlert.addAction(UIAlertAction(title: title, style: .default) { [weak self] _ in
                self?.convertExerciseToTrajectory(record)
            })
        }
        
        importAlert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        if let popover = importAlert.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItems?.first
        }
        
        present(importAlert, animated: true)
    }
    
    private func convertExerciseToTrajectory(_ record: ExerciseRecord) {
        let points = record.trajectory.map { $0.coordinate.clLocationCoordinate }
        
        let trajectory = TrajectoryRecord(
            name: "🏃‍♂️ \(record.type.displayName) - \(record.formattedDistance)",
            points: points,
            startTime: record.startTime,
            endTime: record.endTime ?? record.startTime.addingTimeInterval(record.duration),
            transportModes: [convertExerciseTypeToTransportMode(record.type)]
        )
        
        TrajectoryDataManager.shared.saveTrajectory(trajectory)
        loadTrajectories()
        
        showAlert(title: "导入成功", message: "运动记录已转换为轨迹动画")
    }
    
    private func convertExerciseTypeToTransportMode(_ exerciseType: ExerciseType) -> TransportMode {
        switch exerciseType {
        case .running:
            return .running
        case .walking:
            return .walking
        case .cycling:
            return .cycling
        default:
            return .walking
        }
    }
    
    private func createCustomTrajectory() {
        let alert = UIAlertController(title: "手动创建", message: "该功能将允许您在地图上点击创建自定义轨迹路径", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "开发中", style: .default))
        present(alert, animated: true)
    }
    
    private func addMoreSampleTrajectories() {
        // 添加更多有趣的示例轨迹
        let moreTrajectories = [
            createSampleTrajectory(
                name: "🚁 环岛直升机观光",
                startCoordinate: CLLocationCoordinate2D(latitude: 24.4477, longitude: 118.0890),
                endCoordinate: CLLocationCoordinate2D(latitude: 24.4477, longitude: 118.0890),
                pointCount: 20,
                transportMode: .flying,
                isLoop: true,
                description: "厦门环岛空中游览"
            ),
            createSampleTrajectory(
                name: "🚢 长江三峡游轮",
                startCoordinate: CLLocationCoordinate2D(latitude: 30.7125, longitude: 111.2900),
                endCoordinate: CLLocationCoordinate2D(latitude: 31.0447, longitude: 109.5996),
                pointCount: 35,
                transportMode: .transit,
                description: "宜昌-重庆长江游"
            )
        ]
        
        for trajectory in moreTrajectories {
            TrajectoryDataManager.shared.saveTrajectory(trajectory)
        }
        
        loadTrajectories()
        showAlert(title: "添加成功", message: "已添加 \(moreTrajectories.count) 条新轨迹")
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension TrajectoryListViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return trajectories.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "EnhancedTrajectoryCell", for: indexPath) as! EnhancedTrajectoryCell
        cell.configure(with: trajectories[indexPath.row])
        return cell
    }
}

// MARK: - UITableViewDelegate
extension TrajectoryListViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 120 // 增加高度以容纳更多信息
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let trajectory = trajectories[indexPath.row]
        let animationVC = TrajectoryAnimationViewController()
        animationVC.loadTrajectoryData(trajectory: trajectory)
        navigationController?.pushViewController(animationVC, animated: true)
    }
    
    func tableView(_ tableView: UITableView, trailingSwipeActionsConfigurationForRowAt indexPath: IndexPath) -> UISwipeActionsConfiguration? {
        let deleteAction = UIContextualAction(style: .destructive, title: "删除") { [weak self] (_, _, completion) in
            guard let self = self else { return }
            
            let trajectory = self.trajectories[indexPath.row]
            
            // Delete from data manager
            TrajectoryDataManager.shared.deleteTrajectory(withId: trajectory.id)
            
            // Update local data and table view
            self.trajectories.remove(at: indexPath.row)
            tableView.deleteRows(at: [indexPath], with: .automatic)
            self.updateStatsView()
            
            completion(true)
        }
        
        let shareAction = UIContextualAction(style: .normal, title: "分享") { [weak self] (_, _, completion) in
            self?.shareTrajectory(at: indexPath.row)
            completion(true)
        }
        shareAction.backgroundColor = .systemBlue
        
        let configuration = UISwipeActionsConfiguration(actions: [deleteAction, shareAction])
        return configuration
    }
    
    private func shareTrajectory(at index: Int) {
        let trajectory = trajectories[index]
        let shareText = "查看我的轨迹：\(trajectory.name)\n距离：\(String(format: "%.1f", calculateTotalDistance(for: trajectory.points) / 1000))公里"
        
        let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)
        if let popover = activityVC.popoverPresentationController {
            popover.sourceView = tableView.cellForRow(at: IndexPath(row: index, section: 0))
        }
        present(activityVC, animated: true)
    }
}

// MARK: - Enhanced TrajectoryCell
class EnhancedTrajectoryCell: UITableViewCell {
    
    private let containerView = UIView()
    private let thumbnailView = UIView()
    private let nameLabel = UILabel()
    private let descriptionLabel = UILabel()
    private let dateLabel = UILabel()
    private let distanceLabel = UILabel()
    private let transportLabel = UILabel()
    private let durationLabel = UILabel()
    private let playButton = UIButton()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        // 容器视图
        containerView.backgroundColor = .secondarySystemBackground
        containerView.layer.cornerRadius = 12
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowOpacity = 0.1
        containerView.layer.shadowRadius = 4
        containerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(containerView)
        
        // 缩略图视图
        thumbnailView.backgroundColor = .systemGray5
        thumbnailView.layer.cornerRadius = 8
        thumbnailView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(thumbnailView)
        
        // 标签配置
        nameLabel.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        nameLabel.textColor = .label
        nameLabel.numberOfLines = 1
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(nameLabel)
        
        descriptionLabel.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        descriptionLabel.textColor = .secondaryLabel
        descriptionLabel.numberOfLines = 2
        descriptionLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(descriptionLabel)
        
        dateLabel.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        dateLabel.textColor = .secondaryLabel
        dateLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(dateLabel)
        
        distanceLabel.font = UIFont.systemFont(ofSize: 14, weight: .semibold)
        distanceLabel.textColor = .systemBlue
        distanceLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(distanceLabel)
        
        transportLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        transportLabel.textColor = .systemGreen
        transportLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(transportLabel)
        
        durationLabel.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        durationLabel.textColor = .secondaryLabel
        durationLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(durationLabel)
        
        // 播放按钮
        playButton.setImage(UIImage(systemName: "play.circle.fill"), for: .normal)
        playButton.tintColor = .systemBlue
        playButton.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(playButton)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),
            
            thumbnailView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 12),
            thumbnailView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 12),
            thumbnailView.widthAnchor.constraint(equalToConstant: 60),
            thumbnailView.heightAnchor.constraint(equalToConstant: 60),
            
            nameLabel.leadingAnchor.constraint(equalTo: thumbnailView.trailingAnchor, constant: 12),
            nameLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 12),
            nameLabel.trailingAnchor.constraint(equalTo: playButton.leadingAnchor, constant: -8),
            
            descriptionLabel.leadingAnchor.constraint(equalTo: nameLabel.leadingAnchor),
            descriptionLabel.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 4),
            descriptionLabel.trailingAnchor.constraint(equalTo: nameLabel.trailingAnchor),
            
            dateLabel.leadingAnchor.constraint(equalTo: nameLabel.leadingAnchor),
            dateLabel.topAnchor.constraint(equalTo: descriptionLabel.bottomAnchor, constant: 8),
            
            distanceLabel.leadingAnchor.constraint(equalTo: dateLabel.trailingAnchor, constant: 16),
            distanceLabel.centerYAnchor.constraint(equalTo: dateLabel.centerYAnchor),
            
            transportLabel.leadingAnchor.constraint(equalTo: distanceLabel.trailingAnchor, constant: 16),
            transportLabel.centerYAnchor.constraint(equalTo: dateLabel.centerYAnchor),
            
            durationLabel.leadingAnchor.constraint(equalTo: nameLabel.leadingAnchor),
            durationLabel.topAnchor.constraint(equalTo: dateLabel.bottomAnchor, constant: 4),
            
            playButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -12),
            playButton.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            playButton.widthAnchor.constraint(equalToConstant: 30),
            playButton.heightAnchor.constraint(equalToConstant: 30)
        ])
    }
    
    func configure(with trajectory: TrajectoryRecord) {
        nameLabel.text = trajectory.name
        
        // Format date
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .short
        dateFormatter.timeStyle = .none
        dateLabel.text = dateFormatter.string(from: trajectory.startTime)
        
        // Calculate distance
        let distance = calculateDistance(for: trajectory.points)
        distanceLabel.text = String(format: "%.1f公里", distance / 1000)
        
        // Display transport mode
        transportLabel.text = getTransportModeText(trajectory.transportModes.first ?? .unknown)
        
        // Calculate duration
        let duration = trajectory.endTime.timeIntervalSince(trajectory.startTime)
        durationLabel.text = formatDuration(duration)
        
        // 生成简单描述
        let pointCount = trajectory.points.count
        descriptionLabel.text = "包含 \(pointCount) 个轨迹点"
        
        // Draw path in thumbnail
        drawPathInThumbnail(for: trajectory.points)
    }
    
    private func calculateDistance(for points: [CLLocationCoordinate2D]) -> Double {
        guard points.count > 1 else { return 0 }
        
        var totalDistance = 0.0
        
        for i in 0..<points.count - 1 {
            let location1 = CLLocation(latitude: points[i].latitude, longitude: points[i].longitude)
            let location2 = CLLocation(latitude: points[i + 1].latitude, longitude: points[i + 1].longitude)
            
            totalDistance += location1.distance(from: location2)
        }
        
        return totalDistance
    }
    
    private func getTransportModeText(_ mode: TransportMode) -> String {
        switch mode {
        case .walking: return "步行"
        case .running: return "跑步"
        case .cycling: return "骑行"
        case .driving: return "驾车"
        case .transit: return "公交"
        case .flying: return "飞行"
        case .unknown: return "未知"
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        
        if hours > 0 {
            return "\(hours)小时\(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
    
    private func drawPathInThumbnail(for points: [CLLocationCoordinate2D]) {
        guard !points.isEmpty else { return }
        
        thumbnailView.layer.sublayers?.removeAll()
        
        // Convert coordinates to thumbnail coordinate system
        let thumbnailSize = thumbnailView.bounds.size
        guard thumbnailSize.width > 0 && thumbnailSize.height > 0 else { return }
        
        // Find bounding box
        var minLat = points[0].latitude
        var maxLat = points[0].latitude
        var minLon = points[0].longitude
        var maxLon = points[0].longitude
        
        for point in points {
            minLat = min(minLat, point.latitude)
            maxLat = max(maxLat, point.latitude)
            minLon = min(minLon, point.longitude)
            maxLon = max(maxLon, point.longitude)
        }
        
        // Add padding
        let latPadding = (maxLat - minLat) * 0.1
        let lonPadding = (maxLon - minLon) * 0.1
        
        minLat -= latPadding
        maxLat += latPadding
        minLon -= lonPadding
        maxLon += lonPadding
        
        // Create path
        let path = UIBezierPath()
        
        for (index, point) in points.enumerated() {
            let x = (point.longitude - minLon) / (maxLon - minLon) * thumbnailSize.width
            let y = (1 - (point.latitude - minLat) / (maxLat - minLat)) * thumbnailSize.height
            
            let cgPoint = CGPoint(x: x, y: y)
            
            if index == 0 {
                path.move(to: cgPoint)
            } else {
                path.addLine(to: cgPoint)
            }
        }
        
        // Create shape layer
        let shapeLayer = CAShapeLayer()
        shapeLayer.path = path.cgPath
        shapeLayer.strokeColor = UIColor.systemBlue.cgColor
        shapeLayer.fillColor = UIColor.clear.cgColor
        shapeLayer.lineWidth = 2.0
        shapeLayer.lineCap = .round
        shapeLayer.lineJoin = .round
        
        thumbnailView.layer.addSublayer(shapeLayer)
        
        // Add start/end markers
        if points.count > 1 {
            // Start marker
            let startPoint = points[0]
            let startX = (startPoint.longitude - minLon) / (maxLon - minLon) * thumbnailSize.width
            let startY = (1 - (startPoint.latitude - minLat) / (maxLat - minLat)) * thumbnailSize.height
            
            let startMarker = CALayer()
            startMarker.backgroundColor = UIColor.systemGreen.cgColor
            startMarker.cornerRadius = 3
            startMarker.frame = CGRect(x: startX - 3, y: startY - 3, width: 6, height: 6)
            thumbnailView.layer.addSublayer(startMarker)
            
            // End marker
            let endPoint = points[points.count - 1]
            let endX = (endPoint.longitude - minLon) / (maxLon - minLon) * thumbnailSize.width
            let endY = (1 - (endPoint.latitude - minLat) / (maxLat - minLat)) * thumbnailSize.height
            
            let endMarker = CALayer()
            endMarker.backgroundColor = UIColor.systemRed.cgColor
            endMarker.cornerRadius = 3
            endMarker.frame = CGRect(x: endX - 3, y: endY - 3, width: 6, height: 6)
            thumbnailView.layer.addSublayer(endMarker)
        }
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        // Redraw path when layout changes
        if let trajectory = nameLabel.text {
            // Find the trajectory by name (not ideal, but works for this example)
            if let trajectory = TrajectoryDataManager.shared.getAllTrajectories().first(where: { $0.name == trajectory }) {
                drawPathInThumbnail(for: trajectory.points)
            }
        }
    }
}
