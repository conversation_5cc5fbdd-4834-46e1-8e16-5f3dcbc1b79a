import Foundation
import CoreLocation
import Photos
import UIKit

class GlobalLifeDataManager: NSObject {
    static let shared = GlobalLifeDataManager()
    
    private var continents: [Continent] = []
    private var countries: [Country] = []
    private var provinces: [Province] = []
    private var cities: [City] = []
    private var visitedRegions: Set<String> = []
    
    private let userDefaults = UserDefaults.standard
    private let visitedRegionsKey = "VisitedRegions"
    private let manualMarksKey = "ManualMarks"
    
    // 位置管理器
    private let locationManager = CLLocationManager()
    private var locationCompletion: ((CLLocation?) -> Void)?
    
    private override init() {
        super.init()
        setupLocationManager()
        loadData()
        loadVisitedRegions()
    }
    
    // MARK: - 位置管理器设置
    private func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        locationManager.distanceFilter = 10
    }
    
    private func requestLocationPermission() {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .denied, .restricted:
            print("位置权限被拒绝")
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.startUpdatingLocation()
        @unknown default:
            break
        }
    }
    
    // MARK: - 数据加载
    private func loadData() {
        loadChinaData()
        loadWorldData()
    }
    
    private func loadChinaData() {
        // 中国省份和城市数据
        let chinaProvinces = [
            createProvince(id: "beijing", name: "北京", cities: ["北京市"]),
            createProvince(id: "tianjin", name: "天津", cities: ["天津市"]),
            createProvince(id: "hebei", name: "河北", cities: ["石家庄市", "唐山市", "秦皇岛市", "邯郸市", "邢台市", "保定市", "张家口市", "承德市", "沧州市", "廊坊市", "衡水市"]),
            createProvince(id: "shanxi", name: "山西", cities: ["太原市", "大同市", "阳泉市", "长治市", "晋城市", "朔州市", "晋中市", "运城市", "忻州市", "临汾市", "吕梁市"]),
            createProvince(id: "neimenggu", name: "内蒙古", cities: ["呼和浩特市", "包头市", "乌海市", "赤峰市", "通辽市", "鄂尔多斯市", "呼伦贝尔市", "巴彦淖尔市", "乌兰察布市"]),
            createProvince(id: "liaoning", name: "辽宁", cities: ["沈阳市", "大连市", "鞍山市", "抚顺市", "本溪市", "丹东市", "锦州市", "营口市", "阜新市", "辽阳市", "盘锦市", "铁岭市", "朝阳市", "葫芦岛市"]),
            createProvince(id: "jilin", name: "吉林", cities: ["长春市", "吉林市", "四平市", "辽源市", "通化市", "白山市", "松原市", "白城市"]),
            createProvince(id: "heilongjiang", name: "黑龙江", cities: ["哈尔滨市", "齐齐哈尔市", "鸡西市", "鹤岗市", "双鸭山市", "大庆市", "伊春市", "佳木斯市", "七台河市", "牡丹江市", "黑河市", "绥化市"]),
            createProvince(id: "shanghai", name: "上海", cities: ["上海市"]),
            createProvince(id: "jiangsu", name: "江苏", cities: ["南京市", "无锡市", "徐州市", "常州市", "苏州市", "南通市", "连云港市", "淮安市", "盐城市", "扬州市", "镇江市", "泰州市", "宿迁市"]),
            createProvince(id: "zhejiang", name: "浙江", cities: ["杭州市", "宁波市", "温州市", "嘉兴市", "湖州市", "绍兴市", "金华市", "衢州市", "舟山市", "台州市", "丽水市"]),
            createProvince(id: "anhui", name: "安徽", cities: ["合肥市", "芜湖市", "蚌埠市", "淮南市", "马鞍山市", "淮北市", "铜陵市", "安庆市", "黄山市", "滁州市", "阜阳市", "宿州市", "六安市", "亳州市", "池州市", "宣城市"]),
            createProvince(id: "fujian", name: "福建", cities: ["福州市", "厦门市", "莆田市", "三明市", "泉州市", "漳州市", "南平市", "龙岩市", "宁德市"]),
            createProvince(id: "jiangxi", name: "江西", cities: ["南昌市", "景德镇市", "萍乡市", "九江市", "新余市", "鹰潭市", "赣州市", "吉安市", "宜春市", "抚州市", "上饶市"]),
            createProvince(id: "shandong", name: "山东", cities: ["济南市", "青岛市", "淄博市", "枣庄市", "东营市", "烟台市", "潍坊市", "济宁市", "泰安市", "威海市", "日照市", "临沂市", "德州市", "聊城市", "滨州市", "菏泽市"]),
            createProvince(id: "henan", name: "河南", cities: ["郑州市", "开封市", "洛阳市", "平顶山市", "安阳市", "鹤壁市", "新乡市", "焦作市", "濮阳市", "许昌市", "漯河市", "三门峡市", "南阳市", "商丘市", "信阳市", "周口市", "驻马店市"]),
            createProvince(id: "hubei", name: "湖北", cities: ["武汉市", "黄石市", "十堰市", "宜昌市", "襄阳市", "鄂州市", "荆门市", "孝感市", "荆州市", "黄冈市", "咸宁市", "随州市"]),
            createProvince(id: "hunan", name: "湖南", cities: ["长沙市", "株洲市", "湘潭市", "衡阳市", "邵阳市", "岳阳市", "常德市", "张家界市", "益阳市", "郴州市", "永州市", "怀化市", "娄底市"]),
            createProvince(id: "guangdong", name: "广东", cities: ["广州市", "韶关市", "深圳市", "珠海市", "汕头市", "佛山市", "江门市", "湛江市", "茂名市", "肇庆市", "惠州市", "梅州市", "汕尾市", "河源市", "阳江市", "清远市", "东莞市", "中山市", "潮州市", "揭阳市", "云浮市"]),
            createProvince(id: "guangxi", name: "广西", cities: ["南宁市", "柳州市", "桂林市", "梧州市", "北海市", "防城港市", "钦州市", "贵港市", "玉林市", "百色市", "贺州市", "河池市", "来宾市", "崇左市"]),
            createProvince(id: "hainan", name: "海南", cities: ["海口市", "三亚市", "三沙市", "儋州市"]),
            createProvince(id: "chongqing", name: "重庆", cities: ["重庆市"]),
            createProvince(id: "sichuan", name: "四川", cities: ["成都市", "自贡市", "攀枝花市", "泸州市", "德阳市", "绵阳市", "广元市", "遂宁市", "内江市", "乐山市", "南充市", "眉山市", "宜宾市", "广安市", "达州市", "雅安市", "巴中市", "资阳市"]),
            createProvince(id: "guizhou", name: "贵州", cities: ["贵阳市", "六盘水市", "遵义市", "安顺市", "毕节市", "铜仁市"]),
            createProvince(id: "yunnan", name: "云南", cities: ["昆明市", "曲靖市", "玉溪市", "保山市", "昭通市", "丽江市", "普洱市", "临沧市"]),
            createProvince(id: "xizang", name: "西藏", cities: ["拉萨市", "日喀则市", "昌都市", "林芝市", "山南市", "那曲市", "阿里地区"]),
            createProvince(id: "shaanxi", name: "陕西", cities: ["西安市", "铜川市", "宝鸡市", "咸阳市", "渭南市", "延安市", "汉中市", "榆林市", "安康市", "商洛市"]),
            createProvince(id: "gansu", name: "甘肃", cities: ["兰州市", "嘉峪关市", "金昌市", "白银市", "天水市", "武威市", "张掖市", "平凉市", "酒泉市", "庆阳市", "定西市", "陇南市"]),
            createProvince(id: "qinghai", name: "青海", cities: ["西宁市", "海东市"]),
            createProvince(id: "ningxia", name: "宁夏", cities: ["银川市", "石嘴山市", "吴忠市", "固原市", "中卫市"]),
            createProvince(id: "xinjiang", name: "新疆", cities: ["乌鲁木齐市", "克拉玛依市", "吐鲁番市", "哈密市"]),
            createProvince(id: "taiwan", name: "台湾", cities: ["台北市", "高雄市", "台中市", "台南市", "新北市", "桃园市"]),
            createProvince(id: "hongkong", name: "香港", cities: ["香港"]),
            createProvince(id: "macau", name: "澳门", cities: ["澳门"])
        ]
        
        self.provinces = chinaProvinces
    }
    
    private func createProvince(id: String, name: String, cities: [String]) -> Province {
        let provinceCities = cities.enumerated().map { index, cityName in
            City(
                id: "\(id)_city_\(index)",
                name: cityName,
                provinceId: id,
                countryId: "china",
                coordinate: generateCoordinateForCity(cityName),
                isVisited: visitedRegions.contains("\(id)_city_\(index)"),
                visitDate: nil,
                visitSource: nil,
                photoCount: 0,
                stayDuration: nil
            )
        }
        
        // 使用真实的省份边界坐标
        let realCoordinates = ProvinceGeometry.shared.getProvinceCoordinates(for: id)
        
        return Province(
            id: id,
            name: name,
            countryId: "china",
            cities: provinceCities,
            visitedCount: provinceCities.filter { $0.isVisited }.count,
            totalCount: provinceCities.count,
            coordinates: realCoordinates,
            center: generateProvinceCenter(name),
            isVisited: visitedRegions.contains(id),
            visitSource: nil
        )
    }
    
    private func loadWorldData() {
        // 加载世界其他国家数据
        let worldCountries = [
            createCountry(id: "usa", name: "美国", code: "US"),
            createCountry(id: "japan", name: "日本", code: "JP"),
            createCountry(id: "korea", name: "韩国", code: "KR"),
            createCountry(id: "uk", name: "英国", code: "GB"),
            createCountry(id: "france", name: "法国", code: "FR"),
            createCountry(id: "germany", name: "德国", code: "DE"),
            createCountry(id: "italy", name: "意大利", code: "IT"),
            createCountry(id: "spain", name: "西班牙", code: "ES"),
            createCountry(id: "australia", name: "澳大利亚", code: "AU"),
            createCountry(id: "canada", name: "加拿大", code: "CA"),
            createCountry(id: "russia", name: "俄罗斯", code: "RU"),
            createCountry(id: "india", name: "印度", code: "IN"),
            createCountry(id: "brazil", name: "巴西", code: "BR"),
            createCountry(id: "thailand", name: "泰国", code: "TH"),
            createCountry(id: "singapore", name: "新加坡", code: "SG"),
            createCountry(id: "malaysia", name: "马来西亚", code: "MY")
        ]
        
        self.countries = worldCountries
    }
    
    private func createCountry(id: String, name: String, code: String) -> Country {
        let cities = generateCitiesForCountry(id, name)
        return Country(
            id: id,
            name: name,
            code: code,
            provinces: [],
            cities: cities,
            visitedCount: cities.filter { $0.isVisited }.count,
            totalCount: cities.count,
            coordinates: generateCountryCoordinates(id),
            center: generateCountryCenter(id)
        )
    }
    
    // MARK: - 坐标生成（简化版本，实际应用中需要真实的地理数据）
    private func generateCoordinateForCity(_ cityName: String) -> CLLocationCoordinate2D {
        // 这里是简化的坐标生成，实际应用中应该使用真实的城市坐标
        let cityCoordinates: [String: CLLocationCoordinate2D] = [
            "北京市": CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
            "上海市": CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737),
            "广州市": CLLocationCoordinate2D(latitude: 23.1291, longitude: 113.2644),
            "深圳市": CLLocationCoordinate2D(latitude: 22.5431, longitude: 114.0579),
            "天津市": CLLocationCoordinate2D(latitude: 39.3434, longitude: 117.3616),
            "重庆市": CLLocationCoordinate2D(latitude: 29.5647, longitude: 106.5507),
            "成都市": CLLocationCoordinate2D(latitude: 30.5728, longitude: 104.0668),
            "西安市": CLLocationCoordinate2D(latitude: 34.3416, longitude: 108.9398),
            "杭州市": CLLocationCoordinate2D(latitude: 30.2741, longitude: 120.1551),
            "武汉市": CLLocationCoordinate2D(latitude: 30.5928, longitude: 114.3055)
        ]
        
        return cityCoordinates[cityName] ?? CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
    }
    
    private func generateProvinceCoordinates(_ provinceName: String) -> [CLLocationCoordinate2D] {
        // 使用ProvinceGeometry中的真实地理边界数据
        let provinceIdMap: [String: String] = [
            "北京": "beijing",
            "天津": "tianjin", 
            "河北": "hebei",
            "山西": "shanxi",
            "内蒙古": "neimenggu",
            "辽宁": "liaoning",
            "吉林": "jilin",
            "黑龙江": "heilongjiang",
            "上海": "shanghai",
            "江苏": "jiangsu",
            "浙江": "zhejiang",
            "安徽": "anhui",
            "福建": "fujian",
            "江西": "jiangxi",
            "山东": "shandong",
            "河南": "henan",
            "湖北": "hubei",
            "湖南": "hunan",
            "广东": "guangdong",
            "广西": "guangxi",
            "海南": "hainan",
            "重庆": "chongqing",
            "四川": "sichuan",
            "贵州": "guizhou",
            "云南": "yunnan",
            "西藏": "xizang",
            "陕西": "shaanxi",
            "甘肃": "gansu",
            "青海": "qinghai",
            "宁夏": "ningxia",
            "新疆": "xinjiang",
            "台湾": "taiwan",
            "香港": "hongkong",
            "澳门": "macau"
        ]
        
        if let provinceId = provinceIdMap[provinceName] {
            return ProvinceGeometry.shared.getProvinceCoordinates(for: provinceId)
        }
        
        return []
    }
    
    private func generateProvinceCenter(_ provinceName: String) -> CLLocationCoordinate2D {
        // 简化的省份中心坐标
        let provinceCoordinates: [String: CLLocationCoordinate2D] = [
            "北京": CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
            "上海": CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737),
            "广东": CLLocationCoordinate2D(latitude: 23.1291, longitude: 113.2644),
            "江苏": CLLocationCoordinate2D(latitude: 32.0617, longitude: 118.7778),
            "浙江": CLLocationCoordinate2D(latitude: 30.2741, longitude: 120.1551),
            "山东": CLLocationCoordinate2D(latitude: 36.6758, longitude: 117.0009),
            "河北": CLLocationCoordinate2D(latitude: 38.0428, longitude: 114.5149),
            "河南": CLLocationCoordinate2D(latitude: 34.7466, longitude: 113.6254),
            "四川": CLLocationCoordinate2D(latitude: 30.5728, longitude: 104.0668),
            "湖北": CLLocationCoordinate2D(latitude: 30.5928, longitude: 114.3055)
        ]
        
        return provinceCoordinates[provinceName] ?? CLLocationCoordinate2D(latitude: 35.0, longitude: 105.0)
    }
    
    private func generateCountryCoordinates(_ countryId: String) -> [CLLocationCoordinate2D] {
        // 简化的国家边界坐标
        return []
    }
    
    private func generateCountryCenter(_ countryId: String) -> CLLocationCoordinate2D {
        let countryCoordinates: [String: CLLocationCoordinate2D] = [
            "usa": CLLocationCoordinate2D(latitude: 39.8283, longitude: -98.5795),
            "japan": CLLocationCoordinate2D(latitude: 36.2048, longitude: 138.2529),
            "korea": CLLocationCoordinate2D(latitude: 35.9078, longitude: 127.7669),
            "uk": CLLocationCoordinate2D(latitude: 55.3781, longitude: -3.4360),
            "france": CLLocationCoordinate2D(latitude: 46.2276, longitude: 2.2137),
            "germany": CLLocationCoordinate2D(latitude: 51.1657, longitude: 10.4515),
            "australia": CLLocationCoordinate2D(latitude: -25.2744, longitude: 133.7751)
        ]
        
        return countryCoordinates[countryId] ?? CLLocationCoordinate2D(latitude: 0, longitude: 0)
    }
    
    private func generateCitiesForCountry(_ countryId: String, _ countryName: String) -> [City] {
        // 简化的国家城市数据
        let citiesData: [String: [String]] = [
            "usa": ["纽约", "洛杉矶", "芝加哥", "休斯顿", "费城"],
            "japan": ["东京", "大阪", "京都", "名古屋", "福冈"],
            "korea": ["首尔", "釜山", "大邱", "仁川", "光州"],
            "uk": ["伦敦", "曼彻斯特", "伯明翰", "利物浦", "爱丁堡"],
            "france": ["巴黎", "马赛", "里昂", "图卢兹", "尼斯"],
            "germany": ["柏林", "汉堡", "慕尼黑", "科隆", "法兰克福"],
            "australia": ["悉尼", "墨尔本", "布里斯班", "珀斯", "阿德莱德"]
        ]
        
        let cityNames = citiesData[countryId] ?? [countryName]
        return cityNames.enumerated().map { index, cityName in
            City(
                id: "\(countryId)_city_\(index)",
                name: cityName,
                provinceId: "",
                countryId: countryId,
                coordinate: generateCoordinateForCity(cityName),
                isVisited: visitedRegions.contains("\(countryId)_city_\(index)"),
                visitDate: nil,
                visitSource: nil,
                photoCount: 0,
                stayDuration: nil
            )
        }
    }
    
    // MARK: - 数据持久化
    private func loadVisitedRegions() {
        if let data = userDefaults.data(forKey: visitedRegionsKey),
           let regions = try? JSONDecoder().decode(Set<String>.self, from: data) {
            visitedRegions = regions
        }
    }
    
    private func saveVisitedRegions() {
        if let data = try? JSONEncoder().encode(visitedRegions) {
            userDefaults.set(data, forKey: visitedRegionsKey)
        }
    }
    
    // MARK: - 公共接口
    func getProvinces() -> [Province] {
        return provinces
    }
    
    func getCountries() -> [Country] {
        return countries
    }
    
    func getCities(for provinceId: String) -> [City] {
        return provinces.first { $0.id == provinceId }?.cities ?? []
    }
    
    func markRegionAsVisited(_ regionId: String, source: VisitSource) {
        visitedRegions.insert(regionId)
        
        // 如果标记的是城市，也要标记对应的省份
        if let city = findCityById(regionId) {
            visitedRegions.insert(city.provinceId)
        }
        
        saveVisitedRegions()
        
        // 更新数据模型
        updateRegionVisitStatus(regionId, isVisited: true, source: source)
        
        // 如果标记的是城市，也要更新省份状态
        if let city = findCityById(regionId) {
            updateProvinceVisitStatus(city.provinceId, source: source)
        }
    }
    
    func unmarkRegionAsVisited(_ regionId: String) {
        visitedRegions.remove(regionId)
        
        // 如果取消标记的是城市，检查是否需要取消省份标记
        if let city = findCityById(regionId) {
            let province = provinces.first { $0.id == city.provinceId }
            let otherVisitedCities = province?.cities.filter { $0.id != regionId && $0.isVisited } ?? []
            
            // 如果省份内没有其他已访问的城市，则取消省份标记
            if otherVisitedCities.isEmpty {
                visitedRegions.remove(city.provinceId)
            }
        }
        
        saveVisitedRegions()
        
        // 更新数据模型
        updateRegionVisitStatus(regionId, isVisited: false, source: nil)
        
        // 如果取消标记的是城市，也要更新省份状态
        if let city = findCityById(regionId) {
            updateProvinceVisitStatusAfterCityRemoval(city.provinceId)
        }
    }
    
    private func findCityById(_ cityId: String) -> City? {
        for province in provinces {
            if let city = province.cities.first(where: { $0.id == cityId }) {
                return city
            }
        }
        return nil
    }
    
    private func updateProvinceVisitStatus(_ provinceId: String, source: VisitSource) {
        if let index = provinces.firstIndex(where: { $0.id == provinceId }) {
            let province = provinces[index]
            let updatedProvince = Province(
                id: province.id,
                name: province.name,
                countryId: province.countryId,
                cities: province.cities,
                visitedCount: province.cities.filter { $0.isVisited }.count,
                totalCount: province.totalCount,
                coordinates: province.coordinates,
                center: province.center,
                isVisited: true,
                visitSource: source
            )
            provinces[index] = updatedProvince
        }
    }
    
    private func updateProvinceVisitStatusAfterCityRemoval(_ provinceId: String) {
        if let index = provinces.firstIndex(where: { $0.id == provinceId }) {
            let province = provinces[index]
            let visitedCitiesCount = province.cities.filter { $0.isVisited }.count
            
            let updatedProvince = Province(
                id: province.id,
                name: province.name,
                countryId: province.countryId,
                cities: province.cities,
                visitedCount: visitedCitiesCount,
                totalCount: province.totalCount,
                coordinates: province.coordinates,
                center: province.center,
                isVisited: visitedCitiesCount > 0,
                visitSource: visitedCitiesCount > 0 ? province.visitSource : nil
            )
            provinces[index] = updatedProvince
        }
    }
    
    private func updateRegionVisitStatus(_ regionId: String, isVisited: Bool, source: VisitSource?) {
        // 更新省份状态
        if let index = provinces.firstIndex(where: { $0.id == regionId }) {
            let province = provinces[index]
            let updatedProvince = Province(
                id: province.id,
                name: province.name,
                countryId: province.countryId,
                cities: province.cities,
                visitedCount: province.visitedCount,
                totalCount: province.totalCount,
                coordinates: province.coordinates,
                center: province.center,
                isVisited: isVisited,
                visitSource: source
            )
            provinces[index] = updatedProvince
        }
        
        // 更新城市状态
        for (provinceIndex, province) in provinces.enumerated() {
            if let cityIndex = province.cities.firstIndex(where: { $0.id == regionId }) {
                let city = province.cities[cityIndex]
                let updatedCity = City(
                    id: city.id,
                    name: city.name,
                    provinceId: city.provinceId,
                    countryId: city.countryId,
                    coordinate: city.coordinate,
                    isVisited: isVisited,
                    visitDate: isVisited ? Date() : nil,
                    visitSource: source,
                    photoCount: city.photoCount,
                    stayDuration: city.stayDuration
                )
                
                var updatedCities = province.cities
                updatedCities[cityIndex] = updatedCity
                
                let updatedProvince = Province(
                    id: province.id,
                    name: province.name,
                    countryId: province.countryId,
                    cities: updatedCities,
                    visitedCount: updatedCities.filter { $0.isVisited }.count,
                    totalCount: updatedCities.count,
                    coordinates: province.coordinates,
                    center: province.center,
                    isVisited: province.isVisited,
                    visitSource: province.visitSource
                )
                provinces[provinceIndex] = updatedProvince
                break
            }
        }
    }
    
    // MARK: - 统计信息计算
    func calculateGlobalLifeStats() -> GlobalLifeStats {
        let visitedProvinces = provinces.filter { $0.isVisited }.count
        let totalProvinces = provinces.count
        let visitedCities = provinces.flatMap { $0.cities }.filter { $0.isVisited }.count
        let totalCities = provinces.flatMap { $0.cities }.count
        let visitedCountries = countries.filter { $0.visitedCount > 0 }.count
        let totalCountries = countries.count
        
        // 模拟统计数据
        let totalDistance = Double.random(in: 10000...100000) // 10-100万公里
        let worldCoveragePercentage = Double(visitedCountries) / Double(totalCountries) * 100
        let countryCoveragePercentage = Double(visitedProvinces) / Double(totalProvinces) * 100
        let userRanking = min(countryCoveragePercentage * 2.5, 99.9) // 简化的排名计算
        
        return GlobalLifeStats(
            totalDistance: totalDistance,
            visitedCountries: visitedCountries,
            totalCountries: totalCountries,
            visitedProvinces: visitedProvinces,
            totalProvinces: totalProvinces,
            visitedCities: visitedCities,
            totalCities: totalCities,
            worldCoveragePercentage: worldCoveragePercentage,
            countryCoveragePercentage: countryCoveragePercentage,
            userRanking: userRanking,
            totalTravelDays: Int.random(in: 100...1000),
            firstTravelDate: Calendar.current.date(byAdding: .year, value: -5, to: Date()),
            lastTravelDate: Date(),
            totalPhotos: Int.random(in: 1000...10000),
            photosWithGPS: Int.random(in: 500...5000),
            manuallyMarkedPlaces: visitedRegions.count
        )
    }
    
    // MARK: - 照片GPS分析
    func analyzePhotosGPS(completion: @escaping ([City]) -> Void) {
        PHPhotoLibrary.requestAuthorization { status in
            guard status == .authorized else {
                completion([])
                return
            }
            
            let fetchOptions = PHFetchOptions()
            fetchOptions.predicate = NSPredicate(format: "location != nil")
            let assets = PHAsset.fetchAssets(with: .image, options: fetchOptions)
            
            var photoCities: [City] = []
            
            assets.enumerateObjects { asset, _, _ in
                if let location = asset.location {
                    // 根据照片位置查找对应的城市
                    if let city = self.findNearestCity(to: location.coordinate) {
                        photoCities.append(city)
                    }
                }
            }
            
            DispatchQueue.main.async {
                completion(photoCities)
            }
        }
    }
    
    private func findNearestCity(to coordinate: CLLocationCoordinate2D) -> City? {
        let allCities = provinces.flatMap { $0.cities } + countries.flatMap { $0.cities }
        
        var nearestCity: City?
        var minDistance: Double = Double.infinity
        
        for city in allCities {
            let distance = coordinate.distance(to: city.coordinate)
            if distance < minDistance && distance < 50000 { // 50公里范围内
                minDistance = distance
                nearestCity = city
            }
        }
        
        return nearestCity
    }
}

// MARK: - CLLocationManagerDelegate
extension GlobalLifeDataManager: CLLocationManagerDelegate {
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        locationCompletion?(location)
        locationCompletion = nil
        locationManager.stopUpdatingLocation()
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("位置获取失败: \(error)")
        locationCompletion?(nil)
        locationCompletion = nil
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            if locationCompletion != nil {
                locationManager.startUpdatingLocation()
            }
        case .denied, .restricted:
            print("位置权限被拒绝")
            locationCompletion?(nil)
            locationCompletion = nil
        case .notDetermined:
            break
        @unknown default:
            break
        }
    }
    
    // 获取当前位置的公共方法
    func getCurrentLocation(completion: @escaping (CLLocation?) -> Void) {
        locationCompletion = completion
        
        switch locationManager.authorizationStatus {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .denied, .restricted:
            completion(nil)
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.startUpdatingLocation()
        @unknown default:
            completion(nil)
        }
    }
}

// MARK: - CLLocationCoordinate2D Extension
extension CLLocationCoordinate2D {
    func distance(to coordinate: CLLocationCoordinate2D) -> Double {
        let location1 = CLLocation(latitude: self.latitude, longitude: self.longitude)
        let location2 = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        return location1.distance(from: location2)
    }
} 