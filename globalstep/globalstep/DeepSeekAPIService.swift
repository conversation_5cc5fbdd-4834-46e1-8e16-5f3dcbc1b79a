import Foundation

class DeepSeekAPIService {
    static let shared = DeepSeekAPIService()
    
    private let apiKey = "sk-4fe5dc3418d046c38266b65b9542792d"
    private let baseURL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    private var isAPIAvailable = true
    
    private init() {}
    
    // MARK: - Network Status Check
    private func checkAPIAvailability(completion: @escaping (Bool) -> Void) {
        // 简化可用性检查 - 直接返回true，让实际API调用处理错误
        completion(true)
    }
    
    // MARK: - 智能旅游规划
    func generateTravelPlan(
        departure: String,
        destination: String,
        days: Int,
        travelType: String,
        preferences: TravelPreferences,
        completion: @escaping (Result<TravelPlanResponse, Error>) -> Void
    ) {
        // 首先检查API可用性
        checkAPIAvailability { [weak self] isAvailable in
            guard let self = self else { return }
            
            if !isAvailable {
                DispatchQueue.main.async {
                    completion(.failure(APIError.networkTimeout))
                }
                return
            }
            
            let prompt = self.buildTravelPlanPrompt(
                departure: departure,
                destination: destination,
                days: days,
                travelType: travelType,
                preferences: preferences
            )
            
            self.callDeepSeekAPI(prompt: prompt) { result in
                switch result {
                case .success(let response):
                    // 解析API响应为旅游规划数据
                    self.parseTravelPlanResponse(response) { parsedResult in
                        completion(parsedResult)
                    }
                case .failure(let error):
                    completion(.failure(error))
                }
            }
        }
    }
    
    // MARK: - 景点推荐
    func getAttractionRecommendations(
        city: String,
        interests: [String],
        days: Int,
        completion: @escaping (Result<[AttractionRecommendation], Error>) -> Void
    ) {
        let prompt = """
        请为我推荐\(city)的景点，要求：
        1. 根据兴趣偏好：\(interests.joined(separator: "、"))
        2. 适合\(days)天的行程
        3. 包含具体的景点名称、地址、开放时间、门票价格
        4. 提供游览建议和最佳路线
        5. 返回JSON格式数据
        
        JSON格式：
        {
          "attractions": [
            {
              "name": "景点名称",
              "address": "详细地址", 
              "openTime": "开放时间",
              "ticketPrice": "门票价格",
              "rating": 4.5,
              "description": "景点描述",
              "visitDuration": "建议游览时间",
              "bestTime": "最佳游览时间",
              "tags": ["标签1", "标签2"]
            }
          ]
        }
        """
        
        callDeepSeekAPI(prompt: prompt) { result in
            switch result {
            case .success(let response):
                self.parseAttractionRecommendations(response) { parsedResult in
                    completion(parsedResult)
                }
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    // MARK: - 路线优化
    func optimizeRoute(
        attractions: [String],
        startLocation: String,
        completion: @escaping (Result<RouteOptimization, Error>) -> Void
    ) {
        let prompt = """
        请优化以下景点的游览路线：
        
        起点：\(startLocation)
        景点列表：\(attractions.joined(separator: "、"))
        
        要求：
        1. 考虑地理位置，最小化总路程
        2. 考虑景点开放时间和游览时长
        3. 提供详细的路线说明
        4. 估算每段路程的时间和距离
        5. 返回JSON格式
        
        JSON格式：
        {
          "optimizedRoute": [
            {
              "order": 1,
              "attraction": "景点名称",
              "arrivalTime": "到达时间",
              "departureTime": "离开时间",
              "travelTime": "到下一站时间",
              "distance": "距离",
              "transportation": "交通方式"
            }
          ],
          "totalDistance": "总距离",
          "totalTime": "总用时",
          "tips": ["路线建议1", "路线建议2"]
        }
        """
        
        callDeepSeekAPI(prompt: prompt) { result in
            switch result {
            case .success(let response):
                self.parseRouteOptimization(response) { parsedResult in
                    completion(parsedResult)
                }
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    // MARK: - 美食推荐
    func getFoodRecommendations(
        city: String,
        cuisineTypes: [String],
        budget: String,
        completion: @escaping (Result<[FoodRecommendation], Error>) -> Void
    ) {
        let prompt = """
        请推荐\(city)的美食，要求：
        1. 菜系偏好：\(cuisineTypes.joined(separator: "、"))
        2. 预算范围：\(budget)
        3. 包含餐厅名称、地址、招牌菜、价格范围
        4. 提供用餐建议
        5. 返回JSON格式
        
        JSON格式：
        {
          "restaurants": [
            {
              "name": "餐厅名称",
              "address": "详细地址",
              "cuisine": "菜系类型",
              "priceRange": "价格范围",
              "rating": 4.5,
              "specialDishes": ["招牌菜1", "招牌菜2"],
              "description": "餐厅描述",
              "openHours": "营业时间",
              "tips": "用餐建议"
            }
          ]
        }
        """
        
        callDeepSeekAPI(prompt: prompt) { result in
            switch result {
            case .success(let response):
                self.parseFoodRecommendations(response) { parsedResult in
                    completion(parsedResult)
                }
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
        // MARK: - 基础API调用 (带重试机制)
    private func callDeepSeekAPI(
        prompt: String,
        completion: @escaping (Result<String, Error>) -> Void
    ) {
        callDeepSeekAPIWithRetry(prompt: prompt, retryCount: 0, completion: completion)
    }
    
    private func callDeepSeekAPIWithRetry(
        prompt: String,
        retryCount: Int,
        completion: @escaping (Result<String, Error>) -> Void
    ) {
        let maxRetries = 3
        let retryDelays: [TimeInterval] = [2.0, 5.0, 10.0] // 递增延迟
        
        guard let url = URL(string: "\(baseURL)/chat/completions") else {
            completion(.failure(APIError.invalidURL))
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue("globalstep/1.0", forHTTPHeaderField: "User-Agent")
        
        // 调整超时时间 - 适合AI生成任务
        let baseTimeout: TimeInterval = 30.0  // 基础超时时间设为30秒
        let timeoutMultiplier = 1.0 + (Double(retryCount) * 0.3)  // 递增倍数
        request.timeoutInterval = baseTimeout * timeoutMultiplier
        
        let requestBody: [String: Any] = [
            "model": "qwen-plus",
            "messages": [
                [
                    "role": "user",
                    "content": prompt
                ]
            ],
            "max_tokens": 4000,
            "temperature": 0.7,
            "stream": false
        ]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            request.httpBody = jsonData
        } catch {
            completion(.failure(error))
            return
        }
        
        // 创建优化的URLSession配置 - 适合AI任务
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = request.timeoutInterval
        config.timeoutIntervalForResource = request.timeoutInterval * 2.0  // 资源超时更宽松
        config.waitsForConnectivity = true  // 等待网络连接恢复
        config.allowsCellularAccess = true
        config.networkServiceType = .responsiveData
        config.shouldUseExtendedBackgroundIdleMode = false
        config.requestCachePolicy = .reloadIgnoringLocalCacheData  // 忽略缓存
        config.urlCache = nil  // 禁用缓存
        
        let session = URLSession(configuration: config)
        
        print("🚀 DeepSeek API 请求 (第\(retryCount + 1)次尝试，超时：\(request.timeoutInterval)秒)")
        
        session.dataTask(with: request) { [weak self] data, response, error in
            if let error = error {
                let errorMsg = error.localizedDescription
                print("❌ DeepSeek API 网络错误 (第\(retryCount + 1)次): \(errorMsg)")
                
                // 判断是否需要重试
                let shouldRetry = retryCount < maxRetries && self?.shouldRetryForError(error) == true
                
                if shouldRetry {
                    let delay = retryDelays[min(retryCount, retryDelays.count - 1)]
                    print("⏰ \(delay)秒后重试...")
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                        self?.callDeepSeekAPIWithRetry(
                            prompt: prompt,
                            retryCount: retryCount + 1,
                            completion: completion
                        )
                    }
                } else {
                    DispatchQueue.main.async {
                        if errorMsg.contains("timeout") || errorMsg.contains("timed out") {
                            completion(.failure(APIError.networkTimeout))
                        } else {
                            completion(.failure(error))
                        }
                    }
                }
                return
            }
            
            // 检查HTTP响应状态
            if let httpResponse = response as? HTTPURLResponse {
                print("📡 DeepSeek API HTTP状态码: \(httpResponse.statusCode)")
                if httpResponse.statusCode != 200 {
                    let shouldRetry = retryCount < maxRetries && self?.shouldRetryForStatusCode(httpResponse.statusCode) == true
                    
                    if shouldRetry {
                        let delay = retryDelays[min(retryCount, retryDelays.count - 1)]
                        print("⏰ 服务器错误，\(delay)秒后重试...")
                        
                        DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                            self?.callDeepSeekAPIWithRetry(
                                prompt: prompt,
                                retryCount: retryCount + 1,
                                completion: completion
                            )
                        }
                    } else {
                        DispatchQueue.main.async {
                            completion(.failure(APIError.serverError(httpResponse.statusCode)))
                        }
                    }
                    return
                }
            }
            
            guard let data = data else {
                DispatchQueue.main.async {
                    completion(.failure(APIError.noData))
                }
                return
            }
            
            // 打印成功响应
            if let responseString = String(data: data, encoding: .utf8) {
                print("✅ DeepSeek API 响应成功: \(responseString.prefix(150))...")
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    // 检查是否有错误信息
                    if let error = json["error"] as? [String: Any],
                       let errorMessage = error["message"] as? String {
                        DispatchQueue.main.async {
                            completion(.failure(APIError.serverMessage(errorMessage)))
                        }
                        return
                    }
                    
                    // 正常解析响应
                    if let choices = json["choices"] as? [[String: Any]],
                       let firstChoice = choices.first,
                       let message = firstChoice["message"] as? [String: Any],
                       let content = message["content"] as? String {
                        
                        DispatchQueue.main.async {
                            print("🎉 AI响应解析成功，内容长度: \(content.count)")
                            completion(.success(content))
                        }
                    } else {
                        DispatchQueue.main.async {
                            completion(.failure(APIError.invalidResponse))
                        }
                    }
                } else {
                    DispatchQueue.main.async {
                        completion(.failure(APIError.invalidResponse))
                    }
                }
            } catch {
                DispatchQueue.main.async {
                    print("DeepSeek API JSON解析错误: \(error)")
                    completion(.failure(error))
                }
            }
        }.resume()
    }
    
    // MARK: - 重试逻辑辅助方法
    private func shouldRetryForError(_ error: Error) -> Bool {
        let nsError = error as NSError
        
        // 网络相关错误可以重试
        if nsError.domain == NSURLErrorDomain {
            switch nsError.code {
            case NSURLErrorTimedOut,
                 NSURLErrorCannotFindHost,
                 NSURLErrorCannotConnectToHost,
                 NSURLErrorNetworkConnectionLost,
                 NSURLErrorDNSLookupFailed,
                 NSURLErrorNotConnectedToInternet:
                return true
            default:
                return false
            }
        }
        
        return false
    }
    
    private func shouldRetryForStatusCode(_ statusCode: Int) -> Bool {
        // 服务器错误和某些客户端错误可以重试
        switch statusCode {
        case 429, // Too Many Requests
             500, 502, 503, 504, // Server errors
             408: // Request Timeout
            return true
        default:
            return false
        }
    }
    
    // MARK: - 构建提示词
    private func buildTravelPlanPrompt(
        departure: String,
        destination: String,
        days: Int,
        travelType: String,
        preferences: TravelPreferences
    ) -> String {
        let interestsText = preferences.interests.isEmpty ? "经典打卡、自然风光、人文历史" : preferences.interests.joined(separator: "、")
        let foodsText = preferences.foods.isEmpty ? "本地特色、街头小吃" : preferences.foods.joined(separator: "、")
        let accommodationsText = preferences.accommodations.isEmpty ? "性价比高、交通便利" : preferences.accommodations.joined(separator: "、")
        
        return """
        请为\(destination)\(days)日游制定1个精简方案，包含10-15个真实景点。

        基本信息：出发地\(departure)，目的地\(destination)，\(days)天\(travelType)游
        偏好：\(interestsText)

        要求：每天6-8个活动，只使用\(destination)真实存在的景点！

        请严格按照以下JSON格式返回：
        {
          "plans": [
            {
              "title": "\(destination)精品游",
              "description": "\(destination)经典景点游览",
              "totalBudget": "人均600-800元",
              "theme": "经典游览",
              "dailyPlans": [
                {
                  "day": 1,
                  "title": "第一天",
                  "activities": [
                    {
                      "time": "09:00-10:30",
                      "activity": "景点游览",
                      "location": "\(destination)著名景点",
                      "duration": "1.5小时",
                      "cost": "门票价格",
                      "tips": "游览建议"
                    }
                  ]
                }
              ],
              "attractions": ["\(destination)景点1", "\(destination)景点2", "\(destination)景点3", "\(destination)景点4", "\(destination)景点5"],
              "restaurants": ["\(destination)餐厅1", "\(destination)餐厅2"],
              "hotels": ["\(destination)酒店1"],
              "transportation": "交通建议",
              "tips": ["游览提示"]
            }
          ]
        }
        
        重要：请用\(destination)真实景点替换示例中的占位符！确保JSON格式完整！
        """
    }
    
    // MARK: - 解析响应
    private func parseTravelPlanResponse(
        _ response: String,
        completion: @escaping (Result<TravelPlanResponse, Error>) -> Void
    ) {
        // 提取JSON部分
        let jsonString = extractJSON(from: response)
        
        guard let data = jsonString.data(using: .utf8) else {
            completion(.failure(APIError.invalidResponse))
            return
        }
        
        do {
            let decoder = JSONDecoder()
            let travelPlan = try decoder.decode(TravelPlanResponse.self, from: data)
            print("✅ JSON解析成功，方案数量: \(travelPlan.plans.count)")
            for (index, plan) in travelPlan.plans.enumerated() {
                print("📋 方案\(index + 1): \(plan.title) - 景点数: \(plan.attractions.count)")
            }
            completion(.success(travelPlan))
        } catch {
            // 如果JSON解析失败，尝试调试
            print("❌ JSON解析失败详细错误: \(error)")
            print("📄 JSON字符串内容:")
            print(jsonString)
            
            // 尝试解析为旧格式（单个方案）
            if let oldFormatPlan = tryParseOldFormat(jsonString) {
                print("✅ 使用旧格式解析成功")
                let response = TravelPlanResponse(plans: [oldFormatPlan])
                completion(.success(response))
                return
            }
            
            print("⚠️ 创建默认方案")
            // 创建默认方案
            let basicSinglePlan = SingleTravelPlan(
                title: "AI智能规划行程",
                description: "基于AI智能分析生成的个性化行程",
                totalBudget: "请根据实际情况调整",
                theme: "综合游览",
                dailyPlans: [],
                attractions: [],
                restaurants: [],
                hotels: [],
                transportation: "建议使用公共交通",
                tips: ["请提前查看天气", "注意安全"]
            )
            
            let basicPlan = TravelPlanResponse(plans: [basicSinglePlan])
            completion(.success(basicPlan))
        }
    }
    
    private func parseAttractionRecommendations(
        _ response: String,
        completion: @escaping (Result<[AttractionRecommendation], Error>) -> Void
    ) {
        let jsonString = extractJSON(from: response)
        
        guard let data = jsonString.data(using: .utf8) else {
            completion(.failure(APIError.invalidResponse))
            return
        }
        
        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let attractionsData = json["attractions"] as? [[String: Any]] {
                
                let attractions = attractionsData.compactMap { dict -> AttractionRecommendation? in
                    guard let name = dict["name"] as? String else { return nil }
                    
                    return AttractionRecommendation(
                        name: name,
                        address: dict["address"] as? String ?? "",
                        openTime: dict["openTime"] as? String ?? "",
                        ticketPrice: dict["ticketPrice"] as? String ?? "",
                        rating: dict["rating"] as? Double ?? 0.0,
                        description: dict["description"] as? String ?? "",
                        visitDuration: dict["visitDuration"] as? String ?? "",
                        bestTime: dict["bestTime"] as? String ?? "",
                        tags: dict["tags"] as? [String] ?? []
                    )
                }
                
                completion(.success(attractions))
            } else {
                completion(.failure(APIError.invalidResponse))
            }
        } catch {
            completion(.failure(error))
        }
    }
    
    private func parseRouteOptimization(
        _ response: String,
        completion: @escaping (Result<RouteOptimization, Error>) -> Void
    ) {
        let jsonString = extractJSON(from: response)
        
        guard let data = jsonString.data(using: .utf8) else {
            completion(.failure(APIError.invalidResponse))
            return
        }
        
        do {
            let decoder = JSONDecoder()
            let routeOptimization = try decoder.decode(RouteOptimization.self, from: data)
            completion(.success(routeOptimization))
        } catch {
            completion(.failure(error))
        }
    }
    
    private func parseFoodRecommendations(
        _ response: String,
        completion: @escaping (Result<[FoodRecommendation], Error>) -> Void
    ) {
        let jsonString = extractJSON(from: response)
        
        guard let data = jsonString.data(using: .utf8) else {
            completion(.failure(APIError.invalidResponse))
            return
        }
        
        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let restaurantsData = json["restaurants"] as? [[String: Any]] {
                
                let restaurants = restaurantsData.compactMap { dict -> FoodRecommendation? in
                    guard let name = dict["name"] as? String else { return nil }
                    
                    return FoodRecommendation(
                        name: name,
                        address: dict["address"] as? String ?? "",
                        cuisine: dict["cuisine"] as? String ?? "",
                        priceRange: dict["priceRange"] as? String ?? "",
                        rating: dict["rating"] as? Double ?? 0.0,
                        specialDishes: dict["specialDishes"] as? [String] ?? [],
                        description: dict["description"] as? String ?? "",
                        openHours: dict["openHours"] as? String ?? "",
                        tips: dict["tips"] as? String ?? ""
                    )
                }
                
                completion(.success(restaurants))
            } else {
                completion(.failure(APIError.invalidResponse))
            }
        } catch {
            completion(.failure(error))
        }
    }
    
    // MARK: - 工具方法
    private func tryParseOldFormat(_ jsonString: String) -> SingleTravelPlan? {
        guard let data = jsonString.data(using: .utf8) else { return nil }
        
        do {
            // 尝试解析为单个旅行方案格式
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                
                // 检查是否是单个方案格式
                if let title = json["title"] as? String,
                   let description = json["description"] as? String {
                    
                    return SingleTravelPlan(
                        title: title,
                        description: description,
                        totalBudget: json["totalBudget"] as? String ?? "",
                        theme: json["theme"] as? String ?? "综合游览",
                        dailyPlans: [],
                        attractions: json["attractions"] as? [String] ?? [],
                        restaurants: json["restaurants"] as? [String] ?? [],
                        hotels: json["hotels"] as? [String] ?? [],
                        transportation: json["transportation"] as? String ?? "",
                        tips: json["tips"] as? [String] ?? []
                    )
                }
            }
        } catch {
            print("⚠️ 旧格式解析也失败: \(error)")
        }
        
        return nil
    }
    
    private func extractJSON(from text: String) -> String {
        print("🔍 开始提取JSON，原始响应长度: \(text.count)")
        
        // 移除markdown代码块标记
        var cleanText = text
        if let jsonStart = text.range(of: "```json") {
            cleanText = String(text[jsonStart.upperBound...])
        }
        if let jsonEnd = cleanText.range(of: "```", options: .backwards) {
            cleanText = String(cleanText[..<jsonEnd.lowerBound])
        }
        
        // 移除前后空白
        cleanText = cleanText.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 尝试找到完整的JSON部分
        guard let startIndex = cleanText.firstIndex(of: "{") else {
            print("❌ 未找到JSON开始标记")
            print("📄 清理后文本开头: \(String(cleanText.prefix(200)))...")
            return text
        }
        
        // 使用括号匹配找到完整的JSON
        var braceCount = 0
        var endIndex: String.Index?
        var inString = false
        var escapeNext = false
        
        for i in cleanText[startIndex...].indices {
            let char = cleanText[i]
            
            if escapeNext {
                escapeNext = false
                continue
            }
            
            if char == "\\" {
                escapeNext = true
                continue
            }
            
            if char == "\"" {
                inString = !inString
                continue
            }
            
            if !inString {
                if char == "{" {
                    braceCount += 1
                } else if char == "}" {
                    braceCount -= 1
                    if braceCount == 0 {
                        endIndex = i
                        break
                    }
                }
            }
        }
        
        if let endIndex = endIndex {
            let jsonString = String(cleanText[startIndex...endIndex])
            print("✅ 成功提取JSON，长度: \(jsonString.count)")
            print("📝 JSON开头: \(String(jsonString.prefix(200)))...")
            return jsonString
        } else {
            print("❌ 未找到完整的JSON结构")
            print("🔧 括号计数结果: \(braceCount)")
            print("📄 清理后文本长度: \(cleanText.count)")
            print("📄 清理后文本开头: \(String(cleanText.prefix(500)))...")
            return cleanText
        }
    }
}

// MARK: - API错误类型
enum APIError: Error {
    case invalidURL
    case noData
    case invalidResponse
    case decodingError
    case networkTimeout
    case serverError(Int)
    case serverMessage(String)
    
    var localizedDescription: String {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .noData:
            return "没有返回数据"
        case .invalidResponse:
            return "无效的响应格式"
        case .decodingError:
            return "数据解析失败"
        case .networkTimeout:
            return "网络连接超时，请检查网络连接"
        case .serverError(let code):
            return "服务器错误 (HTTP \(code))"
        case .serverMessage(let message):
            return "API错误: \(message)"
        }
    }
}

// MARK: - 数据模型
struct TravelPlanResponse: Codable {
    let plans: [SingleTravelPlan]
}

struct SingleTravelPlan: Codable {
    let title: String
    let description: String
    let totalBudget: String
    let theme: String
    let dailyPlans: [APIDailyPlan]
    let attractions: [String]
    let restaurants: [String]
    let hotels: [String]
    let transportation: String
    let tips: [String]
}

struct APIDailyPlan: Codable {
    let day: Int
    let title: String
    let activities: [APIActivity]
}

struct APIActivity: Codable {
    let time: String
    let activity: String
    let location: String
    let duration: String
    let cost: String
    let tips: String
}

struct AttractionRecommendation: Codable {
    let name: String
    let address: String
    let openTime: String
    let ticketPrice: String
    let rating: Double
    let description: String
    let visitDuration: String
    let bestTime: String
    let tags: [String]
}

struct RouteOptimization: Codable {
    let optimizedRoute: [RouteStep]
    let totalDistance: String
    let totalTime: String
    let tips: [String]
}

struct RouteStep: Codable {
    let order: Int
    let attraction: String
    let arrivalTime: String
    let departureTime: String
    let travelTime: String
    let distance: String
    let transportation: String
}

struct FoodRecommendation: Codable {
    let name: String
    let address: String
    let cuisine: String
    let priceRange: String
    let rating: Double
    let specialDishes: [String]
    let description: String
    let openHours: String
    let tips: String
} 