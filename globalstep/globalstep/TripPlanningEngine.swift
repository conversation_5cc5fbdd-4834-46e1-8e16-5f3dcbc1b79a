import Foundation
import CoreLocation

// MARK: - Trip Planning Engine
class TripPlanningEngine {
    static let shared = TripPlanningEngine()
    private let dataManager = TravelDataManager.shared
    
    private init() {}
    
    // MARK: - AI Enhanced Planning Method
    func generateAIEnhancedTripPlans(
        departure: String,
        destination: String,
        days: Int,
        travelType: String,
        preferences: TravelPreferences,
        completion: @escaping ([TripPlan]) -> Void
    ) {
        print("🚀 开始AI增强规划 - TripPlanningEngine")
        
        // 使用DeepSeek API生成智能规划
        DeepSeekAPIService.shared.generateTravelPlan(
            departure: departure,
            destination: destination,
            days: days,
            travelType: travelType,
            preferences: preferences
        ) { [weak self] result in
            print("📨 收到DeepSeek API回调")
            
            switch result {
            case .success(let aiPlan):
                print("✅ API调用成功，开始转换AI响应")
                // 将AI规划转换为TripPlan格式
                let tripPlan = self?.convertAIPlanToTripPlan(aiPlan, departure: departure, destination: destination, days: days) ?? []
                
                print("🔄 转换结果: \(tripPlan.count)个方案")
                
                // 如果AI规划失败，使用本地算法
                if tripPlan.isEmpty {
                    print("⚠️ AI转换结果为空，使用本地算法")
                    let localPlans = self?.generateTripPlans(
                        departure: departure,
                        destination: destination,
                        days: days,
                        travelType: travelType,
                        preferences: preferences
                    ) ?? []
                    print("🏠 本地算法生成\(localPlans.count)个方案")
                    completion(localPlans)
                } else {
                    print("🎉 返回AI生成的\(tripPlan.count)个方案")
                    completion(tripPlan)
                }
                
            case .failure(let error):
                print("❌ AI规划失败: \(error.localizedDescription)")
                // 降级到本地算法
                let localPlans = self?.generateTripPlans(
                    departure: departure,
                    destination: destination,
                    days: days,
                    travelType: travelType,
                    preferences: preferences
                ) ?? []
                print("🏠 降级到本地算法，生成\(localPlans.count)个方案")
                completion(localPlans)
            }
        }
    }
    
    // MARK: - Main Planning Method
    func generateTripPlans(
        departure: String,
        destination: String,
        days: Int,
        travelType: String,
        preferences: TravelPreferences
    ) -> [TripPlan] {
        
        guard let destinationCity = dataManager.getCityByName(destination) else {
            print("🔍 在TravelDataManager中未找到城市: \(destination)，生成通用行程")
            return generateGenericTripPlans(departure: departure, destination: destination, days: days, travelType: travelType)
        }
        
        let attractions = dataManager.getAttractions(for: destinationCity.id)
        let restaurants = dataManager.getRestaurants(for: destinationCity.id)
        
        // 根据偏好筛选景点
        let filteredAttractions = filterAttractionsByPreferences(attractions, preferences: preferences)
        
        // 生成多个方案
        var plans: [TripPlan] = []
        
        // 方案1：文化历史主题
        if preferences.interests.contains("人文历史") || preferences.interests.isEmpty {
            let culturalPlan = generateCulturalPlan(
                departure: departure,
                destination: destination,
                days: days,
                attractions: filteredAttractions,
                restaurants: restaurants
            )
            plans.append(culturalPlan)
        }
        
        // 方案2：现代都市主题
        if preferences.interests.contains("城市搜索") || preferences.interests.isEmpty {
            let modernPlan = generateModernPlan(
                departure: departure,
                destination: destination,
                days: days,
                attractions: filteredAttractions,
                restaurants: restaurants
            )
            plans.append(modernPlan)
        }
        
        // 方案3：美食文化主题
        if preferences.interests.contains("休闲娱乐") || !preferences.foods.isEmpty || preferences.interests.isEmpty {
            let foodPlan = generateFoodPlan(
                departure: departure,
                destination: destination,
                days: days,
                attractions: filteredAttractions,
                restaurants: restaurants
            )
            plans.append(foodPlan)
        }
        
        // 如果没有生成任何方案，返回默认方案
        if plans.isEmpty {
            plans.append(generateDefaultPlan(departure: departure, destination: destination, days: days))
        }
        
        return Array(plans.prefix(3)) // 最多返回3个方案
    }
    
    // MARK: - Filter Methods
    private func filterAttractionsByPreferences(_ attractions: [AttractionDetail], preferences: TravelPreferences) -> [AttractionDetail] {
        var filtered = attractions
        
        // 根据兴趣偏好筛选
        if !preferences.interests.isEmpty {
            filtered = filtered.filter { attraction in
                let interestKeywords = getInterestKeywords(preferences.interests)
                return !Set(attraction.tags).isDisjoint(with: Set(interestKeywords)) ||
                       interestKeywords.contains(attraction.category.rawValue)
            }
        }
        
        // 按热门程度和评分排序
        filtered.sort { (a1, a2) in
            if a1.popularity != a2.popularity {
                return a1.popularity > a2.popularity
            }
            return a1.rating > a2.rating
        }
        
        return filtered
    }
    
    private func getInterestKeywords(_ interests: [String]) -> [String] {
        var keywords: [String] = []
        
        for interest in interests {
            switch interest {
            case "经典打卡":
                keywords.append(contentsOf: ["地标建筑", "著名景点", "必游之地"])
            case "自然风光":
                keywords.append(contentsOf: ["自然风光", "公园", "海河夜景", "山水景观"])
            case "人文历史":
                keywords.append(contentsOf: ["历史文化", "传统建筑", "古代建筑", "文化遗产"])
            case "城市搜索":
                keywords.append(contentsOf: ["现代建筑", "都市风光", "购物娱乐"])
            case "户外冒险":
                keywords.append(contentsOf: ["户外", "冒险", "登高", "探索"])
            case "休闲娱乐":
                keywords.append(contentsOf: ["休闲", "娱乐", "放松", "体验"])
            case "艺术创意":
                keywords.append(contentsOf: ["艺术", "创意", "设计", "文化创意"])
            case "特色体验":
                keywords.append(contentsOf: ["特色", "体验", "民俗", "传统"])
            case "度假":
                keywords.append(contentsOf: ["度假", "休闲", "放松"])
            default:
                break
            }
        }
        
        return keywords
    }
    
    // MARK: - Plan Generation Methods
    private func generateCulturalPlan(departure: String, destination: String, days: Int, attractions: [AttractionDetail], restaurants: [Restaurant]) -> TripPlan {
        
        // 优选历史文化类景点
        let culturalAttractions = attractions.filter { 
            $0.category == .historical || $0.tags.contains("历史文化") || $0.tags.contains("传统建筑")
        }.prefix(days * 3)
        
        let culturalRestaurants = restaurants.filter {
            $0.cuisine == .local || $0.tags.contains("传统")
        }
        
        var attractionNames: [String] = []
        
        if destination.contains("天津") {
            attractionNames = [
                "天津站", "古文化街（津门故里）", "狗不理包子（鼓楼店）", 
                "天津之眼", "意大利风情区", "津湾广场",
                "五大道历史文化区", "瓷房子", "海河", "西开教堂", "耳朵眼炸糕总店"
            ]
        } else {
            attractionNames = Array(culturalAttractions.map { $0.name })
            if culturalRestaurants.count > 0 {
                attractionNames.append(culturalRestaurants[0].name)
            }
        }
        
        return TripPlan(
            title: "\(destination)文化与现代风情\(days)日游",
            description: "从\(departure)出发，感受\(destination)的历史与现代魅力",
            dayCount: days,
            attractionCount: attractionNames.count,
            attractions: attractionNames,
            dailyPlans: nil
        )
    }
    
    private func generateModernPlan(departure: String, destination: String, days: Int, attractions: [AttractionDetail], restaurants: [Restaurant]) -> TripPlan {
        
        // 优选现代景观和购物娱乐类景点
        let modernAttractions = attractions.filter { 
            $0.category == .modern || $0.category == .shopping || $0.tags.contains("现代建筑")
        }.prefix(days * 3)
        
        let modernRestaurants = restaurants.filter {
            $0.cuisine == .fine || $0.cuisine == .western
        }
        
        var attractionNames: [String] = []
        
        if destination.contains("天津") {
            attractionNames = [
                "天津站", "国家海洋博物馆", "极地海洋世界", 
                "滨海喜来登酒店", "泰达航母主题公园", "滨海图书馆",
                "天津之眼", "津湾广场"
            ]
        } else {
            attractionNames = Array(modernAttractions.map { $0.name })
            if modernRestaurants.count > 0 {
                attractionNames.append(modernRestaurants[0].name)
            }
        }
        
        return TripPlan(
            title: "\(destination)新区与市区联动\(days)日深度游",
            description: "从\(departure)出发，体验\(destination)的海滨与都市风光",
            dayCount: days,
            attractionCount: attractionNames.count,
            attractions: attractionNames,
            dailyPlans: nil
        )
    }
    
    private func generateFoodPlan(departure: String, destination: String, days: Int, attractions: [AttractionDetail], restaurants: [Restaurant]) -> TripPlan {
        
        // 优选美食相关景点和餐厅
        let foodAttractions = attractions.filter { 
            $0.category == .food || $0.tags.contains("美食") || $0.tags.contains("小吃")
        }
        
        let localRestaurants = restaurants.filter {
            $0.cuisine == .local || $0.cuisine == .street
        }.prefix(days * 2)
        
        var attractionNames: [String] = []
        
        if destination.contains("天津") {
            attractionNames = [
                "天津站", "耳朵眼炸糕总店", "古文化街（津门故里）", 
                "煎饼果子店", "狗不理包子（鼓楼店）", "西开教堂",
                "意大利风情区", "海河", "天津之眼", "五大道历史文化区", "十八街麻花"
            ]
        } else {
            // 混合景点和餐厅
            attractionNames.append(contentsOf: foodAttractions.map { $0.name })
            attractionNames.append(contentsOf: localRestaurants.map { $0.name })
            
            // 补充一些热门景点
            let topAttractions = attractions.sorted { $0.popularity > $1.popularity }.prefix(days * 2)
            attractionNames.append(contentsOf: topAttractions.map { $0.name })
        }
        
        return TripPlan(
            title: "\(destination)味美食与文化探秘\(days)日行",
            description: "从\(departure)出发，深度体验\(destination)的美食文化",
            dayCount: days,
            attractionCount: attractionNames.count,
            attractions: Array(attractionNames.prefix(11)),
            dailyPlans: nil
        )
    }
    
    private func generateDefaultPlan(departure: String, destination: String, days: Int) -> TripPlan {
        let defaultAttractions = [
            "市中心", "历史文化区", "特色美食街", "著名景点", "购物中心", "公园广场"
        ]
        
        return TripPlan(
            title: "\(destination)经典文化游",
            description: "从\(departure)出发，探索\(destination)的文化魅力",
            dayCount: days,
            attractionCount: defaultAttractions.count,
            attractions: defaultAttractions,
            dailyPlans: nil
        )
    }
    
    // MARK: - Generic Trip Planning
    private func generateGenericTripPlans(departure: String, destination: String, days: Int, travelType: String) -> [TripPlan] {
        print("🎯 生成通用行程方案")
        
        var plans: [TripPlan] = []
        
        // 方案1：经典观光游
        let classicAttractions = generateClassicAttractions(for: destination, days: days)
        plans.append(TripPlan(
            title: "\(destination)经典观光\(days)日游",
            description: "从\(departure)出发，游览\(destination)的经典景点和地标建筑",
            dayCount: days,
            attractionCount: classicAttractions.count,
            attractions: classicAttractions,
            dailyPlans: nil
        ))
        
        // 方案2：文化体验游
        let culturalAttractions = generateCulturalAttractions(for: destination, days: days)
        plans.append(TripPlan(
            title: "\(destination)文化体验\(days)日游",
            description: "从\(departure)出发，深度体验\(destination)的历史文化和民俗风情",
            dayCount: days,
            attractionCount: culturalAttractions.count,
            attractions: culturalAttractions,
            dailyPlans: nil
        ))
        
        // 方案3：美食休闲游
        let leisureAttractions = generateLeisureAttractions(for: destination, days: days)
        plans.append(TripPlan(
            title: "\(destination)美食休闲\(days)日游",
            description: "从\(departure)出发，品味\(destination)的特色美食和休闲生活",
            dayCount: days,
            attractionCount: leisureAttractions.count,
            attractions: leisureAttractions,
            dailyPlans: nil
        ))
        
        print("✅ 生成了\(plans.count)个通用方案")
        return plans
    }
    
    private func generateClassicAttractions(for destination: String, days: Int) -> [String] {
        let baseAttractions = [
            "\(destination)火车站", "\(destination)市中心广场", "\(destination)博物馆", 
            "\(destination)历史文化街区", "\(destination)城市公园", "\(destination)地标建筑",
            "\(destination)观景台", "\(destination)商业中心", "\(destination)文化广场",
            "\(destination)风景名胜区", "\(destination)特色景点", "\(destination)著名建筑"
        ]
        
        let attractionsPerDay = max(3, min(6, baseAttractions.count / days))
        let totalAttractions = min(days * attractionsPerDay, baseAttractions.count)
        
        return Array(baseAttractions.prefix(totalAttractions))
    }
    
    private func generateCulturalAttractions(for destination: String, days: Int) -> [String] {
        let baseAttractions = [
            "\(destination)古城区", "\(destination)文化中心", "\(destination)历史博物馆",
            "\(destination)民俗文化村", "\(destination)传统老街", "\(destination)文化遗址",
            "\(destination)艺术馆", "\(destination)古建筑群", "\(destination)文化公园",
            "\(destination)民族风情园", "\(destination)传统工艺街", "\(destination)文化广场"
        ]
        
        let attractionsPerDay = max(3, min(6, baseAttractions.count / days))
        let totalAttractions = min(days * attractionsPerDay, baseAttractions.count)
        
        return Array(baseAttractions.prefix(totalAttractions))
    }
    
    private func generateLeisureAttractions(for destination: String, days: Int) -> [String] {
        let baseAttractions = [
            "\(destination)特色美食街", "\(destination)休闲公园", "\(destination)购物中心",
            "\(destination)小吃一条街", "\(destination)咖啡街区", "\(destination)夜市",
            "\(destination)温泉度假村", "\(destination)娱乐中心", "\(destination)滨水公园",
            "\(destination)特色餐厅", "\(destination)休闲广场", "\(destination)茶文化街"
        ]
        
        let attractionsPerDay = max(3, min(6, baseAttractions.count / days))
        let totalAttractions = min(days * attractionsPerDay, baseAttractions.count)
        
        return Array(baseAttractions.prefix(totalAttractions))
    }
    
    // MARK: - Detailed Day Planning
    func generateDetailedDayPlans(for tripPlan: TripPlan, destination: String) -> [DayPlan] {
        guard let destinationCity = dataManager.getCityByName(destination) else {
            return generateDefaultDayPlans(for: tripPlan)
        }
        
        let allAttractions = dataManager.getAttractions(for: destinationCity.id)
        var dayPlans: [DayPlan] = []
        
        // 将行程中的景点名称转换为详细信息
        var planAttractions: [AttractionDetail] = []
        for name in tripPlan.attractions {
            if let attraction = allAttractions.first(where: { $0.name == name }) {
                planAttractions.append(attraction)
            }
        }
        
        // 对所有景点进行路线优化
        let optimizedAttractions = optimizeRoute(planAttractions)
        
        // 将优化后的景点分配到不同天数
        let attractionsPerDay = max(1, optimizedAttractions.count / tripPlan.dayCount)
        
        for day in 1...tripPlan.dayCount {
            let startIndex = (day - 1) * attractionsPerDay
            let endIndex = min(day * attractionsPerDay, optimizedAttractions.count)
            
            // 确保startIndex < endIndex且不超出数组边界
            guard startIndex < optimizedAttractions.count && startIndex < endIndex else {
                continue
            }
            
            let dayAttractionDetails = Array(optimizedAttractions[startIndex..<endIndex])
            
            // 转换为Attraction类型
            let dayAttractions = dayAttractionDetails.map { attraction in
                Attraction(
                    name: attraction.name,
                    type: attraction.category.rawValue,
                    coordinate: attraction.coordinate
                )
            }
            
            // 计算这一天的总游玩时间和距离
            let totalDuration = dayAttractionDetails.reduce(0) { $0 + $1.visitDuration }
            let totalDistance = calculateDayDistance(dayAttractionDetails)
            
            let dayPlan = DayPlan(
                day: day,
                title: dayAttractions.first?.name ?? "第\(day)天行程",
                subtitle: "共\(dayAttractions.count)个地点 · 约\(totalDuration/60)小时 · \(String(format: "%.1f", totalDistance/1000))公里",
                attractions: dayAttractions
            )
            
            dayPlans.append(dayPlan)
        }
        
        return dayPlans
    }
    
    private func calculateDayDistance(_ attractions: [AttractionDetail]) -> Double {
        guard attractions.count > 1 else { return 0 }
        
        var totalDistance = 0.0
        for i in 0..<(attractions.count - 1) {
            totalDistance += distance(from: attractions[i].coordinate, to: attractions[i + 1].coordinate)
        }
        return totalDistance
    }
    
    private func generateDefaultDayPlans(for tripPlan: TripPlan) -> [DayPlan] {
        var dayPlans: [DayPlan] = []
        let attractionsPerDay = max(1, tripPlan.attractions.count / tripPlan.dayCount)
        
        for day in 1...tripPlan.dayCount {
            let startIndex = (day - 1) * attractionsPerDay
            let endIndex = min(day * attractionsPerDay, tripPlan.attractions.count)
            
            // 确保startIndex < endIndex且不超出数组边界
            guard startIndex < tripPlan.attractions.count && startIndex < endIndex else {
                continue
            }
            
            let dayAttractionNames = Array(tripPlan.attractions[startIndex..<endIndex])
            let dayAttractions = dayAttractionNames.map { name in
                Attraction(
                    name: name,
                    type: "景点",
                    coordinate: CLLocationCoordinate2D(latitude: 39.1304, longitude: 117.1892)
                )
            }
            
            let dayPlan = DayPlan(
                day: day,
                title: dayAttractions.first?.name ?? "第\(day)天行程",
                subtitle: "共\(dayAttractions.count)个地点",
                attractions: dayAttractions
            )
            
            dayPlans.append(dayPlan)
        }
        
        return dayPlans
    }
    
    // MARK: - Route Optimization
    func optimizeRoute(_ attractions: [AttractionDetail]) -> [AttractionDetail] {
        // 智能路线优化：考虑距离、开放时间、游玩时长等因素
        
        if attractions.count <= 1 {
            return attractions
        }
        
        var optimized: [AttractionDetail] = []
        var remaining = attractions
        
        // 第一步：选择起始点（交通枢纽或热门景点）
        let startPoint = findBestStartPoint(remaining)
        optimized.append(startPoint)
        remaining.removeAll { $0.id == startPoint.id }
        
        // 第二步：使用改进的贪心算法，考虑多个因素
        while !remaining.isEmpty {
            let current = optimized.last!
            let nextAttraction = findBestNextAttraction(from: current, candidates: remaining)
            
            optimized.append(nextAttraction)
            remaining.removeAll { $0.id == nextAttraction.id }
        }
        
        return optimized
    }
    
    private func findBestStartPoint(_ attractions: [AttractionDetail]) -> AttractionDetail {
        // 优先选择交通枢纽
        if let transportHub = attractions.first(where: { $0.tags.contains("交通枢纽") || $0.tags.contains("出发点") }) {
            return transportHub
        }
        
        // 否则选择热门程度最高的景点
        return attractions.max { $0.popularity < $1.popularity } ?? attractions.first!
    }
    
    private func findBestNextAttraction(from current: AttractionDetail, candidates: [AttractionDetail]) -> AttractionDetail {
        var bestScore = -1.0
        var bestAttraction = candidates.first!
        
        for candidate in candidates {
            let score = calculateAttractionScore(from: current, to: candidate)
            if score > bestScore {
                bestScore = score
                bestAttraction = candidate
            }
        }
        
        return bestAttraction
    }
    
    private func calculateAttractionScore(from current: AttractionDetail, to candidate: AttractionDetail) -> Double {
        // 距离因子（距离越近得分越高）
        let dist = distance(from: current.coordinate, to: candidate.coordinate)
        let distanceScore = max(0, 1.0 - dist / 10000) // 10km归一化
        
        // 热门程度因子
        let popularityScore = Double(candidate.popularity) / 10.0
        
        // 类型匹配因子（相似类型的景点分开安排）
        let typeScore = current.category == candidate.category ? 0.3 : 1.0
        
        // 游玩时长因子（合理安排时间）
        let durationScore = candidate.visitDuration < 180 ? 1.0 : 0.7
        
        // 综合评分
        return distanceScore * 0.4 + popularityScore * 0.3 + typeScore * 0.2 + durationScore * 0.1
    }
    
    private func distance(from: CLLocationCoordinate2D, to: CLLocationCoordinate2D) -> Double {
        let fromLocation = CLLocation(latitude: from.latitude, longitude: from.longitude)
        let toLocation = CLLocation(latitude: to.latitude, longitude: to.longitude)
        return fromLocation.distance(from: toLocation)
    }
    
    // MARK: - AI Response Conversion
    private func convertAIPlanToTripPlan(_ aiPlan: TravelPlanResponse, departure: String, destination: String, days: Int) -> [TripPlan] {
        print("🔄 开始转换AI响应到TripPlan")
        print("📊 AI返回的方案数量: \(aiPlan.plans.count)")
        
        var plans: [TripPlan] = []
        
        // 转换每个AI方案
        for (index, singlePlan) in aiPlan.plans.enumerated() {
            print("📋 方案\(index + 1): '\(singlePlan.title)'")
            print("📝 描述: '\(singlePlan.description)'")
            print("🎯 景点数量: \(singlePlan.attractions.count)")
            print("🏛️ 景点列表: \(singlePlan.attractions)")
            
            // 转换API格式到内部格式
            let convertedDailyPlans = singlePlan.dailyPlans.map { apiDailyPlan in
                let convertedActivities = apiDailyPlan.activities.map { apiActivity in
                    TripActivity(
                        time: apiActivity.time,
                        activity: apiActivity.activity,
                        location: apiActivity.location,
                        duration: apiActivity.duration,
                        cost: apiActivity.cost,
                        tips: apiActivity.tips
                    )
                }
                return TripDailyPlan(
                    day: apiDailyPlan.day,
                    title: apiDailyPlan.title,
                    activities: convertedActivities
                )
            }
            
            // 创建方案
            let tripPlan = TripPlan(
                title: singlePlan.title.isEmpty ? "\(destination)AI智能规划\(days)日游" : singlePlan.title,
                description: singlePlan.description.isEmpty ? "基于AI智能分析生成的个性化行程" : singlePlan.description,
                dayCount: days,
                attractionCount: singlePlan.attractions.count,
                attractions: singlePlan.attractions,
                dailyPlans: convertedDailyPlans
            )
            plans.append(tripPlan)
            print("✅ 创建方案\(index + 1): '\(tripPlan.title)' - \(tripPlan.attractions.count)个景点")
        }
        
                // 如果AI方案较少或景点不够，补充更多方案
        if plans.isEmpty || plans.allSatisfy({ $0.attractions.count < days * 3 }) {
            print("⚠️ AI方案不足，补充本地方案")
            
            let localPlans = generateTripPlans(
                departure: departure,
                destination: destination,
                days: days,
                travelType: "经典游",
                preferences: TravelPreferences(interests: [], foods: [], accommodations: [])
            )
            
            // 如果AI方案为空，使用本地方案
            if plans.isEmpty {
                plans = localPlans
            } else {
                // 补充到3个方案
                let needMorePlans = max(0, 3 - plans.count)
                for localPlan in localPlans.prefix(needMorePlans) {
                    plans.append(localPlan)
                }
            }
        }
        
        print("🎊 AI转换完成，总共生成\(plans.count)个方案")
        for (index, plan) in plans.enumerated() {
            print("  方案\(index + 1): '\(plan.title)' - \(plan.attractions.count)个景点")
        }
        
        return Array(plans.prefix(3)) // 最多返回3个方案
    }
    
    // MARK: - AI Attraction Recommendations
    func getAIAttractionRecommendations(
        city: String,
        interests: [String],
        days: Int,
        completion: @escaping ([AttractionRecommendation]) -> Void
    ) {
        DeepSeekAPIService.shared.getAttractionRecommendations(
            city: city,
            interests: interests,
            days: days
        ) { result in
            switch result {
            case .success(let recommendations):
                completion(recommendations)
            case .failure(let error):
                print("获取AI景点推荐失败: \(error.localizedDescription)")
                completion([])
            }
        }
    }
    
    // MARK: - AI Route Optimization
    func getAIRouteOptimization(
        attractions: [String],
        startLocation: String,
        completion: @escaping (RouteOptimization?) -> Void
    ) {
        DeepSeekAPIService.shared.optimizeRoute(
            attractions: attractions,
            startLocation: startLocation
        ) { result in
            switch result {
            case .success(let optimization):
                completion(optimization)
            case .failure(let error):
                print("获取AI路线优化失败: \(error.localizedDescription)")
                completion(nil)
            }
        }
    }
} 